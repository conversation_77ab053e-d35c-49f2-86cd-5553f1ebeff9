# OpenMV矩形检测与MSPM0G3507通信指南

## 📋 功能概述

这个系统实现了OpenMV检测矩形并将矩形中心与图像中心的偏移量发送给MSPM0G3507的功能。

### 主要特性：
- 灰度图像矩形检测
- 平滑滤波减少抖动
- 实时偏移量计算
- UART通信传输数据
- TFT屏幕显示检测结果

## 🔧 硬件连接

```
OpenMV ←→ MSPM0G3507
P4 (TX) ←→ PA9 (UART1_RX)
P5 (RX) ←→ PA8 (UART1_TX)
GND     ←→ GND
VCC     ←→ 3.3V
```

## 📡 通信协议

### 数据帧格式
```
[0x2B, 0x11, data0, data1, data2, data3, 0x5A, 0x59]
```

| 字节 | 内容 | 说明 |
|------|------|------|
| 0-1 | 0x2B, 0x11 | 帧头 |
| 2 | data0 | 检测标志 (0=未检测到, 1=检测到矩形) |
| 3 | data1 | X轴偏移量 (有符号，-128到127) |
| 4 | data2 | Y轴偏移量 (有符号，-128到127) |
| 5 | data3 | 保留字节 (设为0) |
| 6-7 | 0x5A, 0x59 | 帧尾 |

### 偏移量说明
- **X轴偏移量**: 
  - 负值 = 矩形在图像中心左侧
  - 正值 = 矩形在图像中心右侧
  - 0 = 矩形在X轴中心
- **Y轴偏移量**: 
  - 负值 = 矩形在图像中心上方
  - 正值 = 矩形在图像中心下方
  - 0 = 矩形在Y轴中心

## 💻 OpenMV代码说明

### 主要功能模块：

1. **图像处理**：
   - 灰度图像采集
   - 二值化处理
   - 矩形检测

2. **平滑滤波**：
   - 指数移动平均滤波
   - 减少检测抖动
   - 提高稳定性

3. **偏移量计算**：
   - 矩形中心坐标计算
   - 与图像中心比较
   - 偏移量范围限制

4. **数据传输**：
   - UART1通信
   - 9600波特率
   - 20Hz发送频率

### 关键参数：
- `binary_threshold = (100, 255)`: 二值化阈值
- `smoothing_alpha = 0.8`: 平滑系数
- `threshold = 15000`: 矩形检测阈值
- 最小矩形面积: 400像素

## 🖥️ MSPM0G3507显示内容

### TFT屏幕显示：
```
OpenMV Rect Data:
Rect: FOUND
X Offset: -25
Y Offset: 15
X-Dir: LEFT
Y-Dir: DOWN
```

### 显示状态：
- **检测状态**: FOUND / NOT FOUND
- **偏移量**: 数值显示
- **方向指示**: LEFT/RIGHT/CENTER, UP/DOWN/CENTER

## 🚀 使用步骤

### 1. 准备工作
- 确保硬件连接正确
- 检查波特率设置(9600)
- 准备检测目标(矩形物体)

### 2. 代码部署
- 将 `openmv_rectangle_detection.py` 烧录到OpenMV
- 编译并烧录MSPM0G3507代码

### 3. 测试验证
- 先使用 `openmv_rect_test.py` 测试通信
- 观察TFT屏幕显示是否正常
- 确认数据更新频率

### 4. 实际应用
- 将矩形物体放在摄像头前
- 观察检测效果和偏移量显示
- 根据需要调整参数

## ⚙️ 参数调整指南

### OpenMV参数调整：

1. **二值化阈值** (`binary_threshold`):
   - 根据光照条件调整
   - 值越小检测越敏感
   - 建议范围: (80, 255) ~ (120, 255)

2. **平滑系数** (`smoothing_alpha`):
   - 0.1-0.3: 强平滑，响应慢
   - 0.5-0.7: 中等平滑
   - 0.8-0.9: 弱平滑，响应快

3. **矩形检测阈值** (`threshold`):
   - 值越大检测越严格
   - 建议范围: 10000-20000

4. **最小面积** (400像素):
   - 过滤小噪声
   - 根据目标大小调整

### MSPM0参数调整：

1. **超时时间** (500ms):
   - 通信异常检测
   - 可根据需要调整

2. **显示刷新** (100ms):
   - 屏幕更新频率
   - 平衡性能和效果

## 🔍 调试方法

### 1. 通信调试
- 使用测试程序验证基础通信
- 检查串口连接和波特率
- 观察调试输出信息

### 2. 检测调试
- 调整光照条件
- 修改二值化阈值
- 观察矩形检测效果

### 3. 显示调试
- 检查TFT屏幕显示
- 验证数据更新频率
- 确认偏移量计算正确

## 📝 注意事项

1. **光照条件**: 确保良好的光照环境
2. **目标对比度**: 矩形与背景要有足够对比度
3. **检测距离**: 保持适当的检测距离
4. **稳定性**: 避免剧烈震动影响检测
5. **参数调整**: 根据实际环境调整参数

## 🛠️ 故障排除

### 问题1: 屏幕显示"NO DATA"
- 检查UART连接
- 确认波特率匹配
- 验证OpenMV是否正常运行

### 问题2: 检测不到矩形
- 调整二值化阈值
- 改善光照条件
- 检查目标对比度

### 问题3: 检测抖动严重
- 增加平滑系数
- 提高检测阈值
- 改善环境稳定性

### 问题4: 偏移量不准确
- 校准摄像头位置
- 检查计算公式
- 验证坐标系定义
