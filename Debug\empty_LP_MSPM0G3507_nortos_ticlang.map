******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Thu Jul 31 11:05:16 2025

OUTPUT FILE NAME:   <empty_LP_MSPM0G3507_nortos_ticlang.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00003c59


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00008000  00004b40  000034c0  R  X
  SRAM                  20200000   00004000  00000467  00003b99  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00004b40   00004b40    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00004330   00004330    r-x .text
  000043f0    000043f0    00000708   00000708    r-- .rodata
  00004af8    00004af8    00000048   00000048    r-- .cinit
20200000    20200000    00000268   00000000    rw-
  20200000    20200000    0000020f   00000000    rw- .bss
  20200210    20200210    00000058   00000000    rw- .data
20203e00    20203e00    00000200   00000000    rw-
  20203e00    20203e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00004330     
                  000000c0    00000634     quaternion.o (.text.icmAHRSupdate)
                  000006f4    00000364     libc.a : e_asin.c.obj (.text.asin)
                  00000a58    000002f8            : s_atan.c.obj (.text.atan)
                  00000d50    00000288     tft180.o (.text.tft180_init)
                  00000fd8    00000210     quaternion.o (.text.imu660rb_euler_show)
                  000011e8    0000020c     encoder.o (.text.encoder_exti_callback)
                  000013f4    000001ec     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000015e0    000001cc     quaternion.o (.text.icmGetValues)
                  000017ac    000001bc     tft180.o (.text.func_float_to_str)
                  00001968    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00001afa    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00001afc    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  00001c84    00000170            : e_sqrt.c.obj (.text.sqrt)
                  00001df4    00000130     tft180.o (.text.tft180_show_char_color)
                  00001f24    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00002030    0000010c     tft180.o (.text.tft180_show_num_color)
                  0000213c    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00002240    000000f0     openmv.o (.text.UART3_IRQHandler)
                  00002330    000000f0     quaternion.o (.text.gyroOffsetInit)
                  00002420    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  00002508    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000025ec    000000d8                            : addsf3.S.obj (.text)
                  000026c4    000000d4     imu660rb.o (.text.IMU660RB_Init)
                  00002798    000000d4     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  0000286c    0000009c     imu660rb.o (.text.Read_IMU660RB)
                  00002908    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_6_init)
                  00002994    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_7_init)
                  00002a20    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00002aac    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00002b30    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00002bac    00000078     empty.o (.text.main)
                  00002c24    00000078     tft180.o (.text.tft180_clear_color)
                  00002c9c    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00002d10    00000074                            : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00002d84    00000074     delay.o (.text.delay_us)
                  00002df8    0000006c     tft180.o (.text.tft180_set_region)
                  00002e64    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00002ecc    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00002f34    00000062                            : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00002f96    00000002     empty.o (.text.timerB_callback)
                  00002f98    00000062     libclang_rt.builtins.a : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00002ffa    00000062     tft180.o (.text.tft180_show_string_color)
                  0000305c    00000060     imu660rb.o (.text.platform_read)
                  000030bc    00000058     quaternion.o (.text.invSqrt)
                  00003114    00000058     imu660rb.o (.text.platform_write)
                  0000316c    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  000031c2    00000056     lsm6dsr_reg.o (.text.lsm6dsr_acceleration_raw_get)
                  00003218    00000056     lsm6dsr_reg.o (.text.lsm6dsr_angular_rate_raw_get)
                  0000326e    00000002     --HOLE-- [fill = 0]
                  00003270    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_1_init)
                  000032c4    00000050     imu660rb.o (.text.spiTransferByte)
                  00003314    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00003360    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  000033a8    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  000033f0    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_3_init)
                  00003438    00000048     lsm6dsr_reg.o (.text.lsm6dsr_gy_filter_lp1_set)
                  00003480    00000046     lsm6dsr_reg.o (.text.lsm6dsr_i3c_disable_set)
                  000034c6    00000046     lsm6dsr_reg.o (.text.lsm6dsr_reset_set)
                  0000350c    00000044     driverlib.a : dl_spi.o (.text.DL_SPI_init)
                  00003550    00000044     lsm6dsr_reg.o (.text.lsm6dsr_block_data_update_set)
                  00003594    00000044     lsm6dsr_reg.o (.text.lsm6dsr_data_ready_mode_set)
                  000035d8    00000044     lsm6dsr_reg.o (.text.lsm6dsr_gy_data_rate_set)
                  0000361c    00000044     lsm6dsr_reg.o (.text.lsm6dsr_gy_full_scale_set)
                  00003660    00000044     lsm6dsr_reg.o (.text.lsm6dsr_xl_data_rate_set)
                  000036a4    00000044     lsm6dsr_reg.o (.text.lsm6dsr_xl_full_scale_set)
                  000036e8    00000044     tft180.o (.text.tft180_write_16bit_data)
                  0000372c    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_SPI_IMU660RB_init)
                  0000376c    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_TFT_SPI_init)
                  000037ac    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_8_init)
                  000037ec    00000040     libclang_rt.builtins.a : extendsfdf2.S.obj (.text.__extendsfdf2)
                  0000382c    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00003868    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_12_init)
                  000038a4    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  000038e0    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  0000391c    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00003958    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00003992    00000002     --HOLE-- [fill = 0]
                  00003994    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  000039ce    00000002     --HOLE-- [fill = 0]
                  000039d0    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  00003a08    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00003a3c    00000032     openmv.o (.text.get_verify_code)
                  00003a6e    00000002     --HOLE-- [fill = 0]
                  00003a70    00000030     ti_msp_dl_config.o (.text.DL_SPI_setFIFOThreshold)
                  00003aa0    00000030     tft180.o (.text.tft180_write_index)
                  00003ad0    0000002e     lsm6dsr_reg.o (.text.lsm6dsr_reset_get)
                  00003afe    0000002e     lsm6dsr_reg.o (.text.read_reg)
                  00003b2c    0000002e     lsm6dsr_reg.o (.text.write_reg)
                  00003b5a    00000002     --HOLE-- [fill = 0]
                  00003b5c    0000002c     timer.o (.text.__NVIC_ClearPendingIRQ)
                  00003b88    0000002c     timer.o (.text.__NVIC_EnableIRQ)
                  00003bb4    0000002c     tft180.o (.text.tft180_write_8bit_data)
                  00003be0    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00003c08    00000028     ti_msp_dl_config.o (.text.DL_SYSTICK_init)
                  00003c30    00000028     debug.o (.text.UART0_IRQHandler)
                  00003c58    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00003c80    00000024     ti_msp_dl_config.o (.text.DL_SPI_setBitRateSerialClockDivider)
                  00003ca4    00000022     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00003cc6    00000002     --HOLE-- [fill = 0]
                  00003cc8    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00003ce8    00000020     lsm6dsr_reg.o (.text.lsm6dsr_from_fs2000dps_to_mdps)
                  00003d08    00000020     lsm6dsr_reg.o (.text.lsm6dsr_from_fs2g_to_mg)
                  00003d28    00000020     openmv.o (.text.openmv_analysis)
                  00003d48    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00003d66    00000002     --HOLE-- [fill = 0]
                  00003d68    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  00003d84    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00003da0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00003dbc    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00003dd8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  00003df4    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00003e10    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  00003e2c    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00003e48    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00003e64    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00003e80    0000001c     timer.o (.text.TIMG12_IRQHandler)
                  00003e9c    0000001c     timer.o (.text.TIMG8_IRQHandler)
                  00003eb8    0000001c     lsm6dsr_reg.o (.text.lsm6dsr_device_id_get)
                  00003ed4    0000001c     lsm6dsr_reg.o (.text.lsm6dsr_pin_int1_route_get)
                  00003ef0    0000001c     lsm6dsr_reg.o (.text.lsm6dsr_pin_int1_route_set)
                  00003f0c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00003f24    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00003f3c    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00003f54    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00003f6c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00003f84    00000018     imu660rb.o (.text.DL_GPIO_setPins)
                  00003f9c    00000018     tft180.o (.text.DL_GPIO_setPins)
                  00003fb4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00003fcc    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00003fe4    00000018     ti_msp_dl_config.o (.text.DL_SPI_enable)
                  00003ffc    00000018     ti_msp_dl_config.o (.text.DL_SPI_enablePower)
                  00004014    00000018     imu660rb.o (.text.DL_SPI_isBusy)
                  0000402c    00000018     tft180.o (.text.DL_SPI_isBusy)
                  00004044    00000018     imu660rb.o (.text.DL_SPI_isRXFIFOEmpty)
                  0000405c    00000018     ti_msp_dl_config.o (.text.DL_SPI_reset)
                  00004074    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  0000408c    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  000040a4    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000040bc    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  000040d4    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  000040ec    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00004104    00000016     encoder.o (.text.DL_GPIO_readPins)
                  0000411a    00000016     imu660rb.o (.text.DL_SPI_transmitData8)
                  00004130    00000016     tft180.o (.text.DL_SPI_transmitData8)
                  00004146    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  0000415c    00000016     delay.o (.text.delay_ms)
                  00004172    00000016     timer.o (.text.timerA_init)
                  00004188    00000016     timer.o (.text.timerB_init)
                  0000419e    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  000041b4    00000014     imu660rb.o (.text.DL_GPIO_clearPins)
                  000041c8    00000014     tft180.o (.text.DL_GPIO_clearPins)
                  000041dc    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  000041f0    00000014     imu660rb.o (.text.DL_SPI_receiveData8)
                  00004204    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_enableMFCLK)
                  00004218    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  0000422c    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00004240    00000014     debug.o (.text.DL_UART_receiveData)
                  00004254    00000014     openmv.o (.text.DL_UART_receiveData)
                  00004268    00000012     driverlib.a : dl_spi.o (.text.DL_SPI_setClockConfig)
                  0000427a    00000012     timer.o (.text.DL_Timer_getPendingInterrupt)
                  0000428c    00000012     debug.o (.text.DL_UART_getPendingInterrupt)
                  0000429e    00000012     openmv.o (.text.DL_UART_getPendingInterrupt)
                  000042b0    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  000042c2    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  000042d4    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  000042e6    00000002     --HOLE-- [fill = 0]
                  000042e8    00000010     ti_msp_dl_config.o (.text.DL_SYSTICK_enable)
                  000042f8    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00004308    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00004318    00000010     imu660rb.o (.text.platform_delay)
                  00004328    00000010     empty.o (.text.timerA_callback)
                  00004338    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00004342    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  0000434c    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  0000435c    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  00004366    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  00004370    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  0000437a    00000002     --HOLE-- [fill = 0]
                  0000437c    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  0000438c    00000008     empty.o (.text.GROUP1_IRQHandler)
                  00004394    00000008     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  0000439c    00000008            : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  000043a4    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000043ac    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  000043b2    00000002     --HOLE-- [fill = 0]
                  000043b4    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  000043c4    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  000043ca    00000006            : exit.c.obj (.text:abort)
                  000043d0    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  000043d4    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000043d8    00000004     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  000043dc    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000043e0    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  000043e4    0000000c     --HOLE-- [fill = 0]

.cinit     0    00004af8    00000048     
                  00004af8    0000001f     (.cinit..data.load) [load image, compression = lzss]
                  00004b17    00000001     --HOLE-- [fill = 0]
                  00004b18    0000000c     (__TI_handler_table)
                  00004b24    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00004b2c    00000010     (__TI_cinit_table)
                  00004b3c    00000004     --HOLE-- [fill = 0]

.rodata    0    000043f0    00000708     
                  000043f0    000005f0     tft180.o (.rodata.ascii_font_8x16)
                  000049e0    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  00004a20    00000014     ti_msp_dl_config.o (.rodata.gTIMER_12TimerConfig)
                  00004a34    00000014     ti_msp_dl_config.o (.rodata.gTIMER_8TimerConfig)
                  00004a48    00000014     empty.o (.rodata.str1.4000995719088696555.1)
                  00004a5c    00000014     empty.o (.rodata.str1.5792233746214848027.1)
                  00004a70    0000000a     ti_msp_dl_config.o (.rodata.gSPI_IMU660RB_config)
                  00004a7a    0000000a     ti_msp_dl_config.o (.rodata.gTFT_SPI_config)
                  00004a84    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00004a8e    0000000a     ti_msp_dl_config.o (.rodata.gUART_1Config)
                  00004a98    0000000a     ti_msp_dl_config.o (.rodata.gUART_3Config)
                  00004aa2    00000002     ti_msp_dl_config.o (.rodata.gSPI_IMU660RB_clockConfig)
                  00004aa4    00000008     ti_msp_dl_config.o (.rodata.gPWM_6Config)
                  00004aac    00000008     ti_msp_dl_config.o (.rodata.gPWM_7Config)
                  00004ab4    00000007     quaternion.o (.rodata.str1.9250186920080173868.1)
                  00004abb    00000006     quaternion.o (.rodata.str1.890747907189039915.1)
                  00004ac1    00000005     quaternion.o (.rodata.str1.13639597417912095219.1)
                  00004ac6    00000004     quaternion.o (.rodata.str1.15426990692512839315.1)
                  00004aca    00000004     quaternion.o (.rodata.str1.4468864501413443855.1)
                  00004ace    00000004     quaternion.o (.rodata.str1.5489345051456202007.1)
                  00004ad2    00000004     quaternion.o (.rodata.str1.5650737834519794129.1)
                  00004ad6    00000004     quaternion.o (.rodata.str1.8418470146157308009.1)
                  00004ada    00000004     quaternion.o (.rodata.str1.9323597929406665958.1)
                  00004ade    00000003     ti_msp_dl_config.o (.rodata.gPWM_6ClockConfig)
                  00004ae1    00000003     ti_msp_dl_config.o (.rodata.gPWM_7ClockConfig)
                  00004ae4    00000003     ti_msp_dl_config.o (.rodata.gTIMER_12ClockConfig)
                  00004ae7    00000003     ti_msp_dl_config.o (.rodata.gTIMER_8ClockConfig)
                  00004aea    00000002     ti_msp_dl_config.o (.rodata.gTFT_SPI_clockConfig)
                  00004aec    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00004aee    00000002     ti_msp_dl_config.o (.rodata.gUART_1ClockConfig)
                  00004af0    00000002     ti_msp_dl_config.o (.rodata.gUART_3ClockConfig)
                  00004af2    00000006     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    0000020f     UNINITIALIZED
                  20200000    000000a0     (.common:gPWM_6Backup)
                  202000a0    000000a0     (.common:gPWM_7Backup)
                  20200140    00000030     (.common:gUART_3Backup)
                  20200170    00000028     (.common:gSPI_IMU660RBBackup)
                  20200198    00000028     (.common:gTFT_SPIBackup)
                  202001c0    00000010     imu660rb.o (.bss.dev_ctx)
                  202001d0    0000000c     (.common:acceleration_mg)
                  202001dc    0000000c     (.common:angular_rate_mdps)
                  202001e8    00000008     openmv.o (.bss.rx_buffer)
                  202001f0    00000006     imu660rb.o (.bss.data_raw_acceleration)
                  202001f6    00000006     imu660rb.o (.bss.data_raw_angular_rate)
                  202001fc    00000004     (.common:I_ex)
                  20200200    00000004     (.common:I_ey)
                  20200204    00000004     (.common:I_ez)
                  20200208    00000004     (.common:openmvData)
                  2020020c    00000001     openmv.o (.bss.data)
                  2020020d    00000001     imu660rb.o (.bss.rst)
                  2020020e    00000001     imu660rb.o (.bss.whoamI)

.data      0    20200210    00000058     UNINITIALIZED
                  20200210    00000004     quaternion.o (.data.Angle_ax)
                  20200214    00000004     quaternion.o (.data.Angle_ay)
                  20200218    00000004     quaternion.o (.data.Angle_az)
                  2020021c    00000004     quaternion.o (.data.Angle_gx)
                  20200220    00000004     quaternion.o (.data.Angle_gy)
                  20200224    00000004     quaternion.o (.data.Angle_gz)
                  20200228    00000004     quaternion.o (.data.Q_info_q0)
                  2020022c    00000004     quaternion.o (.data.Q_info_q1)
                  20200230    00000004     quaternion.o (.data.Q_info_q2)
                  20200234    00000004     quaternion.o (.data.Q_info_q3)
                  20200238    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  2020023c    00000004     quaternion.o (.data.eulerAngle_pitch)
                  20200240    00000004     quaternion.o (.data.eulerAngle_roll)
                  20200244    00000004     quaternion.o (.data.eulerAngle_yaw)
                  20200248    00000004     quaternion.o (.data.gx_offset)
                  2020024c    00000004     quaternion.o (.data.gy_offset)
                  20200250    00000004     quaternion.o (.data.gz_offset)
                  20200254    00000004     quaternion.o (.data.icm_ki)
                  20200258    00000004     quaternion.o (.data.icm_kp)
                  2020025c    00000002     encoder.o (.data.left_counter)
                  2020025e    00000002     encoder.o (.data.right_counter)
                  20200260    00000002     tft180.o (.data.tft180_bgcolor)
                  20200262    00000001     quaternion.o (.data.imu660rb_euler_show.first_display)
                  20200263    00000001     openmv.o (.data.n)
                  20200264    00000001     openmv.o (.data.state)
                  20200265    00000001     tft180.o (.data.tft180_x_max)
                  20200266    00000001     tft180.o (.data.tft180_y_max)
                  20200267    00000001     debug.o (.data.uart_data)

.stack     0    20203e00    00000200     UNINITIALIZED
                  20203e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20203e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             2814    128       448    
       startup_mspm0g350x_ticlang.o   8       192       0      
       empty.o                        146     40        0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2968    360       448    
                                                               
    .\drivers\
       tft180.o                       2240    1520      4      
       quaternion.o                   2904    42        85     
       lsm6dsr_reg.o                  1078    0         0      
       imu660rb.o                     782     0         54     
       encoder.o                      598     0         4      
       openmv.o                       360     0         15     
       timer.o                        206     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         8168    1562      162    
                                                               
    .\soft\
       delay.o                        138     0         0      
       debug.o                        78      0         1      
    +--+------------------------------+-------+---------+---------+
       Total:                         216     0         1      
                                                               
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     588     0         0      
       dl_uart.o                      90      0         0      
       dl_spi.o                       86      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         774     0         0      
                                                               
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       e_asin.c.obj                   908     0         0      
       s_atan.c.obj                   784     64        0      
       e_atan2.c.obj                  392     0         0      
       e_sqrt.c.obj                   368     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            40      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2752    64        4      
                                                               
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   284     0         0      
       muldf3.S.obj                   244     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       mulsf3.S.obj                   140     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               2       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2286    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       67        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   17168   2053      1127   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00004b2c records: 2, size/record: 8, table size: 16
	.data: load addr=00004af8, load size=0000001f bytes, run addr=20200210, run size=00000058 bytes, compression=lzss
	.bss: load addr=00004b24, load size=00000008 bytes, run addr=20200000, run size=0000020f bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00004b18 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   00002509     0000434c     0000434a   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             00004364          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             0000436e          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             0000439a          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             000043c8          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   00001f25     0000437c     00004378   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   00001973     000043b4     000043b0   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             000043da          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)

[3 trampolines]
[8 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                            
-------   ----                            
000043d1  ADC0_IRQHandler                 
000043d1  ADC1_IRQHandler                 
000043d1  AES_IRQHandler                  
20200210  Angle_ax                        
20200214  Angle_ay                        
20200218  Angle_az                        
2020021c  Angle_gx                        
20200220  Angle_gy                        
20200224  Angle_gz                        
000043d4  C$$EXIT                         
000043d1  CANFD0_IRQHandler               
000043d1  DAC0_IRQHandler                 
00004339  DL_Common_delayCycles           
0000350d  DL_SPI_init                     
00004269  DL_SPI_setClockConfig           
0000213d  DL_Timer_initFourCCPWMMode      
00002421  DL_Timer_initTimerMode          
00003e2d  DL_Timer_setCaptCompUpdateMethod
000040bd  DL_Timer_setCaptureCompareOutCtl
000042f9  DL_Timer_setCaptureCompareValue 
00003e49  DL_Timer_setClockConfig         
00003361  DL_UART_init                    
000042b1  DL_UART_setClockConfig          
000043d1  DMA_IRQHandler                  
000043d1  Default_Handler                 
000043d1  GROUP0_IRQHandler               
0000438d  GROUP1_IRQHandler               
000043d5  HOSTexit                        
000043d1  HardFault_Handler               
000043d1  I2C0_IRQHandler                 
000043d1  I2C1_IRQHandler                 
000026c5  IMU660RB_Init                   
202001fc  I_ex                            
20200200  I_ey                            
20200204  I_ez                            
000043d1  NMI_Handler                     
000043d1  PendSV_Handler                  
20200228  Q_info_q0                       
2020022c  Q_info_q1                       
20200230  Q_info_q2                       
20200234  Q_info_q3                       
000043d1  RTC_IRQHandler                  
0000286d  Read_IMU660RB                   
000043dd  Reset_Handler                   
000043d1  SPI0_IRQHandler                 
000043d1  SPI1_IRQHandler                 
000043d1  SVC_Handler                     
000013f5  SYSCFG_DL_GPIO_init             
00002909  SYSCFG_DL_PWM_6_init            
00002995  SYSCFG_DL_PWM_7_init            
0000372d  SYSCFG_DL_SPI_IMU660RB_init     
00003ca5  SYSCFG_DL_SYSCTL_init           
00004309  SYSCFG_DL_SYSTICK_init          
0000376d  SYSCFG_DL_TFT_SPI_init          
00003869  SYSCFG_DL_TIMER_12_init         
000037ad  SYSCFG_DL_TIMER_8_init          
000033a9  SYSCFG_DL_UART_0_init           
00003271  SYSCFG_DL_UART_1_init           
000033f1  SYSCFG_DL_UART_3_init           
00002e65  SYSCFG_DL_init                  
00002799  SYSCFG_DL_initPower             
000043d1  SysTick_Handler                 
000043d1  TIMA0_IRQHandler                
000043d1  TIMA1_IRQHandler                
000043d1  TIMG0_IRQHandler                
00003e81  TIMG12_IRQHandler               
000043d1  TIMG6_IRQHandler                
000043d1  TIMG7_IRQHandler                
00003e9d  TIMG8_IRQHandler                
000042c3  TI_memcpy_small                 
00003c31  UART0_IRQHandler                
000043d1  UART1_IRQHandler                
000043d1  UART2_IRQHandler                
00002241  UART3_IRQHandler                
20204000  __STACK_END                     
00000200  __STACK_SIZE                    
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00004b2c  __TI_CINIT_Base                 
00004b3c  __TI_CINIT_Limit                
00004b3c  __TI_CINIT_Warm                 
00004b18  __TI_Handler_Table_Base         
00004b24  __TI_Handler_Table_Limit        
0000391d  __TI_auto_init_nobinit_nopinit  
00002b31  __TI_decompress_lzss            
000042d5  __TI_decompress_none            
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
00000000  __TI_static_base__              
0000419f  __TI_zero_init_nomemset         
00001973  __adddf3                        
000025f7  __addsf3                        
00002d11  __aeabi_d2f                     
00001973  __aeabi_dadd                    
00002f35  __aeabi_dcmpeq                  
00002f71  __aeabi_dcmpge                  
00002f85  __aeabi_dcmpgt                  
00002f5d  __aeabi_dcmple                  
00002f49  __aeabi_dcmplt                  
00001f25  __aeabi_ddiv                    
00002509  __aeabi_dmul                    
00001969  __aeabi_dsub                    
20200238  __aeabi_errno                   
0000439d  __aeabi_errno_addr              
000037ed  __aeabi_f2d                     
000039d1  __aeabi_f2iz                    
000025f7  __aeabi_fadd                    
00002f99  __aeabi_fcmpeq                  
00002fd5  __aeabi_fcmpge                  
00002fe9  __aeabi_fcmpgt                  
00002fc1  __aeabi_fcmple                  
00002fad  __aeabi_fcmplt                  
00002a21  __aeabi_fmul                    
000025ed  __aeabi_fsub                    
000038a5  __aeabi_i2f                     
0000316d  __aeabi_idiv                    
00001afb  __aeabi_idiv0                   
0000316d  __aeabi_idivmod                 
000043a5  __aeabi_memcpy                  
000043a5  __aeabi_memcpy4                 
000043a5  __aeabi_memcpy8                 
ffffffff  __binit__                       
00002ecd  __cmpdf2                        
00003959  __cmpsf2                        
00001f25  __divdf3                        
00002ecd  __eqdf2                         
00003959  __eqsf2                         
000037ed  __extendsfdf2                   
000039d1  __fixsfsi                       
000038a5  __floatsisf                     
00002c9d  __gedf2                         
000038e1  __gesf2                         
00002c9d  __gtdf2                         
000038e1  __gtsf2                         
00002ecd  __ledf2                         
00003959  __lesf2                         
00002ecd  __ltdf2                         
00003959  __ltsf2                         
UNDEFED   __mpu_init                      
00002509  __muldf3                        
00003995  __muldsi3                       
00002a21  __mulsf3                        
00002ecd  __nedf2                         
00003959  __nesf2                         
20203e00  __stack                         
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
00001969  __subdf3                        
000025ed  __subsf3                        
00002d11  __truncdfsf2                    
00003c59  _c_int00_noargs                 
UNDEFED   _system_post_cinit              
000043e1  _system_pre_init                
000043cb  abort                           
202001d0  acceleration_mg                 
202001dc  angular_rate_mdps               
000043f0  ascii_font_8x16                 
000006f5  asin                            
000006f5  asinl                           
00000a59  atan                            
00001afd  atan2                           
00001afd  atan2l                          
00000a59  atanl                           
ffffffff  binit                           
0000415d  delay_ms                        
00002d85  delay_us                        
000011e9  encoder_exti_callback           
2020023c  eulerAngle_pitch                
20200240  eulerAngle_roll                 
20200244  eulerAngle_yaw                  
000017ad  func_float_to_str               
20200000  gPWM_6Backup                    
202000a0  gPWM_7Backup                    
20200170  gSPI_IMU660RBBackup             
20200198  gTFT_SPIBackup                  
20200140  gUART_3Backup                   
20200248  gx_offset                       
2020024c  gy_offset                       
00002331  gyroOffsetInit                  
20200250  gz_offset                       
000000c1  icmAHRSupdate                   
000015e1  icmGetValues                    
20200254  icm_ki                          
20200258  icm_kp                          
00000fd9  imu660rb_euler_show             
00000000  interruptVectors                
000030bd  invSqrt                         
2020025c  left_counter                    
000031c3  lsm6dsr_acceleration_raw_get    
00003219  lsm6dsr_angular_rate_raw_get    
00003551  lsm6dsr_block_data_update_set   
00003595  lsm6dsr_data_ready_mode_set     
00003eb9  lsm6dsr_device_id_get           
00003ce9  lsm6dsr_from_fs2000dps_to_mdps  
00003d09  lsm6dsr_from_fs2g_to_mg         
000035d9  lsm6dsr_gy_data_rate_set        
00003439  lsm6dsr_gy_filter_lp1_set       
0000361d  lsm6dsr_gy_full_scale_set       
00003481  lsm6dsr_i3c_disable_set         
00003ed5  lsm6dsr_pin_int1_route_get      
00003ef1  lsm6dsr_pin_int1_route_set      
00003ad1  lsm6dsr_reset_get               
000034c7  lsm6dsr_reset_set               
00003661  lsm6dsr_xl_data_rate_set        
000036a5  lsm6dsr_xl_full_scale_set       
00002bad  main                            
20200208  openmvData                      
00003d29  openmv_analysis                 
2020025e  right_counter                   
00001c85  sqrt                            
00001c85  sqrtl                           
00002c25  tft180_clear_color              
00000d51  tft180_init                     
00001df5  tft180_show_char_color          
00002031  tft180_show_num_color           
00002ffb  tft180_show_string_color        
000036e9  tft180_write_16bit_data         
00003bb5  tft180_write_8bit_data          
00004329  timerA_callback                 
00004173  timerA_init                     
00002f97  timerB_callback                 
00004189  timerB_init                     
20200267  uart_data                       


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                            
-------   ----                            
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00000000  __TI_static_base__              
00000000  interruptVectors                
000000c1  icmAHRSupdate                   
00000200  __STACK_SIZE                    
000006f5  asin                            
000006f5  asinl                           
00000a59  atan                            
00000a59  atanl                           
00000d51  tft180_init                     
00000fd9  imu660rb_euler_show             
000011e9  encoder_exti_callback           
000013f5  SYSCFG_DL_GPIO_init             
000015e1  icmGetValues                    
000017ad  func_float_to_str               
00001969  __aeabi_dsub                    
00001969  __subdf3                        
00001973  __adddf3                        
00001973  __aeabi_dadd                    
00001afb  __aeabi_idiv0                   
00001afd  atan2                           
00001afd  atan2l                          
00001c85  sqrt                            
00001c85  sqrtl                           
00001df5  tft180_show_char_color          
00001f25  __aeabi_ddiv                    
00001f25  __divdf3                        
00002031  tft180_show_num_color           
0000213d  DL_Timer_initFourCCPWMMode      
00002241  UART3_IRQHandler                
00002331  gyroOffsetInit                  
00002421  DL_Timer_initTimerMode          
00002509  __aeabi_dmul                    
00002509  __muldf3                        
000025ed  __aeabi_fsub                    
000025ed  __subsf3                        
000025f7  __addsf3                        
000025f7  __aeabi_fadd                    
000026c5  IMU660RB_Init                   
00002799  SYSCFG_DL_initPower             
0000286d  Read_IMU660RB                   
00002909  SYSCFG_DL_PWM_6_init            
00002995  SYSCFG_DL_PWM_7_init            
00002a21  __aeabi_fmul                    
00002a21  __mulsf3                        
00002b31  __TI_decompress_lzss            
00002bad  main                            
00002c25  tft180_clear_color              
00002c9d  __gedf2                         
00002c9d  __gtdf2                         
00002d11  __aeabi_d2f                     
00002d11  __truncdfsf2                    
00002d85  delay_us                        
00002e65  SYSCFG_DL_init                  
00002ecd  __cmpdf2                        
00002ecd  __eqdf2                         
00002ecd  __ledf2                         
00002ecd  __ltdf2                         
00002ecd  __nedf2                         
00002f35  __aeabi_dcmpeq                  
00002f49  __aeabi_dcmplt                  
00002f5d  __aeabi_dcmple                  
00002f71  __aeabi_dcmpge                  
00002f85  __aeabi_dcmpgt                  
00002f97  timerB_callback                 
00002f99  __aeabi_fcmpeq                  
00002fad  __aeabi_fcmplt                  
00002fc1  __aeabi_fcmple                  
00002fd5  __aeabi_fcmpge                  
00002fe9  __aeabi_fcmpgt                  
00002ffb  tft180_show_string_color        
000030bd  invSqrt                         
0000316d  __aeabi_idiv                    
0000316d  __aeabi_idivmod                 
000031c3  lsm6dsr_acceleration_raw_get    
00003219  lsm6dsr_angular_rate_raw_get    
00003271  SYSCFG_DL_UART_1_init           
00003361  DL_UART_init                    
000033a9  SYSCFG_DL_UART_0_init           
000033f1  SYSCFG_DL_UART_3_init           
00003439  lsm6dsr_gy_filter_lp1_set       
00003481  lsm6dsr_i3c_disable_set         
000034c7  lsm6dsr_reset_set               
0000350d  DL_SPI_init                     
00003551  lsm6dsr_block_data_update_set   
00003595  lsm6dsr_data_ready_mode_set     
000035d9  lsm6dsr_gy_data_rate_set        
0000361d  lsm6dsr_gy_full_scale_set       
00003661  lsm6dsr_xl_data_rate_set        
000036a5  lsm6dsr_xl_full_scale_set       
000036e9  tft180_write_16bit_data         
0000372d  SYSCFG_DL_SPI_IMU660RB_init     
0000376d  SYSCFG_DL_TFT_SPI_init          
000037ad  SYSCFG_DL_TIMER_8_init          
000037ed  __aeabi_f2d                     
000037ed  __extendsfdf2                   
00003869  SYSCFG_DL_TIMER_12_init         
000038a5  __aeabi_i2f                     
000038a5  __floatsisf                     
000038e1  __gesf2                         
000038e1  __gtsf2                         
0000391d  __TI_auto_init_nobinit_nopinit  
00003959  __cmpsf2                        
00003959  __eqsf2                         
00003959  __lesf2                         
00003959  __ltsf2                         
00003959  __nesf2                         
00003995  __muldsi3                       
000039d1  __aeabi_f2iz                    
000039d1  __fixsfsi                       
00003ad1  lsm6dsr_reset_get               
00003bb5  tft180_write_8bit_data          
00003c31  UART0_IRQHandler                
00003c59  _c_int00_noargs                 
00003ca5  SYSCFG_DL_SYSCTL_init           
00003ce9  lsm6dsr_from_fs2000dps_to_mdps  
00003d09  lsm6dsr_from_fs2g_to_mg         
00003d29  openmv_analysis                 
00003e2d  DL_Timer_setCaptCompUpdateMethod
00003e49  DL_Timer_setClockConfig         
00003e81  TIMG12_IRQHandler               
00003e9d  TIMG8_IRQHandler                
00003eb9  lsm6dsr_device_id_get           
00003ed5  lsm6dsr_pin_int1_route_get      
00003ef1  lsm6dsr_pin_int1_route_set      
000040bd  DL_Timer_setCaptureCompareOutCtl
0000415d  delay_ms                        
00004173  timerA_init                     
00004189  timerB_init                     
0000419f  __TI_zero_init_nomemset         
00004269  DL_SPI_setClockConfig           
000042b1  DL_UART_setClockConfig          
000042c3  TI_memcpy_small                 
000042d5  __TI_decompress_none            
000042f9  DL_Timer_setCaptureCompareValue 
00004309  SYSCFG_DL_SYSTICK_init          
00004329  timerA_callback                 
00004339  DL_Common_delayCycles           
0000438d  GROUP1_IRQHandler               
0000439d  __aeabi_errno_addr              
000043a5  __aeabi_memcpy                  
000043a5  __aeabi_memcpy4                 
000043a5  __aeabi_memcpy8                 
000043cb  abort                           
000043d1  ADC0_IRQHandler                 
000043d1  ADC1_IRQHandler                 
000043d1  AES_IRQHandler                  
000043d1  CANFD0_IRQHandler               
000043d1  DAC0_IRQHandler                 
000043d1  DMA_IRQHandler                  
000043d1  Default_Handler                 
000043d1  GROUP0_IRQHandler               
000043d1  HardFault_Handler               
000043d1  I2C0_IRQHandler                 
000043d1  I2C1_IRQHandler                 
000043d1  NMI_Handler                     
000043d1  PendSV_Handler                  
000043d1  RTC_IRQHandler                  
000043d1  SPI0_IRQHandler                 
000043d1  SPI1_IRQHandler                 
000043d1  SVC_Handler                     
000043d1  SysTick_Handler                 
000043d1  TIMA0_IRQHandler                
000043d1  TIMA1_IRQHandler                
000043d1  TIMG0_IRQHandler                
000043d1  TIMG6_IRQHandler                
000043d1  TIMG7_IRQHandler                
000043d1  UART1_IRQHandler                
000043d1  UART2_IRQHandler                
000043d4  C$$EXIT                         
000043d5  HOSTexit                        
000043dd  Reset_Handler                   
000043e1  _system_pre_init                
000043f0  ascii_font_8x16                 
00004b18  __TI_Handler_Table_Base         
00004b24  __TI_Handler_Table_Limit        
00004b2c  __TI_CINIT_Base                 
00004b3c  __TI_CINIT_Limit                
00004b3c  __TI_CINIT_Warm                 
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
20200000  gPWM_6Backup                    
202000a0  gPWM_7Backup                    
20200140  gUART_3Backup                   
20200170  gSPI_IMU660RBBackup             
20200198  gTFT_SPIBackup                  
202001d0  acceleration_mg                 
202001dc  angular_rate_mdps               
202001fc  I_ex                            
20200200  I_ey                            
20200204  I_ez                            
20200208  openmvData                      
20200210  Angle_ax                        
20200214  Angle_ay                        
20200218  Angle_az                        
2020021c  Angle_gx                        
20200220  Angle_gy                        
20200224  Angle_gz                        
20200228  Q_info_q0                       
2020022c  Q_info_q1                       
20200230  Q_info_q2                       
20200234  Q_info_q3                       
20200238  __aeabi_errno                   
2020023c  eulerAngle_pitch                
20200240  eulerAngle_roll                 
20200244  eulerAngle_yaw                  
20200248  gx_offset                       
2020024c  gy_offset                       
20200250  gz_offset                       
20200254  icm_ki                          
20200258  icm_kp                          
2020025c  left_counter                    
2020025e  right_counter                   
20200267  uart_data                       
20203e00  __stack                         
20204000  __STACK_END                     
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
ffffffff  __binit__                       
ffffffff  binit                           
UNDEFED   __mpu_init                      
UNDEFED   _system_post_cinit              

[233 symbols]
