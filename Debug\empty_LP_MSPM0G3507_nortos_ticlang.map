******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Thu Jul 31 17:55:44 2025

OUTPUT FILE NAME:   <empty_LP_MSPM0G3507_nortos_ticlang.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000020a5


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00008000  00002e18  000051e8  R  X
  SRAM                  20200000   00004000  000003e8  00003c18  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00002e18   00002e18    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000025a8   000025a8    r-x .text
  00002668    00002668    00000778   00000778    r-- .rodata
  00002de0    00002de0    00000038   00000038    r-- .cinit
20200000    20200000    000001eb   00000000    rw-
  20200000    20200000    000001d5   00000000    rw- .bss
  202001d8    202001d8    00000013   00000000    rw- .data
20203e00    20203e00    00000200   00000000    rw-
  20203e00    20203e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000025a8     
                  000000c0    00000288     tft180.o (.text.tft180_init)
                  00000348    0000020c     encoder.o (.text.encoder_exti_callback)
                  00000554    00000204     openmv.o (.text.openmv_display_data)
                  00000758    000001ec     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000944    000001bc     tft180.o (.text.func_float_to_str)
                  00000b00    00000130     tft180.o (.text.tft180_show_char_color)
                  00000c30    00000120     openmv.o (.text.UART3_IRQHandler)
                  00000d50    0000010c     tft180.o (.text.tft180_show_num_color)
                  00000e5c    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00000f60    000000e8                 : dl_timer.o (.text.DL_Timer_initTimerMode)
                  00001048    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00001120    000000d4     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  000011f4    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_6_init)
                  00001280    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_7_init)
                  0000130c    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00001398    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  0000141c    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00001498    00000078     tft180.o (.text.tft180_clear_color)
                  00001510    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00001584    00000074     delay.o (.text.delay_us)
                  000015f8    0000006c     tft180.o (.text.tft180_set_region)
                  00001664    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  000016cc    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00001734    00000062                            : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00001796    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00001798    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  000017fa    00000062     tft180.o (.text.tft180_show_string_color)
                  0000185c    00000058     empty.o (.text.main)
                  000018b4    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  0000190a    00000002     empty.o (.text.timerA_callback)
                  0000190c    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_1_init)
                  00001960    00000050     openmv.o (.text.openmv_is_data_valid)
                  000019b0    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  000019fc    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00001a44    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00001a8c    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_3_init)
                  00001ad4    00000044     driverlib.a : dl_spi.o (.text.DL_SPI_init)
                  00001b18    00000044     tft180.o (.text.tft180_write_16bit_data)
                  00001b5c    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_SPI_IMU660RB_init)
                  00001b9c    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_TFT_SPI_init)
                  00001bdc    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_8_init)
                  00001c1c    00000040     libclang_rt.builtins.a : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00001c5c    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00001c98    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_12_init)
                  00001cd4    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00001d10    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00001d4c    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00001d88    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00001dc2    00000002     empty.o (.text.timerB_callback)
                  00001dc4    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  00001dfe    00000002     --HOLE-- [fill = 0]
                  00001e00    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  00001e38    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00001e6c    00000034     openmv.o (.text.openmv_analysis)
                  00001ea0    00000030     ti_msp_dl_config.o (.text.DL_SPI_setFIFOThreshold)
                  00001ed0    00000030     tft180.o (.text.tft180_write_index)
                  00001f00    0000002c     openmv.o (.text.__NVIC_ClearPendingIRQ)
                  00001f2c    0000002c     timer.o (.text.__NVIC_ClearPendingIRQ)
                  00001f58    0000002c     openmv.o (.text.__NVIC_EnableIRQ)
                  00001f84    0000002c     timer.o (.text.__NVIC_EnableIRQ)
                  00001fb0    0000002c     tft180.o (.text.tft180_write_8bit_data)
                  00001fdc    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00002004    00000028     ti_msp_dl_config.o (.text.DL_SYSTICK_init)
                  0000202c    00000028     timer.o (.text.TIMG8_IRQHandler)
                  00002054    00000028     debug.o (.text.UART0_IRQHandler)
                  0000207c    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  000020a4    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000020cc    00000024     ti_msp_dl_config.o (.text.DL_SPI_setBitRateSerialClockDivider)
                  000020f0    00000022     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00002112    00000002     --HOLE-- [fill = 0]
                  00002114    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00002134    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00002152    00000002     --HOLE-- [fill = 0]
                  00002154    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  00002170    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  0000218c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  000021a8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  000021c4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  000021e0    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  000021fc    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  00002218    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00002234    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00002250    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  0000226c    0000001c     timer.o (.text.TIMG12_IRQHandler)
                  00002288    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  000022a0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  000022b8    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  000022d0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  000022e8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00002300    00000018     tft180.o (.text.DL_GPIO_setPins)
                  00002318    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00002330    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00002348    00000018     ti_msp_dl_config.o (.text.DL_SPI_enable)
                  00002360    00000018     ti_msp_dl_config.o (.text.DL_SPI_enablePower)
                  00002378    00000018     tft180.o (.text.DL_SPI_isBusy)
                  00002390    00000018     ti_msp_dl_config.o (.text.DL_SPI_reset)
                  000023a8    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  000023c0    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  000023d8    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000023f0    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00002408    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00002420    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00002438    00000016     encoder.o (.text.DL_GPIO_readPins)
                  0000244e    00000016     tft180.o (.text.DL_SPI_transmitData8)
                  00002464    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  0000247a    00000016     delay.o (.text.delay_ms)
                  00002490    00000016     openmv.o (.text.openmv_init)
                  000024a6    00000016     timer.o (.text.timerA_init)
                  000024bc    00000016     timer.o (.text.timerB_init)
                  000024d2    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  000024e8    00000014     tft180.o (.text.DL_GPIO_clearPins)
                  000024fc    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00002510    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_enableMFCLK)
                  00002524    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00002538    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  0000254c    00000014     debug.o (.text.DL_UART_receiveData)
                  00002560    00000014     openmv.o (.text.DL_UART_receiveData)
                  00002574    00000012     driverlib.a : dl_spi.o (.text.DL_SPI_setClockConfig)
                  00002586    00000012     timer.o (.text.DL_Timer_getPendingInterrupt)
                  00002598    00000012     debug.o (.text.DL_UART_getPendingInterrupt)
                  000025aa    00000012     openmv.o (.text.DL_UART_getPendingInterrupt)
                  000025bc    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  000025ce    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  000025e0    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  000025f2    00000002     --HOLE-- [fill = 0]
                  000025f4    00000010     ti_msp_dl_config.o (.text.DL_SYSTICK_enable)
                  00002604    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00002614    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00002624    0000000c     timer.o (.text.get_system_time_ms)
                  00002630    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0000263a    00000008     empty.o (.text.GROUP1_IRQHandler)
                  00002642    00000002     --HOLE-- [fill = 0]
                  00002644    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  0000264c    00000006     libc.a : exit.c.obj (.text:abort)
                  00002652    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00002656    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  0000265a    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  0000265e    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00002662    00000006     --HOLE-- [fill = 0]

.cinit     0    00002de0    00000038     
                  00002de0    0000000f     (.cinit..data.load) [load image, compression = lzss]
                  00002def    00000001     --HOLE-- [fill = 0]
                  00002df0    0000000c     (__TI_handler_table)
                  00002dfc    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00002e04    00000010     (__TI_cinit_table)
                  00002e14    00000004     --HOLE-- [fill = 0]

.rodata    0    00002668    00000778     
                  00002668    000005f0     tft180.o (.rodata.ascii_font_8x16)
                  00002c58    00000014     ti_msp_dl_config.o (.rodata.gTIMER_12TimerConfig)
                  00002c6c    00000014     ti_msp_dl_config.o (.rodata.gTIMER_8TimerConfig)
                  00002c80    00000014     empty.o (.rodata.str1.5792233746214848027.1)
                  00002c94    00000013     openmv.o (.rodata.str1.14055760531511630791.1)
                  00002ca7    00000012     openmv.o (.rodata.str1.10222307361326560281.1)
                  00002cb9    00000012     openmv.o (.rodata.str1.10322375466862398049.1)
                  00002ccb    00000012     openmv.o (.rodata.str1.10499335994202400488.1)
                  00002cdd    00000012     openmv.o (.rodata.str1.11972700756869316087.1)
                  00002cef    00000012     openmv.o (.rodata.str1.12960560650680968270.1)
                  00002d01    00000012     openmv.o (.rodata.str1.12983843792890534433.1)
                  00002d13    00000012     openmv.o (.rodata.str1.16410698957387474858.1)
                  00002d25    00000012     openmv.o (.rodata.str1.4018680016288177859.1)
                  00002d37    00000012     openmv.o (.rodata.str1.5573925365973559781.1)
                  00002d49    00000012     openmv.o (.rodata.str1.8871796710599046883.1)
                  00002d5b    00000012     openmv.o (.rodata.str1.9558640640855864504.1)
                  00002d6d    00000001     --HOLE-- [fill = 0]
                  00002d6e    0000000a     ti_msp_dl_config.o (.rodata.gSPI_IMU660RB_config)
                  00002d78    0000000a     ti_msp_dl_config.o (.rodata.gTFT_SPI_config)
                  00002d82    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00002d8c    0000000a     ti_msp_dl_config.o (.rodata.gUART_1Config)
                  00002d96    0000000a     ti_msp_dl_config.o (.rodata.gUART_3Config)
                  00002da0    0000000a     openmv.o (.rodata.str1.16395811435273266920.1)
                  00002daa    0000000a     openmv.o (.rodata.str1.9416414711272993270.1)
                  00002db4    00000008     ti_msp_dl_config.o (.rodata.gPWM_6Config)
                  00002dbc    00000008     ti_msp_dl_config.o (.rodata.gPWM_7Config)
                  00002dc4    00000003     ti_msp_dl_config.o (.rodata.gPWM_6ClockConfig)
                  00002dc7    00000003     ti_msp_dl_config.o (.rodata.gPWM_7ClockConfig)
                  00002dca    00000003     ti_msp_dl_config.o (.rodata.gTIMER_12ClockConfig)
                  00002dcd    00000003     ti_msp_dl_config.o (.rodata.gTIMER_8ClockConfig)
                  00002dd0    00000003     openmv.o (.rodata.str1.15289475315984735280.1)
                  00002dd3    00000002     ti_msp_dl_config.o (.rodata.gSPI_IMU660RB_clockConfig)
                  00002dd5    00000002     ti_msp_dl_config.o (.rodata.gTFT_SPI_clockConfig)
                  00002dd7    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00002dd9    00000002     ti_msp_dl_config.o (.rodata.gUART_1ClockConfig)
                  00002ddb    00000002     ti_msp_dl_config.o (.rodata.gUART_3ClockConfig)
                  00002ddd    00000003     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000001d5     UNINITIALIZED
                  20200000    000000a0     (.common:gPWM_6Backup)
                  202000a0    000000a0     (.common:gPWM_7Backup)
                  20200140    00000030     (.common:gUART_3Backup)
                  20200170    00000028     (.common:gSPI_IMU660RBBackup)
                  20200198    00000028     (.common:gTFT_SPIBackup)
                  202001c0    0000000c     (.common:openmvData)
                  202001cc    00000008     openmv.o (.bss.rx_buffer)
                  202001d4    00000001     openmv.o (.bss.data)

.data      0    202001d8    00000013     UNINITIALIZED
                  202001d8    00000004     timer.o (.data.system_time_ms)
                  202001dc    00000002     encoder.o (.data.left_counter)
                  202001de    00000002     encoder.o (.data.right_counter)
                  202001e0    00000002     openmv.o (.data.tft180_bgcolor)
                  202001e2    00000002     tft180.o (.data.tft180_bgcolor)
                  202001e4    00000002     openmv.o (.data.tft180_pencolor)
                  202001e6    00000001     openmv.o (.data.n)
                  202001e7    00000001     openmv.o (.data.state)
                  202001e8    00000001     tft180.o (.data.tft180_x_max)
                  202001e9    00000001     tft180.o (.data.tft180_y_max)
                  202001ea    00000001     debug.o (.data.uart_data)

.stack     0    20203e00    00000200     UNINITIALIZED
                  20203e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20203e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             2814   128       448    
       startup_mspm0g350x_ticlang.o   8      192       0      
       empty.o                        100    20        0      
    +--+------------------------------+------+---------+---------+
       Total:                         2922   340       448    
                                                              
    .\drivers\
       tft180.o                       2240   1520      4      
       openmv.o                       1084   240       27     
       encoder.o                      598    0         4      
       timer.o                        230    0         4      
    +--+------------------------------+------+---------+---------+
       Total:                         4152   1760      39     
                                                              
    .\soft\
       delay.o                        138    0         0      
       debug.o                        78     0         1      
    +--+------------------------------+------+---------+---------+
       Total:                         216    0         1      
                                                              
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     588    0         0      
       dl_uart.o                      90     0         0      
       dl_spi.o                       86     0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         774    0         0      
                                                              
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       copy_decompress_lzss.c.obj     124    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       memcpy16.S.obj                 18     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         292    0         0      
                                                              
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       comparedf2.c.obj               220    0         0      
       addsf3.S.obj                   216    0         0      
       mulsf3.S.obj                   140    0         0      
       comparesf2.S.obj               118    0         0      
       aeabi_dcmp.S.obj               98     0         0      
       aeabi_fcmp.S.obj               98     0         0      
       aeabi_idivmod.S.obj            86     0         0      
       extendsfdf2.S.obj              64     0         0      
       floatsisf.S.obj                60     0         0      
       muldsi3.S.obj                  58     0         0      
       fixsfsi.S.obj                  56     0         0      
       floatunsisf.S.obj              40     0         0      
       aeabi_memcpy.S.obj             8      0         0      
       aeabi_div0.c.obj               2      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         1264   0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      51        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   9624   2151      1000   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00002e04 records: 2, size/record: 8, table size: 16
	.data: load addr=00002de0, load size=0000000f bytes, run addr=202001d8, run size=00000013 bytes, compression=lzss
	.bss: load addr=00002dfc, load size=00000008 bytes, run addr=20200000, run size=000001d5 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00002df0 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                            
-------   ----                            
00002653  ADC0_IRQHandler                 
00002653  ADC1_IRQHandler                 
00002653  AES_IRQHandler                  
00002656  C$$EXIT                         
00002653  CANFD0_IRQHandler               
00002653  DAC0_IRQHandler                 
00002631  DL_Common_delayCycles           
00001ad5  DL_SPI_init                     
00002575  DL_SPI_setClockConfig           
00000e5d  DL_Timer_initFourCCPWMMode      
00000f61  DL_Timer_initTimerMode          
00002219  DL_Timer_setCaptCompUpdateMethod
000023f1  DL_Timer_setCaptureCompareOutCtl
00002605  DL_Timer_setCaptureCompareValue 
00002235  DL_Timer_setClockConfig         
000019fd  DL_UART_init                    
000025bd  DL_UART_setClockConfig          
00002653  DMA_IRQHandler                  
00002653  Default_Handler                 
00002653  GROUP0_IRQHandler               
0000263b  GROUP1_IRQHandler               
00002657  HOSTexit                        
00002653  HardFault_Handler               
00002653  I2C0_IRQHandler                 
00002653  I2C1_IRQHandler                 
00002653  NMI_Handler                     
00002653  PendSV_Handler                  
00002653  RTC_IRQHandler                  
0000265b  Reset_Handler                   
00002653  SPI0_IRQHandler                 
00002653  SPI1_IRQHandler                 
00002653  SVC_Handler                     
00000759  SYSCFG_DL_GPIO_init             
000011f5  SYSCFG_DL_PWM_6_init            
00001281  SYSCFG_DL_PWM_7_init            
00001b5d  SYSCFG_DL_SPI_IMU660RB_init     
000020f1  SYSCFG_DL_SYSCTL_init           
00002615  SYSCFG_DL_SYSTICK_init          
00001b9d  SYSCFG_DL_TFT_SPI_init          
00001c99  SYSCFG_DL_TIMER_12_init         
00001bdd  SYSCFG_DL_TIMER_8_init          
00001a45  SYSCFG_DL_UART_0_init           
0000190d  SYSCFG_DL_UART_1_init           
00001a8d  SYSCFG_DL_UART_3_init           
00001665  SYSCFG_DL_init                  
00001121  SYSCFG_DL_initPower             
00002653  SysTick_Handler                 
00002653  TIMA0_IRQHandler                
00002653  TIMA1_IRQHandler                
00002653  TIMG0_IRQHandler                
0000226d  TIMG12_IRQHandler               
00002653  TIMG6_IRQHandler                
00002653  TIMG7_IRQHandler                
0000202d  TIMG8_IRQHandler                
000025cf  TI_memcpy_small                 
00002055  UART0_IRQHandler                
00002653  UART1_IRQHandler                
00002653  UART2_IRQHandler                
00000c31  UART3_IRQHandler                
20204000  __STACK_END                     
00000200  __STACK_SIZE                    
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00002e04  __TI_CINIT_Base                 
00002e14  __TI_CINIT_Limit                
00002e14  __TI_CINIT_Warm                 
00002df0  __TI_Handler_Table_Base         
00002dfc  __TI_Handler_Table_Limit        
00001d4d  __TI_auto_init_nobinit_nopinit  
0000141d  __TI_decompress_lzss            
000025e1  __TI_decompress_none            
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
00000000  __TI_static_base__              
000024d3  __TI_zero_init_nomemset         
00001053  __addsf3                        
00001735  __aeabi_dcmpeq                  
00001771  __aeabi_dcmpge                  
00001785  __aeabi_dcmpgt                  
0000175d  __aeabi_dcmple                  
00001749  __aeabi_dcmplt                  
00001c1d  __aeabi_f2d                     
00001e01  __aeabi_f2iz                    
00001053  __aeabi_fadd                    
00001799  __aeabi_fcmpeq                  
000017d5  __aeabi_fcmpge                  
000017e9  __aeabi_fcmpgt                  
000017c1  __aeabi_fcmple                  
000017ad  __aeabi_fcmplt                  
0000130d  __aeabi_fmul                    
00001049  __aeabi_fsub                    
00001cd5  __aeabi_i2f                     
000018b5  __aeabi_idiv                    
00001797  __aeabi_idiv0                   
000018b5  __aeabi_idivmod                 
00002645  __aeabi_memcpy                  
00002645  __aeabi_memcpy4                 
00002645  __aeabi_memcpy8                 
0000207d  __aeabi_ui2f                    
ffffffff  __binit__                       
000016cd  __cmpdf2                        
00001d89  __cmpsf2                        
000016cd  __eqdf2                         
00001d89  __eqsf2                         
00001c1d  __extendsfdf2                   
00001e01  __fixsfsi                       
00001cd5  __floatsisf                     
0000207d  __floatunsisf                   
00001511  __gedf2                         
00001d11  __gesf2                         
00001511  __gtdf2                         
00001d11  __gtsf2                         
000016cd  __ledf2                         
00001d89  __lesf2                         
000016cd  __ltdf2                         
00001d89  __ltsf2                         
UNDEFED   __mpu_init                      
00001dc5  __muldsi3                       
0000130d  __mulsf3                        
000016cd  __nedf2                         
00001d89  __nesf2                         
20203e00  __stack                         
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
00001049  __subsf3                        
000020a5  _c_int00_noargs                 
UNDEFED   _system_post_cinit              
0000265f  _system_pre_init                
0000264d  abort                           
00002668  ascii_font_8x16                 
ffffffff  binit                           
0000247b  delay_ms                        
00001585  delay_us                        
00000349  encoder_exti_callback           
00000945  func_float_to_str               
20200000  gPWM_6Backup                    
202000a0  gPWM_7Backup                    
20200170  gSPI_IMU660RBBackup             
20200198  gTFT_SPIBackup                  
20200140  gUART_3Backup                   
00002625  get_system_time_ms              
00000000  interruptVectors                
202001dc  left_counter                    
0000185d  main                            
202001c0  openmvData                      
00001e6d  openmv_analysis                 
00000555  openmv_display_data             
00002491  openmv_init                     
00001961  openmv_is_data_valid            
202001de  right_counter                   
00001499  tft180_clear_color              
000000c1  tft180_init                     
00000b01  tft180_show_char_color          
00000d51  tft180_show_num_color           
000017fb  tft180_show_string_color        
00001b19  tft180_write_16bit_data         
00001fb1  tft180_write_8bit_data          
0000190b  timerA_callback                 
000024a7  timerA_init                     
00001dc3  timerB_callback                 
000024bd  timerB_init                     
202001ea  uart_data                       


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                            
-------   ----                            
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00000000  __TI_static_base__              
00000000  interruptVectors                
000000c1  tft180_init                     
00000200  __STACK_SIZE                    
00000349  encoder_exti_callback           
00000555  openmv_display_data             
00000759  SYSCFG_DL_GPIO_init             
00000945  func_float_to_str               
00000b01  tft180_show_char_color          
00000c31  UART3_IRQHandler                
00000d51  tft180_show_num_color           
00000e5d  DL_Timer_initFourCCPWMMode      
00000f61  DL_Timer_initTimerMode          
00001049  __aeabi_fsub                    
00001049  __subsf3                        
00001053  __addsf3                        
00001053  __aeabi_fadd                    
00001121  SYSCFG_DL_initPower             
000011f5  SYSCFG_DL_PWM_6_init            
00001281  SYSCFG_DL_PWM_7_init            
0000130d  __aeabi_fmul                    
0000130d  __mulsf3                        
0000141d  __TI_decompress_lzss            
00001499  tft180_clear_color              
00001511  __gedf2                         
00001511  __gtdf2                         
00001585  delay_us                        
00001665  SYSCFG_DL_init                  
000016cd  __cmpdf2                        
000016cd  __eqdf2                         
000016cd  __ledf2                         
000016cd  __ltdf2                         
000016cd  __nedf2                         
00001735  __aeabi_dcmpeq                  
00001749  __aeabi_dcmplt                  
0000175d  __aeabi_dcmple                  
00001771  __aeabi_dcmpge                  
00001785  __aeabi_dcmpgt                  
00001797  __aeabi_idiv0                   
00001799  __aeabi_fcmpeq                  
000017ad  __aeabi_fcmplt                  
000017c1  __aeabi_fcmple                  
000017d5  __aeabi_fcmpge                  
000017e9  __aeabi_fcmpgt                  
000017fb  tft180_show_string_color        
0000185d  main                            
000018b5  __aeabi_idiv                    
000018b5  __aeabi_idivmod                 
0000190b  timerA_callback                 
0000190d  SYSCFG_DL_UART_1_init           
00001961  openmv_is_data_valid            
000019fd  DL_UART_init                    
00001a45  SYSCFG_DL_UART_0_init           
00001a8d  SYSCFG_DL_UART_3_init           
00001ad5  DL_SPI_init                     
00001b19  tft180_write_16bit_data         
00001b5d  SYSCFG_DL_SPI_IMU660RB_init     
00001b9d  SYSCFG_DL_TFT_SPI_init          
00001bdd  SYSCFG_DL_TIMER_8_init          
00001c1d  __aeabi_f2d                     
00001c1d  __extendsfdf2                   
00001c99  SYSCFG_DL_TIMER_12_init         
00001cd5  __aeabi_i2f                     
00001cd5  __floatsisf                     
00001d11  __gesf2                         
00001d11  __gtsf2                         
00001d4d  __TI_auto_init_nobinit_nopinit  
00001d89  __cmpsf2                        
00001d89  __eqsf2                         
00001d89  __lesf2                         
00001d89  __ltsf2                         
00001d89  __nesf2                         
00001dc3  timerB_callback                 
00001dc5  __muldsi3                       
00001e01  __aeabi_f2iz                    
00001e01  __fixsfsi                       
00001e6d  openmv_analysis                 
00001fb1  tft180_write_8bit_data          
0000202d  TIMG8_IRQHandler                
00002055  UART0_IRQHandler                
0000207d  __aeabi_ui2f                    
0000207d  __floatunsisf                   
000020a5  _c_int00_noargs                 
000020f1  SYSCFG_DL_SYSCTL_init           
00002219  DL_Timer_setCaptCompUpdateMethod
00002235  DL_Timer_setClockConfig         
0000226d  TIMG12_IRQHandler               
000023f1  DL_Timer_setCaptureCompareOutCtl
0000247b  delay_ms                        
00002491  openmv_init                     
000024a7  timerA_init                     
000024bd  timerB_init                     
000024d3  __TI_zero_init_nomemset         
00002575  DL_SPI_setClockConfig           
000025bd  DL_UART_setClockConfig          
000025cf  TI_memcpy_small                 
000025e1  __TI_decompress_none            
00002605  DL_Timer_setCaptureCompareValue 
00002615  SYSCFG_DL_SYSTICK_init          
00002625  get_system_time_ms              
00002631  DL_Common_delayCycles           
0000263b  GROUP1_IRQHandler               
00002645  __aeabi_memcpy                  
00002645  __aeabi_memcpy4                 
00002645  __aeabi_memcpy8                 
0000264d  abort                           
00002653  ADC0_IRQHandler                 
00002653  ADC1_IRQHandler                 
00002653  AES_IRQHandler                  
00002653  CANFD0_IRQHandler               
00002653  DAC0_IRQHandler                 
00002653  DMA_IRQHandler                  
00002653  Default_Handler                 
00002653  GROUP0_IRQHandler               
00002653  HardFault_Handler               
00002653  I2C0_IRQHandler                 
00002653  I2C1_IRQHandler                 
00002653  NMI_Handler                     
00002653  PendSV_Handler                  
00002653  RTC_IRQHandler                  
00002653  SPI0_IRQHandler                 
00002653  SPI1_IRQHandler                 
00002653  SVC_Handler                     
00002653  SysTick_Handler                 
00002653  TIMA0_IRQHandler                
00002653  TIMA1_IRQHandler                
00002653  TIMG0_IRQHandler                
00002653  TIMG6_IRQHandler                
00002653  TIMG7_IRQHandler                
00002653  UART1_IRQHandler                
00002653  UART2_IRQHandler                
00002656  C$$EXIT                         
00002657  HOSTexit                        
0000265b  Reset_Handler                   
0000265f  _system_pre_init                
00002668  ascii_font_8x16                 
00002df0  __TI_Handler_Table_Base         
00002dfc  __TI_Handler_Table_Limit        
00002e04  __TI_CINIT_Base                 
00002e14  __TI_CINIT_Limit                
00002e14  __TI_CINIT_Warm                 
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
20200000  gPWM_6Backup                    
202000a0  gPWM_7Backup                    
20200140  gUART_3Backup                   
20200170  gSPI_IMU660RBBackup             
20200198  gTFT_SPIBackup                  
202001c0  openmvData                      
202001dc  left_counter                    
202001de  right_counter                   
202001ea  uart_data                       
20203e00  __stack                         
20204000  __STACK_END                     
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
ffffffff  __binit__                       
ffffffff  binit                           
UNDEFED   __mpu_init                      
UNDEFED   _system_post_cinit              

[172 symbols]
