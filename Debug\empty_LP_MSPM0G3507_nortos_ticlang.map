******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Thu Jul 31 17:21:25 2025

OUTPUT FILE NAME:   <empty_LP_MSPM0G3507_nortos_ticlang.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00002199


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00008000  00002f58  000050a8  R  X
  SRAM                  20200000   00004000  000003e4  00003c1c  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00002f58   00002f58    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00002698   00002698    r-x .text
  00002758    00002758    000007c8   000007c8    r-- .rodata
  00002f20    00002f20    00000038   00000038    r-- .cinit
20200000    20200000    000001e7   00000000    rw-
  20200000    20200000    000001d1   00000000    rw- .bss
  202001d4    202001d4    00000013   00000000    rw- .data
20203e00    20203e00    00000200   00000000    rw-
  20203e00    20203e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00002698     
                  000000c0    00000288     tft180.o (.text.tft180_init)
                  00000348    0000024c     openmv.o (.text.openmv_display_data)
                  00000594    0000020c     encoder.o (.text.encoder_exti_callback)
                  000007a0    000001ec     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  0000098c    000001bc     tft180.o (.text.func_float_to_str)
                  00000b48    00000130     tft180.o (.text.tft180_show_char_color)
                  00000c78    00000120     openmv.o (.text.UART3_IRQHandler)
                  00000d98    0000010c     tft180.o (.text.tft180_show_num_color)
                  00000ea4    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00000fa8    000000e8                 : dl_timer.o (.text.DL_Timer_initTimerMode)
                  00001090    000000d8     openmv.o (.text.display_signed_int_clear_area)
                  00001168    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00001240    000000d4     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00001314    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_6_init)
                  000013a0    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_7_init)
                  0000142c    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  000014b8    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  0000153c    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  000015b8    00000078     tft180.o (.text.tft180_clear_color)
                  00001630    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  000016a4    00000074     delay.o (.text.delay_us)
                  00001718    0000006c     tft180.o (.text.tft180_set_region)
                  00001784    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  000017ec    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00001854    00000062                            : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  000018b6    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000018b8    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  0000191a    00000062     tft180.o (.text.tft180_show_string_color)
                  0000197c    00000058     empty.o (.text.main)
                  000019d4    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00001a2a    00000002     empty.o (.text.timerA_callback)
                  00001a2c    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_1_init)
                  00001a80    00000050     openmv.o (.text.openmv_is_data_valid)
                  00001ad0    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00001b1c    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00001b64    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00001bac    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_3_init)
                  00001bf4    00000044     driverlib.a : dl_spi.o (.text.DL_SPI_init)
                  00001c38    00000044     tft180.o (.text.tft180_write_16bit_data)
                  00001c7c    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_SPI_IMU660RB_init)
                  00001cbc    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_TFT_SPI_init)
                  00001cfc    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_8_init)
                  00001d3c    00000040     libclang_rt.builtins.a : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00001d7c    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00001db8    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_12_init)
                  00001df4    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00001e30    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00001e6c    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00001ea8    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00001ee2    00000002     empty.o (.text.timerB_callback)
                  00001ee4    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  00001f1e    00000002     --HOLE-- [fill = 0]
                  00001f20    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  00001f58    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00001f8c    00000030     ti_msp_dl_config.o (.text.DL_SPI_setFIFOThreshold)
                  00001fbc    00000030     openmv.o (.text.openmv_analysis)
                  00001fec    00000030     tft180.o (.text.tft180_write_index)
                  0000201c    0000002c     openmv.o (.text.__NVIC_ClearPendingIRQ)
                  00002048    0000002c     timer.o (.text.__NVIC_ClearPendingIRQ)
                  00002074    0000002c     openmv.o (.text.__NVIC_EnableIRQ)
                  000020a0    0000002c     timer.o (.text.__NVIC_EnableIRQ)
                  000020cc    0000002c     tft180.o (.text.tft180_write_8bit_data)
                  000020f8    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00002120    00000028     ti_msp_dl_config.o (.text.DL_SYSTICK_init)
                  00002148    00000028     timer.o (.text.TIMG8_IRQHandler)
                  00002170    00000028     debug.o (.text.UART0_IRQHandler)
                  00002198    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000021c0    00000024     ti_msp_dl_config.o (.text.DL_SPI_setBitRateSerialClockDivider)
                  000021e4    00000022     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00002206    00000002     --HOLE-- [fill = 0]
                  00002208    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00002228    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00002246    00000002     --HOLE-- [fill = 0]
                  00002248    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  00002264    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00002280    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  0000229c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  000022b8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  000022d4    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  000022f0    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  0000230c    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00002328    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00002344    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00002360    0000001c     timer.o (.text.TIMG12_IRQHandler)
                  0000237c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00002394    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  000023ac    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  000023c4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  000023dc    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  000023f4    00000018     tft180.o (.text.DL_GPIO_setPins)
                  0000240c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00002424    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  0000243c    00000018     ti_msp_dl_config.o (.text.DL_SPI_enable)
                  00002454    00000018     ti_msp_dl_config.o (.text.DL_SPI_enablePower)
                  0000246c    00000018     tft180.o (.text.DL_SPI_isBusy)
                  00002484    00000018     ti_msp_dl_config.o (.text.DL_SPI_reset)
                  0000249c    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  000024b4    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  000024cc    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000024e4    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  000024fc    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00002514    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  0000252c    00000016     encoder.o (.text.DL_GPIO_readPins)
                  00002542    00000016     tft180.o (.text.DL_SPI_transmitData8)
                  00002558    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  0000256e    00000016     delay.o (.text.delay_ms)
                  00002584    00000016     openmv.o (.text.openmv_init)
                  0000259a    00000016     timer.o (.text.timerA_init)
                  000025b0    00000016     timer.o (.text.timerB_init)
                  000025c6    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  000025dc    00000014     tft180.o (.text.DL_GPIO_clearPins)
                  000025f0    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00002604    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_enableMFCLK)
                  00002618    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  0000262c    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00002640    00000014     debug.o (.text.DL_UART_receiveData)
                  00002654    00000014     openmv.o (.text.DL_UART_receiveData)
                  00002668    00000012     driverlib.a : dl_spi.o (.text.DL_SPI_setClockConfig)
                  0000267a    00000012     timer.o (.text.DL_Timer_getPendingInterrupt)
                  0000268c    00000012     debug.o (.text.DL_UART_getPendingInterrupt)
                  0000269e    00000012     openmv.o (.text.DL_UART_getPendingInterrupt)
                  000026b0    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  000026c2    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  000026d4    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  000026e6    00000002     --HOLE-- [fill = 0]
                  000026e8    00000010     ti_msp_dl_config.o (.text.DL_SYSTICK_enable)
                  000026f8    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00002708    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00002718    0000000c     timer.o (.text.get_system_time_ms)
                  00002724    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0000272e    00000008     empty.o (.text.GROUP1_IRQHandler)
                  00002736    00000002     --HOLE-- [fill = 0]
                  00002738    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00002740    00000006     libc.a : exit.c.obj (.text:abort)
                  00002746    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  0000274a    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  0000274e    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00002752    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00002756    00000002     --HOLE-- [fill = 0]

.cinit     0    00002f20    00000038     
                  00002f20    0000000f     (.cinit..data.load) [load image, compression = lzss]
                  00002f2f    00000001     --HOLE-- [fill = 0]
                  00002f30    0000000c     (__TI_handler_table)
                  00002f3c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00002f44    00000010     (__TI_cinit_table)
                  00002f54    00000004     --HOLE-- [fill = 0]

.rodata    0    00002758    000007c8     
                  00002758    000005f0     tft180.o (.rodata.ascii_font_8x16)
                  00002d48    00000014     ti_msp_dl_config.o (.rodata.gTIMER_12TimerConfig)
                  00002d5c    00000014     ti_msp_dl_config.o (.rodata.gTIMER_8TimerConfig)
                  00002d70    00000014     empty.o (.rodata.str1.5792233746214848027.1)
                  00002d84    00000013     openmv.o (.rodata.str1.14055760531511630791.1)
                  00002d97    00000012     openmv.o (.rodata.str1.10222307361326560281.1)
                  00002da9    00000012     openmv.o (.rodata.str1.10322375466862398049.1)
                  00002dbb    00000012     openmv.o (.rodata.str1.10499335994202400488.1)
                  00002dcd    00000012     openmv.o (.rodata.str1.11972700756869316087.1)
                  00002ddf    00000012     openmv.o (.rodata.str1.12960560650680968270.1)
                  00002df1    00000012     openmv.o (.rodata.str1.12983843792890534433.1)
                  00002e03    00000012     openmv.o (.rodata.str1.15289475315984735280.1)
                  00002e15    00000012     openmv.o (.rodata.str1.16410698957387474858.1)
                  00002e27    00000012     openmv.o (.rodata.str1.16964012482489148156.1)
                  00002e39    00000012     openmv.o (.rodata.str1.4018680016288177859.1)
                  00002e4b    00000012     openmv.o (.rodata.str1.5388014517103437970.1)
                  00002e5d    00000012     openmv.o (.rodata.str1.5573925365973559781.1)
                  00002e6f    00000012     openmv.o (.rodata.str1.5732439976852354875.1)
                  00002e81    00000012     openmv.o (.rodata.str1.8871796710599046883.1)
                  00002e93    00000012     openmv.o (.rodata.str1.9558640640855864504.1)
                  00002ea5    00000001     --HOLE-- [fill = 0]
                  00002ea6    0000000a     ti_msp_dl_config.o (.rodata.gSPI_IMU660RB_config)
                  00002eb0    0000000a     ti_msp_dl_config.o (.rodata.gTFT_SPI_config)
                  00002eba    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00002ec4    0000000a     ti_msp_dl_config.o (.rodata.gUART_1Config)
                  00002ece    0000000a     ti_msp_dl_config.o (.rodata.gUART_3Config)
                  00002ed8    0000000a     openmv.o (.rodata.str1.16395811435273266920.1)
                  00002ee2    0000000a     openmv.o (.rodata.str1.9416414711272993270.1)
                  00002eec    00000008     ti_msp_dl_config.o (.rodata.gPWM_6Config)
                  00002ef4    00000008     ti_msp_dl_config.o (.rodata.gPWM_7Config)
                  00002efc    00000006     openmv.o (.rodata.str1.8815565852085528469.1)
                  00002f02    00000003     ti_msp_dl_config.o (.rodata.gPWM_6ClockConfig)
                  00002f05    00000003     ti_msp_dl_config.o (.rodata.gPWM_7ClockConfig)
                  00002f08    00000003     ti_msp_dl_config.o (.rodata.gTIMER_12ClockConfig)
                  00002f0b    00000003     ti_msp_dl_config.o (.rodata.gTIMER_8ClockConfig)
                  00002f0e    00000002     ti_msp_dl_config.o (.rodata.gSPI_IMU660RB_clockConfig)
                  00002f10    00000002     ti_msp_dl_config.o (.rodata.gTFT_SPI_clockConfig)
                  00002f12    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00002f14    00000002     ti_msp_dl_config.o (.rodata.gUART_1ClockConfig)
                  00002f16    00000002     ti_msp_dl_config.o (.rodata.gUART_3ClockConfig)
                  00002f18    00000002     openmv.o (.rodata.str1.10536413716522492838.1)
                  00002f1a    00000006     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000001d1     UNINITIALIZED
                  20200000    000000a0     (.common:gPWM_6Backup)
                  202000a0    000000a0     (.common:gPWM_7Backup)
                  20200140    00000030     (.common:gUART_3Backup)
                  20200170    00000028     (.common:gSPI_IMU660RBBackup)
                  20200198    00000028     (.common:gTFT_SPIBackup)
                  202001c0    00000008     openmv.o (.bss.rx_buffer)
                  202001c8    00000008     (.common:openmvData)
                  202001d0    00000001     openmv.o (.bss.data)

.data      0    202001d4    00000013     UNINITIALIZED
                  202001d4    00000004     timer.o (.data.system_time_ms)
                  202001d8    00000002     encoder.o (.data.left_counter)
                  202001da    00000002     encoder.o (.data.right_counter)
                  202001dc    00000002     openmv.o (.data.tft180_bgcolor)
                  202001de    00000002     tft180.o (.data.tft180_bgcolor)
                  202001e0    00000002     openmv.o (.data.tft180_pencolor)
                  202001e2    00000001     openmv.o (.data.n)
                  202001e3    00000001     openmv.o (.data.state)
                  202001e4    00000001     tft180.o (.data.tft180_x_max)
                  202001e5    00000001     tft180.o (.data.tft180_y_max)
                  202001e6    00000001     debug.o (.data.uart_data)

.stack     0    20203e00    00000200     UNINITIALIZED
                  20203e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20203e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             2814   128       448    
       startup_mspm0g350x_ticlang.o   8      192       0      
       empty.o                        100    20        0      
    +--+------------------------------+------+---------+---------+
       Total:                         2922   340       448    
                                                              
    .\drivers\
       tft180.o                       2240   1520      4      
       openmv.o                       1368   317       23     
       encoder.o                      598    0         4      
       timer.o                        230    0         4      
    +--+------------------------------+------+---------+---------+
       Total:                         4436   1837      35     
                                                              
    .\soft\
       delay.o                        138    0         0      
       debug.o                        78     0         1      
    +--+------------------------------+------+---------+---------+
       Total:                         216    0         1      
                                                              
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     588    0         0      
       dl_uart.o                      90     0         0      
       dl_spi.o                       86     0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         774    0         0      
                                                              
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       copy_decompress_lzss.c.obj     124    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       memcpy16.S.obj                 18     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         292    0         0      
                                                              
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       comparedf2.c.obj               220    0         0      
       addsf3.S.obj                   216    0         0      
       mulsf3.S.obj                   140    0         0      
       comparesf2.S.obj               118    0         0      
       aeabi_dcmp.S.obj               98     0         0      
       aeabi_fcmp.S.obj               98     0         0      
       aeabi_idivmod.S.obj            86     0         0      
       extendsfdf2.S.obj              64     0         0      
       floatsisf.S.obj                60     0         0      
       muldsi3.S.obj                  58     0         0      
       fixsfsi.S.obj                  56     0         0      
       aeabi_memcpy.S.obj             8      0         0      
       aeabi_div0.c.obj               2      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         1224   0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      51        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   9868   2228      996    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00002f44 records: 2, size/record: 8, table size: 16
	.data: load addr=00002f20, load size=0000000f bytes, run addr=202001d4, run size=00000013 bytes, compression=lzss
	.bss: load addr=00002f3c, load size=00000008 bytes, run addr=20200000, run size=000001d1 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00002f30 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                            
-------   ----                            
00002747  ADC0_IRQHandler                 
00002747  ADC1_IRQHandler                 
00002747  AES_IRQHandler                  
0000274a  C$$EXIT                         
00002747  CANFD0_IRQHandler               
00002747  DAC0_IRQHandler                 
00002725  DL_Common_delayCycles           
00001bf5  DL_SPI_init                     
00002669  DL_SPI_setClockConfig           
00000ea5  DL_Timer_initFourCCPWMMode      
00000fa9  DL_Timer_initTimerMode          
0000230d  DL_Timer_setCaptCompUpdateMethod
000024e5  DL_Timer_setCaptureCompareOutCtl
000026f9  DL_Timer_setCaptureCompareValue 
00002329  DL_Timer_setClockConfig         
00001b1d  DL_UART_init                    
000026b1  DL_UART_setClockConfig          
00002747  DMA_IRQHandler                  
00002747  Default_Handler                 
00002747  GROUP0_IRQHandler               
0000272f  GROUP1_IRQHandler               
0000274b  HOSTexit                        
00002747  HardFault_Handler               
00002747  I2C0_IRQHandler                 
00002747  I2C1_IRQHandler                 
00002747  NMI_Handler                     
00002747  PendSV_Handler                  
00002747  RTC_IRQHandler                  
0000274f  Reset_Handler                   
00002747  SPI0_IRQHandler                 
00002747  SPI1_IRQHandler                 
00002747  SVC_Handler                     
000007a1  SYSCFG_DL_GPIO_init             
00001315  SYSCFG_DL_PWM_6_init            
000013a1  SYSCFG_DL_PWM_7_init            
00001c7d  SYSCFG_DL_SPI_IMU660RB_init     
000021e5  SYSCFG_DL_SYSCTL_init           
00002709  SYSCFG_DL_SYSTICK_init          
00001cbd  SYSCFG_DL_TFT_SPI_init          
00001db9  SYSCFG_DL_TIMER_12_init         
00001cfd  SYSCFG_DL_TIMER_8_init          
00001b65  SYSCFG_DL_UART_0_init           
00001a2d  SYSCFG_DL_UART_1_init           
00001bad  SYSCFG_DL_UART_3_init           
00001785  SYSCFG_DL_init                  
00001241  SYSCFG_DL_initPower             
00002747  SysTick_Handler                 
00002747  TIMA0_IRQHandler                
00002747  TIMA1_IRQHandler                
00002747  TIMG0_IRQHandler                
00002361  TIMG12_IRQHandler               
00002747  TIMG6_IRQHandler                
00002747  TIMG7_IRQHandler                
00002149  TIMG8_IRQHandler                
000026c3  TI_memcpy_small                 
00002171  UART0_IRQHandler                
00002747  UART1_IRQHandler                
00002747  UART2_IRQHandler                
00000c79  UART3_IRQHandler                
20204000  __STACK_END                     
00000200  __STACK_SIZE                    
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00002f44  __TI_CINIT_Base                 
00002f54  __TI_CINIT_Limit                
00002f54  __TI_CINIT_Warm                 
00002f30  __TI_Handler_Table_Base         
00002f3c  __TI_Handler_Table_Limit        
00001e6d  __TI_auto_init_nobinit_nopinit  
0000153d  __TI_decompress_lzss            
000026d5  __TI_decompress_none            
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
00000000  __TI_static_base__              
000025c7  __TI_zero_init_nomemset         
00001173  __addsf3                        
00001855  __aeabi_dcmpeq                  
00001891  __aeabi_dcmpge                  
000018a5  __aeabi_dcmpgt                  
0000187d  __aeabi_dcmple                  
00001869  __aeabi_dcmplt                  
00001d3d  __aeabi_f2d                     
00001f21  __aeabi_f2iz                    
00001173  __aeabi_fadd                    
000018b9  __aeabi_fcmpeq                  
000018f5  __aeabi_fcmpge                  
00001909  __aeabi_fcmpgt                  
000018e1  __aeabi_fcmple                  
000018cd  __aeabi_fcmplt                  
0000142d  __aeabi_fmul                    
00001169  __aeabi_fsub                    
00001df5  __aeabi_i2f                     
000019d5  __aeabi_idiv                    
000018b7  __aeabi_idiv0                   
000019d5  __aeabi_idivmod                 
00002739  __aeabi_memcpy                  
00002739  __aeabi_memcpy4                 
00002739  __aeabi_memcpy8                 
ffffffff  __binit__                       
000017ed  __cmpdf2                        
00001ea9  __cmpsf2                        
000017ed  __eqdf2                         
00001ea9  __eqsf2                         
00001d3d  __extendsfdf2                   
00001f21  __fixsfsi                       
00001df5  __floatsisf                     
00001631  __gedf2                         
00001e31  __gesf2                         
00001631  __gtdf2                         
00001e31  __gtsf2                         
000017ed  __ledf2                         
00001ea9  __lesf2                         
000017ed  __ltdf2                         
00001ea9  __ltsf2                         
UNDEFED   __mpu_init                      
00001ee5  __muldsi3                       
0000142d  __mulsf3                        
000017ed  __nedf2                         
00001ea9  __nesf2                         
20203e00  __stack                         
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
00001169  __subsf3                        
00002199  _c_int00_noargs                 
UNDEFED   _system_post_cinit              
00002753  _system_pre_init                
00002741  abort                           
00002758  ascii_font_8x16                 
ffffffff  binit                           
0000256f  delay_ms                        
000016a5  delay_us                        
00000595  encoder_exti_callback           
0000098d  func_float_to_str               
20200000  gPWM_6Backup                    
202000a0  gPWM_7Backup                    
20200170  gSPI_IMU660RBBackup             
20200198  gTFT_SPIBackup                  
20200140  gUART_3Backup                   
00002719  get_system_time_ms              
00000000  interruptVectors                
202001d8  left_counter                    
0000197d  main                            
202001c8  openmvData                      
00001fbd  openmv_analysis                 
00000349  openmv_display_data             
00002585  openmv_init                     
00001a81  openmv_is_data_valid            
202001da  right_counter                   
000015b9  tft180_clear_color              
000000c1  tft180_init                     
00000b49  tft180_show_char_color          
00000d99  tft180_show_num_color           
0000191b  tft180_show_string_color        
00001c39  tft180_write_16bit_data         
000020cd  tft180_write_8bit_data          
00001a2b  timerA_callback                 
0000259b  timerA_init                     
00001ee3  timerB_callback                 
000025b1  timerB_init                     
202001e6  uart_data                       


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                            
-------   ----                            
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00000000  __TI_static_base__              
00000000  interruptVectors                
000000c1  tft180_init                     
00000200  __STACK_SIZE                    
00000349  openmv_display_data             
00000595  encoder_exti_callback           
000007a1  SYSCFG_DL_GPIO_init             
0000098d  func_float_to_str               
00000b49  tft180_show_char_color          
00000c79  UART3_IRQHandler                
00000d99  tft180_show_num_color           
00000ea5  DL_Timer_initFourCCPWMMode      
00000fa9  DL_Timer_initTimerMode          
00001169  __aeabi_fsub                    
00001169  __subsf3                        
00001173  __addsf3                        
00001173  __aeabi_fadd                    
00001241  SYSCFG_DL_initPower             
00001315  SYSCFG_DL_PWM_6_init            
000013a1  SYSCFG_DL_PWM_7_init            
0000142d  __aeabi_fmul                    
0000142d  __mulsf3                        
0000153d  __TI_decompress_lzss            
000015b9  tft180_clear_color              
00001631  __gedf2                         
00001631  __gtdf2                         
000016a5  delay_us                        
00001785  SYSCFG_DL_init                  
000017ed  __cmpdf2                        
000017ed  __eqdf2                         
000017ed  __ledf2                         
000017ed  __ltdf2                         
000017ed  __nedf2                         
00001855  __aeabi_dcmpeq                  
00001869  __aeabi_dcmplt                  
0000187d  __aeabi_dcmple                  
00001891  __aeabi_dcmpge                  
000018a5  __aeabi_dcmpgt                  
000018b7  __aeabi_idiv0                   
000018b9  __aeabi_fcmpeq                  
000018cd  __aeabi_fcmplt                  
000018e1  __aeabi_fcmple                  
000018f5  __aeabi_fcmpge                  
00001909  __aeabi_fcmpgt                  
0000191b  tft180_show_string_color        
0000197d  main                            
000019d5  __aeabi_idiv                    
000019d5  __aeabi_idivmod                 
00001a2b  timerA_callback                 
00001a2d  SYSCFG_DL_UART_1_init           
00001a81  openmv_is_data_valid            
00001b1d  DL_UART_init                    
00001b65  SYSCFG_DL_UART_0_init           
00001bad  SYSCFG_DL_UART_3_init           
00001bf5  DL_SPI_init                     
00001c39  tft180_write_16bit_data         
00001c7d  SYSCFG_DL_SPI_IMU660RB_init     
00001cbd  SYSCFG_DL_TFT_SPI_init          
00001cfd  SYSCFG_DL_TIMER_8_init          
00001d3d  __aeabi_f2d                     
00001d3d  __extendsfdf2                   
00001db9  SYSCFG_DL_TIMER_12_init         
00001df5  __aeabi_i2f                     
00001df5  __floatsisf                     
00001e31  __gesf2                         
00001e31  __gtsf2                         
00001e6d  __TI_auto_init_nobinit_nopinit  
00001ea9  __cmpsf2                        
00001ea9  __eqsf2                         
00001ea9  __lesf2                         
00001ea9  __ltsf2                         
00001ea9  __nesf2                         
00001ee3  timerB_callback                 
00001ee5  __muldsi3                       
00001f21  __aeabi_f2iz                    
00001f21  __fixsfsi                       
00001fbd  openmv_analysis                 
000020cd  tft180_write_8bit_data          
00002149  TIMG8_IRQHandler                
00002171  UART0_IRQHandler                
00002199  _c_int00_noargs                 
000021e5  SYSCFG_DL_SYSCTL_init           
0000230d  DL_Timer_setCaptCompUpdateMethod
00002329  DL_Timer_setClockConfig         
00002361  TIMG12_IRQHandler               
000024e5  DL_Timer_setCaptureCompareOutCtl
0000256f  delay_ms                        
00002585  openmv_init                     
0000259b  timerA_init                     
000025b1  timerB_init                     
000025c7  __TI_zero_init_nomemset         
00002669  DL_SPI_setClockConfig           
000026b1  DL_UART_setClockConfig          
000026c3  TI_memcpy_small                 
000026d5  __TI_decompress_none            
000026f9  DL_Timer_setCaptureCompareValue 
00002709  SYSCFG_DL_SYSTICK_init          
00002719  get_system_time_ms              
00002725  DL_Common_delayCycles           
0000272f  GROUP1_IRQHandler               
00002739  __aeabi_memcpy                  
00002739  __aeabi_memcpy4                 
00002739  __aeabi_memcpy8                 
00002741  abort                           
00002747  ADC0_IRQHandler                 
00002747  ADC1_IRQHandler                 
00002747  AES_IRQHandler                  
00002747  CANFD0_IRQHandler               
00002747  DAC0_IRQHandler                 
00002747  DMA_IRQHandler                  
00002747  Default_Handler                 
00002747  GROUP0_IRQHandler               
00002747  HardFault_Handler               
00002747  I2C0_IRQHandler                 
00002747  I2C1_IRQHandler                 
00002747  NMI_Handler                     
00002747  PendSV_Handler                  
00002747  RTC_IRQHandler                  
00002747  SPI0_IRQHandler                 
00002747  SPI1_IRQHandler                 
00002747  SVC_Handler                     
00002747  SysTick_Handler                 
00002747  TIMA0_IRQHandler                
00002747  TIMA1_IRQHandler                
00002747  TIMG0_IRQHandler                
00002747  TIMG6_IRQHandler                
00002747  TIMG7_IRQHandler                
00002747  UART1_IRQHandler                
00002747  UART2_IRQHandler                
0000274a  C$$EXIT                         
0000274b  HOSTexit                        
0000274f  Reset_Handler                   
00002753  _system_pre_init                
00002758  ascii_font_8x16                 
00002f30  __TI_Handler_Table_Base         
00002f3c  __TI_Handler_Table_Limit        
00002f44  __TI_CINIT_Base                 
00002f54  __TI_CINIT_Limit                
00002f54  __TI_CINIT_Warm                 
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
20200000  gPWM_6Backup                    
202000a0  gPWM_7Backup                    
20200140  gUART_3Backup                   
20200170  gSPI_IMU660RBBackup             
20200198  gTFT_SPIBackup                  
202001c8  openmvData                      
202001d8  left_counter                    
202001da  right_counter                   
202001e6  uart_data                       
20203e00  __stack                         
20204000  __STACK_END                     
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
ffffffff  __binit__                       
ffffffff  binit                           
UNDEFED   __mpu_init                      
UNDEFED   _system_post_cinit              

[170 symbols]
