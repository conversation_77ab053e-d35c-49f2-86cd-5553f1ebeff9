******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Thu Jul 31 15:14:44 2025

OUTPUT FILE NAME:   <empty_LP_MSPM0G3507_nortos_ticlang.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000020f1


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00008000  00002ea8  00005158  R  X
  SRAM                  20200000   00004000  000003e4  00003c1c  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00002ea8   00002ea8    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000025f0   000025f0    r-x .text
  000026b0    000026b0    000007c0   000007c0    r-- .rodata
  00002e70    00002e70    00000038   00000038    r-- .cinit
20200000    20200000    000001e7   00000000    rw-
  20200000    20200000    000001d1   00000000    rw- .bss
  202001d4    202001d4    00000013   00000000    rw- .data
20203e00    20203e00    00000200   00000000    rw-
  20203e00    20203e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000025f0     
                  000000c0    00000288     tft180.o (.text.tft180_init)
                  00000348    0000027c     openmv.o (.text.openmv_display_data)
                  000005c4    0000020c     encoder.o (.text.encoder_exti_callback)
                  000007d0    000001ec     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000009bc    000001bc     tft180.o (.text.func_float_to_str)
                  00000b78    00000130     tft180.o (.text.tft180_show_char_color)
                  00000ca8    00000120     openmv.o (.text.UART3_IRQHandler)
                  00000dc8    0000010c     tft180.o (.text.tft180_show_num_color)
                  00000ed4    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00000fd8    000000e8                 : dl_timer.o (.text.DL_Timer_initTimerMode)
                  000010c0    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00001198    000000d4     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  0000126c    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_6_init)
                  000012f8    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_7_init)
                  00001384    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00001410    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00001494    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00001510    00000078     tft180.o (.text.tft180_clear_color)
                  00001588    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  000015fc    00000074     delay.o (.text.delay_us)
                  00001670    0000006c     tft180.o (.text.tft180_set_region)
                  000016dc    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00001744    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  000017ac    00000062                            : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  0000180e    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00001810    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00001872    00000062     tft180.o (.text.tft180_show_string_color)
                  000018d4    00000058     empty.o (.text.main)
                  0000192c    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00001982    00000002     empty.o (.text.timerA_callback)
                  00001984    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_1_init)
                  000019d8    00000050     openmv.o (.text.openmv_is_data_valid)
                  00001a28    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00001a74    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00001abc    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00001b04    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_3_init)
                  00001b4c    00000044     driverlib.a : dl_spi.o (.text.DL_SPI_init)
                  00001b90    00000044     tft180.o (.text.tft180_write_16bit_data)
                  00001bd4    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_SPI_IMU660RB_init)
                  00001c14    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_TFT_SPI_init)
                  00001c54    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_8_init)
                  00001c94    00000040     libclang_rt.builtins.a : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00001cd4    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00001d10    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_12_init)
                  00001d4c    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00001d88    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00001dc4    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00001e00    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00001e3a    00000002     empty.o (.text.timerB_callback)
                  00001e3c    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  00001e76    00000002     --HOLE-- [fill = 0]
                  00001e78    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  00001eb0    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00001ee4    00000030     ti_msp_dl_config.o (.text.DL_SPI_setFIFOThreshold)
                  00001f14    00000030     openmv.o (.text.openmv_analysis)
                  00001f44    00000030     tft180.o (.text.tft180_write_index)
                  00001f74    0000002c     openmv.o (.text.__NVIC_ClearPendingIRQ)
                  00001fa0    0000002c     timer.o (.text.__NVIC_ClearPendingIRQ)
                  00001fcc    0000002c     openmv.o (.text.__NVIC_EnableIRQ)
                  00001ff8    0000002c     timer.o (.text.__NVIC_EnableIRQ)
                  00002024    0000002c     tft180.o (.text.tft180_write_8bit_data)
                  00002050    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00002078    00000028     ti_msp_dl_config.o (.text.DL_SYSTICK_init)
                  000020a0    00000028     timer.o (.text.TIMG8_IRQHandler)
                  000020c8    00000028     debug.o (.text.UART0_IRQHandler)
                  000020f0    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00002118    00000024     ti_msp_dl_config.o (.text.DL_SPI_setBitRateSerialClockDivider)
                  0000213c    00000022     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  0000215e    00000002     --HOLE-- [fill = 0]
                  00002160    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00002180    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  0000219e    00000002     --HOLE-- [fill = 0]
                  000021a0    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  000021bc    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  000021d8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  000021f4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00002210    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  0000222c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00002248    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  00002264    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00002280    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  0000229c    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  000022b8    0000001c     timer.o (.text.TIMG12_IRQHandler)
                  000022d4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  000022ec    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00002304    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  0000231c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00002334    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  0000234c    00000018     tft180.o (.text.DL_GPIO_setPins)
                  00002364    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  0000237c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00002394    00000018     ti_msp_dl_config.o (.text.DL_SPI_enable)
                  000023ac    00000018     ti_msp_dl_config.o (.text.DL_SPI_enablePower)
                  000023c4    00000018     tft180.o (.text.DL_SPI_isBusy)
                  000023dc    00000018     ti_msp_dl_config.o (.text.DL_SPI_reset)
                  000023f4    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  0000240c    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00002424    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  0000243c    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00002454    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  0000246c    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00002484    00000016     encoder.o (.text.DL_GPIO_readPins)
                  0000249a    00000016     tft180.o (.text.DL_SPI_transmitData8)
                  000024b0    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  000024c6    00000016     delay.o (.text.delay_ms)
                  000024dc    00000016     openmv.o (.text.openmv_init)
                  000024f2    00000016     timer.o (.text.timerA_init)
                  00002508    00000016     timer.o (.text.timerB_init)
                  0000251e    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00002534    00000014     tft180.o (.text.DL_GPIO_clearPins)
                  00002548    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  0000255c    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_enableMFCLK)
                  00002570    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00002584    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00002598    00000014     debug.o (.text.DL_UART_receiveData)
                  000025ac    00000014     openmv.o (.text.DL_UART_receiveData)
                  000025c0    00000012     driverlib.a : dl_spi.o (.text.DL_SPI_setClockConfig)
                  000025d2    00000012     timer.o (.text.DL_Timer_getPendingInterrupt)
                  000025e4    00000012     debug.o (.text.DL_UART_getPendingInterrupt)
                  000025f6    00000012     openmv.o (.text.DL_UART_getPendingInterrupt)
                  00002608    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  0000261a    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  0000262c    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  0000263e    00000002     --HOLE-- [fill = 0]
                  00002640    00000010     ti_msp_dl_config.o (.text.DL_SYSTICK_enable)
                  00002650    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00002660    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00002670    0000000c     timer.o (.text.get_system_time_ms)
                  0000267c    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00002686    00000008     empty.o (.text.GROUP1_IRQHandler)
                  0000268e    00000002     --HOLE-- [fill = 0]
                  00002690    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00002698    00000006     libc.a : exit.c.obj (.text:abort)
                  0000269e    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  000026a2    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000026a6    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000026aa    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  000026ae    00000002     --HOLE-- [fill = 0]

.cinit     0    00002e70    00000038     
                  00002e70    0000000f     (.cinit..data.load) [load image, compression = lzss]
                  00002e7f    00000001     --HOLE-- [fill = 0]
                  00002e80    0000000c     (__TI_handler_table)
                  00002e8c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00002e94    00000010     (__TI_cinit_table)
                  00002ea4    00000004     --HOLE-- [fill = 0]

.rodata    0    000026b0    000007c0     
                  000026b0    000005f0     tft180.o (.rodata.ascii_font_8x16)
                  00002ca0    00000014     ti_msp_dl_config.o (.rodata.gTIMER_12TimerConfig)
                  00002cb4    00000014     ti_msp_dl_config.o (.rodata.gTIMER_8TimerConfig)
                  00002cc8    00000014     empty.o (.rodata.str1.5792233746214848027.1)
                  00002cdc    00000013     openmv.o (.rodata.str1.14055760531511630791.1)
                  00002cef    00000012     openmv.o (.rodata.str1.10222307361326560281.1)
                  00002d01    00000012     openmv.o (.rodata.str1.10322375466862398049.1)
                  00002d13    00000012     openmv.o (.rodata.str1.10499335994202400488.1)
                  00002d25    00000012     openmv.o (.rodata.str1.11972700756869316087.1)
                  00002d37    00000012     openmv.o (.rodata.str1.12960560650680968270.1)
                  00002d49    00000012     openmv.o (.rodata.str1.12983843792890534433.1)
                  00002d5b    00000012     openmv.o (.rodata.str1.15289475315984735280.1)
                  00002d6d    00000012     openmv.o (.rodata.str1.16410698957387474858.1)
                  00002d7f    00000012     openmv.o (.rodata.str1.16964012482489148156.1)
                  00002d91    00000012     openmv.o (.rodata.str1.4018680016288177859.1)
                  00002da3    00000012     openmv.o (.rodata.str1.5388014517103437970.1)
                  00002db5    00000012     openmv.o (.rodata.str1.5573925365973559781.1)
                  00002dc7    00000012     openmv.o (.rodata.str1.5732439976852354875.1)
                  00002dd9    00000012     openmv.o (.rodata.str1.8871796710599046883.1)
                  00002deb    00000012     openmv.o (.rodata.str1.9558640640855864504.1)
                  00002dfd    00000001     --HOLE-- [fill = 0]
                  00002dfe    0000000a     ti_msp_dl_config.o (.rodata.gSPI_IMU660RB_config)
                  00002e08    0000000a     ti_msp_dl_config.o (.rodata.gTFT_SPI_config)
                  00002e12    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00002e1c    0000000a     ti_msp_dl_config.o (.rodata.gUART_1Config)
                  00002e26    0000000a     ti_msp_dl_config.o (.rodata.gUART_3Config)
                  00002e30    0000000a     openmv.o (.rodata.str1.16395811435273266920.1)
                  00002e3a    0000000a     openmv.o (.rodata.str1.9416414711272993270.1)
                  00002e44    00000008     ti_msp_dl_config.o (.rodata.gPWM_6Config)
                  00002e4c    00000008     ti_msp_dl_config.o (.rodata.gPWM_7Config)
                  00002e54    00000003     ti_msp_dl_config.o (.rodata.gPWM_6ClockConfig)
                  00002e57    00000003     ti_msp_dl_config.o (.rodata.gPWM_7ClockConfig)
                  00002e5a    00000003     ti_msp_dl_config.o (.rodata.gTIMER_12ClockConfig)
                  00002e5d    00000003     ti_msp_dl_config.o (.rodata.gTIMER_8ClockConfig)
                  00002e60    00000002     ti_msp_dl_config.o (.rodata.gSPI_IMU660RB_clockConfig)
                  00002e62    00000002     ti_msp_dl_config.o (.rodata.gTFT_SPI_clockConfig)
                  00002e64    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00002e66    00000002     ti_msp_dl_config.o (.rodata.gUART_1ClockConfig)
                  00002e68    00000002     ti_msp_dl_config.o (.rodata.gUART_3ClockConfig)
                  00002e6a    00000006     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000001d1     UNINITIALIZED
                  20200000    000000a0     (.common:gPWM_6Backup)
                  202000a0    000000a0     (.common:gPWM_7Backup)
                  20200140    00000030     (.common:gUART_3Backup)
                  20200170    00000028     (.common:gSPI_IMU660RBBackup)
                  20200198    00000028     (.common:gTFT_SPIBackup)
                  202001c0    00000008     openmv.o (.bss.rx_buffer)
                  202001c8    00000008     (.common:openmvData)
                  202001d0    00000001     openmv.o (.bss.data)

.data      0    202001d4    00000013     UNINITIALIZED
                  202001d4    00000004     timer.o (.data.system_time_ms)
                  202001d8    00000002     encoder.o (.data.left_counter)
                  202001da    00000002     encoder.o (.data.right_counter)
                  202001dc    00000002     openmv.o (.data.tft180_bgcolor)
                  202001de    00000002     tft180.o (.data.tft180_bgcolor)
                  202001e0    00000002     openmv.o (.data.tft180_pencolor)
                  202001e2    00000001     openmv.o (.data.n)
                  202001e3    00000001     openmv.o (.data.state)
                  202001e4    00000001     tft180.o (.data.tft180_x_max)
                  202001e5    00000001     tft180.o (.data.tft180_y_max)
                  202001e6    00000001     debug.o (.data.uart_data)

.stack     0    20203e00    00000200     UNINITIALIZED
                  20203e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20203e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             2814   128       448    
       startup_mspm0g350x_ticlang.o   8      192       0      
       empty.o                        100    20        0      
    +--+------------------------------+------+---------+---------+
       Total:                         2922   340       448    
                                                              
    .\drivers\
       tft180.o                       2240   1520      4      
       openmv.o                       1200   309       23     
       encoder.o                      598    0         4      
       timer.o                        230    0         4      
    +--+------------------------------+------+---------+---------+
       Total:                         4268   1829      35     
                                                              
    .\soft\
       delay.o                        138    0         0      
       debug.o                        78     0         1      
    +--+------------------------------+------+---------+---------+
       Total:                         216    0         1      
                                                              
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     588    0         0      
       dl_uart.o                      90     0         0      
       dl_spi.o                       86     0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         774    0         0      
                                                              
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       copy_decompress_lzss.c.obj     124    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       memcpy16.S.obj                 18     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         292    0         0      
                                                              
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       comparedf2.c.obj               220    0         0      
       addsf3.S.obj                   216    0         0      
       mulsf3.S.obj                   140    0         0      
       comparesf2.S.obj               118    0         0      
       aeabi_dcmp.S.obj               98     0         0      
       aeabi_fcmp.S.obj               98     0         0      
       aeabi_idivmod.S.obj            86     0         0      
       extendsfdf2.S.obj              64     0         0      
       floatsisf.S.obj                60     0         0      
       muldsi3.S.obj                  58     0         0      
       fixsfsi.S.obj                  56     0         0      
       aeabi_memcpy.S.obj             8      0         0      
       aeabi_div0.c.obj               2      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         1224   0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      51        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   9700   2220      996    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00002e94 records: 2, size/record: 8, table size: 16
	.data: load addr=00002e70, load size=0000000f bytes, run addr=202001d4, run size=00000013 bytes, compression=lzss
	.bss: load addr=00002e8c, load size=00000008 bytes, run addr=20200000, run size=000001d1 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00002e80 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                            
-------   ----                            
0000269f  ADC0_IRQHandler                 
0000269f  ADC1_IRQHandler                 
0000269f  AES_IRQHandler                  
000026a2  C$$EXIT                         
0000269f  CANFD0_IRQHandler               
0000269f  DAC0_IRQHandler                 
0000267d  DL_Common_delayCycles           
00001b4d  DL_SPI_init                     
000025c1  DL_SPI_setClockConfig           
00000ed5  DL_Timer_initFourCCPWMMode      
00000fd9  DL_Timer_initTimerMode          
00002265  DL_Timer_setCaptCompUpdateMethod
0000243d  DL_Timer_setCaptureCompareOutCtl
00002651  DL_Timer_setCaptureCompareValue 
00002281  DL_Timer_setClockConfig         
00001a75  DL_UART_init                    
00002609  DL_UART_setClockConfig          
0000269f  DMA_IRQHandler                  
0000269f  Default_Handler                 
0000269f  GROUP0_IRQHandler               
00002687  GROUP1_IRQHandler               
000026a3  HOSTexit                        
0000269f  HardFault_Handler               
0000269f  I2C0_IRQHandler                 
0000269f  I2C1_IRQHandler                 
0000269f  NMI_Handler                     
0000269f  PendSV_Handler                  
0000269f  RTC_IRQHandler                  
000026a7  Reset_Handler                   
0000269f  SPI0_IRQHandler                 
0000269f  SPI1_IRQHandler                 
0000269f  SVC_Handler                     
000007d1  SYSCFG_DL_GPIO_init             
0000126d  SYSCFG_DL_PWM_6_init            
000012f9  SYSCFG_DL_PWM_7_init            
00001bd5  SYSCFG_DL_SPI_IMU660RB_init     
0000213d  SYSCFG_DL_SYSCTL_init           
00002661  SYSCFG_DL_SYSTICK_init          
00001c15  SYSCFG_DL_TFT_SPI_init          
00001d11  SYSCFG_DL_TIMER_12_init         
00001c55  SYSCFG_DL_TIMER_8_init          
00001abd  SYSCFG_DL_UART_0_init           
00001985  SYSCFG_DL_UART_1_init           
00001b05  SYSCFG_DL_UART_3_init           
000016dd  SYSCFG_DL_init                  
00001199  SYSCFG_DL_initPower             
0000269f  SysTick_Handler                 
0000269f  TIMA0_IRQHandler                
0000269f  TIMA1_IRQHandler                
0000269f  TIMG0_IRQHandler                
000022b9  TIMG12_IRQHandler               
0000269f  TIMG6_IRQHandler                
0000269f  TIMG7_IRQHandler                
000020a1  TIMG8_IRQHandler                
0000261b  TI_memcpy_small                 
000020c9  UART0_IRQHandler                
0000269f  UART1_IRQHandler                
0000269f  UART2_IRQHandler                
00000ca9  UART3_IRQHandler                
20204000  __STACK_END                     
00000200  __STACK_SIZE                    
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00002e94  __TI_CINIT_Base                 
00002ea4  __TI_CINIT_Limit                
00002ea4  __TI_CINIT_Warm                 
00002e80  __TI_Handler_Table_Base         
00002e8c  __TI_Handler_Table_Limit        
00001dc5  __TI_auto_init_nobinit_nopinit  
00001495  __TI_decompress_lzss            
0000262d  __TI_decompress_none            
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
00000000  __TI_static_base__              
0000251f  __TI_zero_init_nomemset         
000010cb  __addsf3                        
000017ad  __aeabi_dcmpeq                  
000017e9  __aeabi_dcmpge                  
000017fd  __aeabi_dcmpgt                  
000017d5  __aeabi_dcmple                  
000017c1  __aeabi_dcmplt                  
00001c95  __aeabi_f2d                     
00001e79  __aeabi_f2iz                    
000010cb  __aeabi_fadd                    
00001811  __aeabi_fcmpeq                  
0000184d  __aeabi_fcmpge                  
00001861  __aeabi_fcmpgt                  
00001839  __aeabi_fcmple                  
00001825  __aeabi_fcmplt                  
00001385  __aeabi_fmul                    
000010c1  __aeabi_fsub                    
00001d4d  __aeabi_i2f                     
0000192d  __aeabi_idiv                    
0000180f  __aeabi_idiv0                   
0000192d  __aeabi_idivmod                 
00002691  __aeabi_memcpy                  
00002691  __aeabi_memcpy4                 
00002691  __aeabi_memcpy8                 
ffffffff  __binit__                       
00001745  __cmpdf2                        
00001e01  __cmpsf2                        
00001745  __eqdf2                         
00001e01  __eqsf2                         
00001c95  __extendsfdf2                   
00001e79  __fixsfsi                       
00001d4d  __floatsisf                     
00001589  __gedf2                         
00001d89  __gesf2                         
00001589  __gtdf2                         
00001d89  __gtsf2                         
00001745  __ledf2                         
00001e01  __lesf2                         
00001745  __ltdf2                         
00001e01  __ltsf2                         
UNDEFED   __mpu_init                      
00001e3d  __muldsi3                       
00001385  __mulsf3                        
00001745  __nedf2                         
00001e01  __nesf2                         
20203e00  __stack                         
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
000010c1  __subsf3                        
000020f1  _c_int00_noargs                 
UNDEFED   _system_post_cinit              
000026ab  _system_pre_init                
00002699  abort                           
000026b0  ascii_font_8x16                 
ffffffff  binit                           
000024c7  delay_ms                        
000015fd  delay_us                        
000005c5  encoder_exti_callback           
000009bd  func_float_to_str               
20200000  gPWM_6Backup                    
202000a0  gPWM_7Backup                    
20200170  gSPI_IMU660RBBackup             
20200198  gTFT_SPIBackup                  
20200140  gUART_3Backup                   
00002671  get_system_time_ms              
00000000  interruptVectors                
202001d8  left_counter                    
000018d5  main                            
202001c8  openmvData                      
00001f15  openmv_analysis                 
00000349  openmv_display_data             
000024dd  openmv_init                     
000019d9  openmv_is_data_valid            
202001da  right_counter                   
00001511  tft180_clear_color              
000000c1  tft180_init                     
00000b79  tft180_show_char_color          
00000dc9  tft180_show_num_color           
00001873  tft180_show_string_color        
00001b91  tft180_write_16bit_data         
00002025  tft180_write_8bit_data          
00001983  timerA_callback                 
000024f3  timerA_init                     
00001e3b  timerB_callback                 
00002509  timerB_init                     
202001e6  uart_data                       


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                            
-------   ----                            
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00000000  __TI_static_base__              
00000000  interruptVectors                
000000c1  tft180_init                     
00000200  __STACK_SIZE                    
00000349  openmv_display_data             
000005c5  encoder_exti_callback           
000007d1  SYSCFG_DL_GPIO_init             
000009bd  func_float_to_str               
00000b79  tft180_show_char_color          
00000ca9  UART3_IRQHandler                
00000dc9  tft180_show_num_color           
00000ed5  DL_Timer_initFourCCPWMMode      
00000fd9  DL_Timer_initTimerMode          
000010c1  __aeabi_fsub                    
000010c1  __subsf3                        
000010cb  __addsf3                        
000010cb  __aeabi_fadd                    
00001199  SYSCFG_DL_initPower             
0000126d  SYSCFG_DL_PWM_6_init            
000012f9  SYSCFG_DL_PWM_7_init            
00001385  __aeabi_fmul                    
00001385  __mulsf3                        
00001495  __TI_decompress_lzss            
00001511  tft180_clear_color              
00001589  __gedf2                         
00001589  __gtdf2                         
000015fd  delay_us                        
000016dd  SYSCFG_DL_init                  
00001745  __cmpdf2                        
00001745  __eqdf2                         
00001745  __ledf2                         
00001745  __ltdf2                         
00001745  __nedf2                         
000017ad  __aeabi_dcmpeq                  
000017c1  __aeabi_dcmplt                  
000017d5  __aeabi_dcmple                  
000017e9  __aeabi_dcmpge                  
000017fd  __aeabi_dcmpgt                  
0000180f  __aeabi_idiv0                   
00001811  __aeabi_fcmpeq                  
00001825  __aeabi_fcmplt                  
00001839  __aeabi_fcmple                  
0000184d  __aeabi_fcmpge                  
00001861  __aeabi_fcmpgt                  
00001873  tft180_show_string_color        
000018d5  main                            
0000192d  __aeabi_idiv                    
0000192d  __aeabi_idivmod                 
00001983  timerA_callback                 
00001985  SYSCFG_DL_UART_1_init           
000019d9  openmv_is_data_valid            
00001a75  DL_UART_init                    
00001abd  SYSCFG_DL_UART_0_init           
00001b05  SYSCFG_DL_UART_3_init           
00001b4d  DL_SPI_init                     
00001b91  tft180_write_16bit_data         
00001bd5  SYSCFG_DL_SPI_IMU660RB_init     
00001c15  SYSCFG_DL_TFT_SPI_init          
00001c55  SYSCFG_DL_TIMER_8_init          
00001c95  __aeabi_f2d                     
00001c95  __extendsfdf2                   
00001d11  SYSCFG_DL_TIMER_12_init         
00001d4d  __aeabi_i2f                     
00001d4d  __floatsisf                     
00001d89  __gesf2                         
00001d89  __gtsf2                         
00001dc5  __TI_auto_init_nobinit_nopinit  
00001e01  __cmpsf2                        
00001e01  __eqsf2                         
00001e01  __lesf2                         
00001e01  __ltsf2                         
00001e01  __nesf2                         
00001e3b  timerB_callback                 
00001e3d  __muldsi3                       
00001e79  __aeabi_f2iz                    
00001e79  __fixsfsi                       
00001f15  openmv_analysis                 
00002025  tft180_write_8bit_data          
000020a1  TIMG8_IRQHandler                
000020c9  UART0_IRQHandler                
000020f1  _c_int00_noargs                 
0000213d  SYSCFG_DL_SYSCTL_init           
00002265  DL_Timer_setCaptCompUpdateMethod
00002281  DL_Timer_setClockConfig         
000022b9  TIMG12_IRQHandler               
0000243d  DL_Timer_setCaptureCompareOutCtl
000024c7  delay_ms                        
000024dd  openmv_init                     
000024f3  timerA_init                     
00002509  timerB_init                     
0000251f  __TI_zero_init_nomemset         
000025c1  DL_SPI_setClockConfig           
00002609  DL_UART_setClockConfig          
0000261b  TI_memcpy_small                 
0000262d  __TI_decompress_none            
00002651  DL_Timer_setCaptureCompareValue 
00002661  SYSCFG_DL_SYSTICK_init          
00002671  get_system_time_ms              
0000267d  DL_Common_delayCycles           
00002687  GROUP1_IRQHandler               
00002691  __aeabi_memcpy                  
00002691  __aeabi_memcpy4                 
00002691  __aeabi_memcpy8                 
00002699  abort                           
0000269f  ADC0_IRQHandler                 
0000269f  ADC1_IRQHandler                 
0000269f  AES_IRQHandler                  
0000269f  CANFD0_IRQHandler               
0000269f  DAC0_IRQHandler                 
0000269f  DMA_IRQHandler                  
0000269f  Default_Handler                 
0000269f  GROUP0_IRQHandler               
0000269f  HardFault_Handler               
0000269f  I2C0_IRQHandler                 
0000269f  I2C1_IRQHandler                 
0000269f  NMI_Handler                     
0000269f  PendSV_Handler                  
0000269f  RTC_IRQHandler                  
0000269f  SPI0_IRQHandler                 
0000269f  SPI1_IRQHandler                 
0000269f  SVC_Handler                     
0000269f  SysTick_Handler                 
0000269f  TIMA0_IRQHandler                
0000269f  TIMA1_IRQHandler                
0000269f  TIMG0_IRQHandler                
0000269f  TIMG6_IRQHandler                
0000269f  TIMG7_IRQHandler                
0000269f  UART1_IRQHandler                
0000269f  UART2_IRQHandler                
000026a2  C$$EXIT                         
000026a3  HOSTexit                        
000026a7  Reset_Handler                   
000026ab  _system_pre_init                
000026b0  ascii_font_8x16                 
00002e80  __TI_Handler_Table_Base         
00002e8c  __TI_Handler_Table_Limit        
00002e94  __TI_CINIT_Base                 
00002ea4  __TI_CINIT_Limit                
00002ea4  __TI_CINIT_Warm                 
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
20200000  gPWM_6Backup                    
202000a0  gPWM_7Backup                    
20200140  gUART_3Backup                   
20200170  gSPI_IMU660RBBackup             
20200198  gTFT_SPIBackup                  
202001c8  openmvData                      
202001d8  left_counter                    
202001da  right_counter                   
202001e6  uart_data                       
20203e00  __stack                         
20204000  __STACK_END                     
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
ffffffff  __binit__                       
ffffffff  binit                           
UNDEFED   __mpu_init                      
UNDEFED   _system_post_cinit              

[170 symbols]
