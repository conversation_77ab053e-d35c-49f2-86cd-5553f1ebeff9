//
// Created by faz<PERSON><PERSON> on 2024/7/25.
//

#include "openmv.h"

static uint8_t rx_buffer[8];
static uint8_t tx_buffer[8];

openmv_data_t openmvData;
openmv_command_t openmvCommand;

// 外部时间函数声明（需要在timer.c中实现）
extern uint32_t get_system_time_ms(void);

void openmv_init()
{
    //清除串口中断标志
    NVIC_ClearPendingIRQ(UART3_INT_IRQn);
    //使能串口中断
    NVIC_EnableIRQ(UART3_INT_IRQn);
}

void openmv_analysis()
{
    // 解析OpenMV发送的数据
    // 数据格式：[0x2B, 0x11, data0, data1, data2, data3, 0x5A, 0x59]
    openmvData.detection_flag = rx_buffer[2];  // 检测标志
    openmvData.offset_x = (int8_t)rx_buffer[3]; // X轴偏移量（有符号）
    openmvData.offset_y = (int8_t)rx_buffer[4]; // Y轴偏移量（有符号）

    // rx_buffer[5] 为保留字节，暂时不使用

    // 标记数据有效并更新时间戳
    openmvData.data_valid = 1;
    openmvData.last_update_time = get_system_time_ms();

    // 调试输出（可选）
    // printf("OpenMV: flag=%d, x=%d, y=%d\n",
    //        openmvData.detection_flag, openmvData.offset_x, openmvData.offset_y);
}

static uint8_t get_verify_code(uint8_t *buffer)
{
    uint32_t sum = 0;
    for (int i = 0; i < 6; ++i) {
        sum += buffer[i];
    }

    return sum&0xff;
}

void send_openmv_command()
{
    tx_buffer[0] = 0x5a;
    tx_buffer[1] = 0x00;  // 添加缺失的字节
    tx_buffer[2] = openmvCommand.num1;
    tx_buffer[3] = openmvCommand.num2;
    tx_buffer[4] = openmvCommand.num3;
    tx_buffer[5] = openmvCommand.num4;
    tx_buffer[6] = get_verify_code(tx_buffer);
    tx_buffer[7] = 0xa5;

    for(uint8_t i = 0; i < 8; i++){
        //当串口忙的时候等待，不忙的时候再发送传进来的字符
        while(DL_UART_isBusy(UART3) == true);
        //发送单个字符
        DL_UART_Main_transmitData(UART3, tx_buffer[i]);
    }
}

#define ERROR   0
#define DOING   1
#define SUCCESS 2
static uint8_t data;
static uint8_t n = 0, state = 0;

void UART3_IRQHandler(void)
{
    switch( DL_UART_getPendingInterrupt(UART3) )
    {
        case DL_UART_IIDX_RX:
            data = DL_UART_Main_receiveData(UART3);

            if (state == ERROR){
                n = 0;
            }

            rx_buffer[n] = data;

            // 检查帧头：0x2B, 0x11
            if (n == 0){
                if (rx_buffer[0] == 0x2B){
                    state = DOING;
                } else{
                    state = ERROR;
                }
            }
            else if (n == 1){
                if (rx_buffer[1] == 0x11){
                    state = DOING;
                } else{
                    state = ERROR;
                }
            }
            // 检查帧尾：0x5A, 0x59
            else if (n == 6){
                if (rx_buffer[6] == 0x5A){
                    state = DOING;
                } else{
                    state = ERROR;
                }
            }
            else if (n == 7){
                if (rx_buffer[7] == 0x59){
                    state = SUCCESS;
                } else{
                    state = ERROR;
                }
            }

            n += 1;
            if (n >= 8) n = 0;  // 防止缓冲区溢出

            if (state == SUCCESS){
                openmv_analysis();
                state = ERROR;
                n = 0;
            }
            break;
        default://其他的串口中断
            break;
    }
}

// 检查数据是否有效（超时检测）
uint8_t openmv_is_data_valid(void)
{
    if (!openmvData.data_valid) {
        return 0;
    }

    uint32_t current_time = get_system_time_ms();
    if (current_time - openmvData.last_update_time > OPENMV_DATA_TIMEOUT_MS) {
        openmvData.data_valid = 0;
        return 0;
    }

    return 1;
}

// 在TFT屏幕上显示OpenMV数据
void openmv_display_data(void)
{
    // 清除显示区域
    tft180_show_string_color(10, 10, "OpenMV Rect Data: ", BLACK, WHITE);

    if (openmv_is_data_valid()) {
        // 显示检测状态
        if (openmvData.detection_flag) {
            tft180_show_string_color(10, 25, "Rect: FOUND      ", BLACK, GREEN);

            // 显示X轴偏移 - 使用清除区域显示函数
            tft180_show_string_color(10, 40, "X Offset:", BLACK, WHITE);
            display_signed_int_clear_area(80, 40, openmvData.offset_x, BLACK, WHITE);

            // 显示Y轴偏移 - 使用清除区域显示函数
            tft180_show_string_color(10, 55, "Y Offset:", BLACK, WHITE);
            display_signed_int_clear_area(80, 55, openmvData.offset_y, BLACK, WHITE);

            // 显示方向指示
            if (openmvData.offset_x > 10) {
                tft180_show_string_color(10, 70, "X-Dir: RIGHT     ", BLACK, YELLOW);
            } else if (openmvData.offset_x < -10) {
                tft180_show_string_color(10, 70, "X-Dir: LEFT      ", BLACK, YELLOW);
            } else {
                tft180_show_string_color(10, 70, "X-Dir: CENTER    ", BLACK, GREEN);
            }

            // 显示Y方向指示
            if (openmvData.offset_y > 10) {
                tft180_show_string_color(10, 85, "Y-Dir: DOWN      ", BLACK, CYAN);
            } else if (openmvData.offset_y < -10) {
                tft180_show_string_color(10, 85, "Y-Dir: UP        ", BLACK, CYAN);
            } else {
                tft180_show_string_color(10, 85, "Y-Dir: CENTER    ", BLACK, GREEN);
            }
        } else {
            tft180_show_string_color(10, 25, "Rect: NOT FOUND  ", BLACK, RED);
            tft180_show_string_color(10, 40, "X Offset: ---    ", BLACK, WHITE);
            tft180_show_string_color(10, 55, "Y Offset: ---    ", BLACK, WHITE);
            tft180_show_string_color(10, 70, "X-Dir: ---       ", BLACK, WHITE);
            tft180_show_string_color(10, 85, "Y-Dir: ---       ", BLACK, WHITE);
        }
    } else {
        tft180_show_string_color(10, 25, "Status: NO DATA  ", BLACK, RED);
        tft180_show_string_color(10, 40, "Check Connection ", BLACK, YELLOW);
        tft180_show_string_color(10, 55, "                 ", BLACK, WHITE);
        tft180_show_string_color(10, 70, "                 ", BLACK, WHITE);
        tft180_show_string_color(10, 85, "                 ", BLACK, WHITE);
    }
}

// 测试函数：发送测试数据给OpenMV
void openmv_send_test_command(void)
{
    openmvCommand.num1 = 0x01;  // 测试命令
    openmvCommand.num2 = 0x02;
    openmvCommand.num3 = 0x03;
    openmvCommand.num4 = 0x04;
    send_openmv_command();
}

// 获取OpenMV数据的函数（供外部调用）
openmv_data_t* openmv_get_data(void)
{
    return &openmvData;
}

// 辅助函数：清除区域并显示有符号整数，避免重叠
static void display_signed_int_clear_area(uint8_t x, uint8_t y, int16_t value, uint16_t bg_color, uint16_t text_color)
{
    // 方法1：先用背景色清除固定宽度区域
    tft180_show_string_color(x, y, "     ", bg_color, bg_color);  // 5个空格清除区域

    // 方法2：显示格式化的数值
    if (value >= 0) {
        // 正数显示为 "+123"
        tft180_show_string_color(x, y, "+", bg_color, text_color);
        tft180_show_int(x + 8, y, value, 3);  // 8像素偏移显示数字
    } else {
        // 负数显示为 "-123"
        tft180_show_int(x, y, value, 4);  // 直接显示负数
    }
}
