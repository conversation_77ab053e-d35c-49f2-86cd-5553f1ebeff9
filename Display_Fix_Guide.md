# TFT显示数字重叠问题解决方案

## 🔍 问题分析

### 重叠原因：
1. **数字长度变化**：从 `-100` 变为 `-5` 时，新数字无法覆盖旧数字的所有像素
2. **字符宽度不同**：不同字符占用的像素宽度不同
3. **背景未清除**：直接覆盖写入，残留像素显示

### 问题示例：
```
原显示: X Offset: -100
新显示: X Offset: -5 0  ← 残留的"0"
```

## 🔧 解决方案

### 方案1：区域清除法（已实现）
```c
// 先清除固定宽度区域
tft180_show_string_color(x, y, "     ", BLACK, BLACK);
// 再显示新数字
tft180_show_int(x, y, value, width);
```

### 方案2：固定格式显示
```c
// 始终显示固定格式，如 "+123" 或 "-123"
if (value >= 0) {
    tft180_show_string_color(x, y, "+", BLACK, WHITE);
    tft180_show_int(x + 8, y, value, 3);
} else {
    tft180_show_int(x, y, value, 4);
}
```

### 方案3：完整行刷新
```c
// 刷新整行内容
char line_buffer[20];
sprintf(line_buffer, "X Offset: %+4d   ", value);
tft180_show_string_color(10, 40, line_buffer, BLACK, WHITE);
```

## 💻 当前实现

我已经在 `drivers/openmv.c` 中实现了解决方案：

### 新增辅助函数：
```c
static void display_signed_int_clear_area(uint8_t x, uint8_t y, int16_t value, 
                                         uint16_t bg_color, uint16_t text_color)
{
    // 先清除固定区域
    tft180_show_string_color(x, y, "     ", bg_color, bg_color);
    
    // 再显示新数值
    if (value >= 0) {
        tft180_show_string_color(x, y, "+", bg_color, text_color);
        tft180_show_int(x + 8, y, value, 3);
    } else {
        tft180_show_int(x, y, value, 4);
    }
}
```

### 修改后的显示效果：
```
OpenMV Rect Data:
Rect: FOUND
X Offset: +25 
Y Offset: -15 
X-Dir: RIGHT
Y-Dir: UP
```

## 🎯 显示格式说明

### 数字格式：
- **正数**: `+123` (带加号)
- **负数**: `-123` (带减号)
- **零值**: `+0  ` (显示为正零)

### 固定宽度：
- 每个数字区域占用5个字符宽度
- 确保完全覆盖之前的显示内容
- 避免不同长度数字的重叠问题

## 🔍 其他可能的改进

### 1. 使用更大的清除区域
```c
// 清除更大区域确保完全覆盖
tft180_show_string_color(x, y, "        ", BLACK, BLACK);  // 8个空格
```

### 2. 使用背景色矩形清除
```c
// 如果TFT库支持矩形填充
tft180_fill_rectangle(x, y, width, height, BLACK);
```

### 3. 双缓冲显示
```c
// 先在缓冲区准备完整内容，再一次性显示
char display_buffer[160];  // 整行缓冲
sprintf(display_buffer, "X:%+4d Y:%+4d", offset_x, offset_y);
tft180_show_string_color(10, 40, display_buffer, BLACK, WHITE);
```

## 🚀 使用建议

### 1. 编译测试
- 编译修改后的代码
- 观察数字显示是否还有重叠

### 2. 调整参数
如果仍有重叠，可以：
- 增加清除区域宽度
- 调整数字显示位置
- 修改字体大小设置

### 3. 性能考虑
- 清除操作会增加显示时间
- 可以降低刷新频率（如200ms）
- 只在数值变化时才更新显示

## 📝 代码示例

### 完整的无重叠显示函数：
```c
void openmv_display_data_no_overlap(void)
{
    static int8_t last_offset_x = 0;
    static int8_t last_offset_y = 0;
    static uint8_t last_flag = 0;
    
    // 只在数据变化时更新显示
    if (openmvData.offset_x != last_offset_x || 
        openmvData.offset_y != last_offset_y ||
        openmvData.detection_flag != last_flag) {
        
        // 更新显示
        display_signed_int_clear_area(80, 40, openmvData.offset_x, BLACK, WHITE);
        display_signed_int_clear_area(80, 55, openmvData.offset_y, BLACK, WHITE);
        
        // 保存当前值
        last_offset_x = openmvData.offset_x;
        last_offset_y = openmvData.offset_y;
        last_flag = openmvData.detection_flag;
    }
}
```

## 🔧 故障排除

### 如果仍有重叠：
1. **增加清除宽度**：将 `"     "` 改为 `"        "`
2. **检查字体设置**：确认字体大小和间距
3. **调整显示位置**：增加X坐标间距
4. **使用完整行刷新**：刷新整行而不是部分区域

### 性能优化：
1. **减少刷新频率**：从100ms改为200ms
2. **条件更新**：只在数值变化时更新
3. **批量更新**：一次更新多个相关显示项

现在数字重叠问题应该得到解决！
