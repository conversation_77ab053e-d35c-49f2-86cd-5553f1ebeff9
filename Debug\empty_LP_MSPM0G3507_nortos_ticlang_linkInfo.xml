<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v4.0.3.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <command_line>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\bin\tiarmlnk -ID:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib -o empty_LP_MSPM0G3507_nortos_ticlang.out -mempty_LP_MSPM0G3507_nortos_ticlang.map -iC:/ti/mspm0_sdk_2_05_01_00/source -iC:/Users/<USER>/workspace_ccstheia/empty_LP_MSPM0G3507_nortos_ticlang -iC:/Users/<USER>/workspace_ccstheia/empty_LP_MSPM0G3507_nortos_ticlang/Debug/syscfg -iD:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib --diag_wrap=off --display_error_number --warn_sections --xml_link_info=empty_LP_MSPM0G3507_nortos_ticlang_linkInfo.xml --rom_model ./empty.o ./ti_msp_dl_config.o ./startup_mspm0g350x_ticlang.o ./drivers/encoder.o ./drivers/flash.o ./drivers/imu660rb.o ./drivers/key.o ./drivers/lsm6dsr_reg.o ./drivers/motor.o ./drivers/openmv.o ./drivers/quaternion.o ./drivers/tft180.o ./drivers/timer.o ./soft/debug.o ./soft/delay.o ./soft/menu.o ./soft/pid.o ./soft/protocol.o ./soft/task.o ./soft/vofa.o -l./device_linker.cmd -ldevice.cmd.genlibs -llibc.a --start-group -llibc++.a -llibc++abi.a -llibc.a -llibsys.a -llibsysbm.a -llibclang_rt.builtins.a -llibclang_rt.profile.a --end-group --cg_opt_level=0</command_line>
   <link_time>0x688b3da0</link_time>
   <link_errors>0x0</link_errors>
   <output_file>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\empty_LP_MSPM0G3507_nortos_ticlang.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0x20a5</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>empty.o</file>
         <name>empty.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-4">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>encoder.o</file>
         <name>encoder.o</name>
      </input_file>
      <input_file id="fl-5">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>flash.o</file>
         <name>flash.o</name>
      </input_file>
      <input_file id="fl-6">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>imu660rb.o</file>
         <name>imu660rb.o</name>
      </input_file>
      <input_file id="fl-7">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>key.o</file>
         <name>key.o</name>
      </input_file>
      <input_file id="fl-8">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>lsm6dsr_reg.o</file>
         <name>lsm6dsr_reg.o</name>
      </input_file>
      <input_file id="fl-9">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>motor.o</file>
         <name>motor.o</name>
      </input_file>
      <input_file id="fl-a">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>openmv.o</file>
         <name>openmv.o</name>
      </input_file>
      <input_file id="fl-b">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>quaternion.o</file>
         <name>quaternion.o</name>
      </input_file>
      <input_file id="fl-c">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>tft180.o</file>
         <name>tft180.o</name>
      </input_file>
      <input_file id="fl-d">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>timer.o</file>
         <name>timer.o</name>
      </input_file>
      <input_file id="fl-e">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\soft\</path>
         <kind>object</kind>
         <file>debug.o</file>
         <name>debug.o</name>
      </input_file>
      <input_file id="fl-f">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\soft\</path>
         <kind>object</kind>
         <file>delay.o</file>
         <name>delay.o</name>
      </input_file>
      <input_file id="fl-10">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\soft\</path>
         <kind>object</kind>
         <file>menu.o</file>
         <name>menu.o</name>
      </input_file>
      <input_file id="fl-11">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\soft\</path>
         <kind>object</kind>
         <file>pid.o</file>
         <name>pid.o</name>
      </input_file>
      <input_file id="fl-12">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\soft\</path>
         <kind>object</kind>
         <file>protocol.o</file>
         <name>protocol.o</name>
      </input_file>
      <input_file id="fl-13">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\soft\</path>
         <kind>object</kind>
         <file>task.o</file>
         <name>task.o</name>
      </input_file>
      <input_file id="fl-14">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\soft\</path>
         <kind>object</kind>
         <file>vofa.o</file>
         <name>vofa.o</name>
      </input_file>
      <input_file id="fl-21">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-22">
         <path>C:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-23">
         <path>C:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_spi.o</name>
      </input_file>
      <input_file id="fl-24">
         <path>C:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_timer.o</name>
      </input_file>
      <input_file id="fl-25">
         <path>C:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_uart.o</name>
      </input_file>
      <input_file id="fl-3c">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>e_asin.c.obj</name>
      </input_file>
      <input_file id="fl-3d">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>e_atan2.c.obj</name>
      </input_file>
      <input_file id="fl-3e">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>e_sqrt.c.obj</name>
      </input_file>
      <input_file id="fl-3f">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>s_atan.c.obj</name>
      </input_file>
      <input_file id="fl-40">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>aeabi_portable.c.obj</name>
      </input_file>
      <input_file id="fl-41">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>assert.c.obj</name>
      </input_file>
      <input_file id="fl-42">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>defs.c.obj</name>
      </input_file>
      <input_file id="fl-43">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memory.c.obj</name>
      </input_file>
      <input_file id="fl-44">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>strlen.c.obj</name>
      </input_file>
      <input_file id="fl-45">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-46">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-47">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-48">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-49">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-4a">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-4b">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-4c">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-4d">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>sprintf.c.obj</name>
      </input_file>
      <input_file id="fl-4e">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fflush.c.obj</name>
      </input_file>
      <input_file id="fl-4f">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fputs.c.obj</name>
      </input_file>
      <input_file id="fl-50">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_io_perm.c.obj</name>
      </input_file>
      <input_file id="fl-51">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>setvbuf.c.obj</name>
      </input_file>
      <input_file id="fl-52">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-53">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-54">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-55">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_printfi.c.obj</name>
      </input_file>
      <input_file id="fl-56">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fopen.c.obj</name>
      </input_file>
      <input_file id="fl-57">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fseek.c.obj</name>
      </input_file>
      <input_file id="fl-58">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>s_frexp.c.obj</name>
      </input_file>
      <input_file id="fl-59">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>s_scalbn.c.obj</name>
      </input_file>
      <input_file id="fl-5a">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>wcslen.c.obj</name>
      </input_file>
      <input_file id="fl-5b">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_ltoa.c.obj</name>
      </input_file>
      <input_file id="fl-5c">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>atoi.c.obj</name>
      </input_file>
      <input_file id="fl-5d">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memccpy.c.obj</name>
      </input_file>
      <input_file id="fl-5e">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fclose.c.obj</name>
      </input_file>
      <input_file id="fl-5f">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>aeabi_ctype.S.obj</name>
      </input_file>
      <input_file id="fl-ff">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostexit.c.obj</name>
      </input_file>
      <input_file id="fl-100">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>write.c.obj</name>
      </input_file>
      <input_file id="fl-101">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>host_device.c.obj</name>
      </input_file>
      <input_file id="fl-102">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>remove.c.obj</name>
      </input_file>
      <input_file id="fl-103">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>open.c.obj</name>
      </input_file>
      <input_file id="fl-104">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>lseek.c.obj</name>
      </input_file>
      <input_file id="fl-105">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>close.c.obj</name>
      </input_file>
      <input_file id="fl-106">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>unlink.c.obj</name>
      </input_file>
      <input_file id="fl-107">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostclose.c.obj</name>
      </input_file>
      <input_file id="fl-108">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostlseek.c.obj</name>
      </input_file>
      <input_file id="fl-109">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostopen.c.obj</name>
      </input_file>
      <input_file id="fl-10a">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostread.c.obj</name>
      </input_file>
      <input_file id="fl-10b">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostrename.c.obj</name>
      </input_file>
      <input_file id="fl-10c">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostunlink.c.obj</name>
      </input_file>
      <input_file id="fl-10d">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostwrite.c.obj</name>
      </input_file>
      <input_file id="fl-10e">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>trgmsg.c.obj</name>
      </input_file>
      <input_file id="fl-10f">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>addsf3.S.obj</name>
      </input_file>
      <input_file id="fl-110">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>adddf3.S.obj</name>
      </input_file>
      <input_file id="fl-111">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldf3.S.obj</name>
      </input_file>
      <input_file id="fl-112">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldsi3.S.obj</name>
      </input_file>
      <input_file id="fl-113">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>mulsf3.S.obj</name>
      </input_file>
      <input_file id="fl-114">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>divsf3.S.obj</name>
      </input_file>
      <input_file id="fl-115">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>divdf3.S.obj</name>
      </input_file>
      <input_file id="fl-116">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>extendsfdf2.S.obj</name>
      </input_file>
      <input_file id="fl-117">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixdfsi.S.obj</name>
      </input_file>
      <input_file id="fl-118">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixsfsi.S.obj</name>
      </input_file>
      <input_file id="fl-119">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatsidf.S.obj</name>
      </input_file>
      <input_file id="fl-11a">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatsisf.S.obj</name>
      </input_file>
      <input_file id="fl-11b">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatunsisf.S.obj</name>
      </input_file>
      <input_file id="fl-11c">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldi3.S.obj</name>
      </input_file>
      <input_file id="fl-11d">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>truncdfsf2.S.obj</name>
      </input_file>
      <input_file id="fl-11e">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_dcmp.S.obj</name>
      </input_file>
      <input_file id="fl-11f">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_fcmp.S.obj</name>
      </input_file>
      <input_file id="fl-120">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_idivmod.S.obj</name>
      </input_file>
      <input_file id="fl-121">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-122">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-123">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uidivmod.S.obj</name>
      </input_file>
      <input_file id="fl-124">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uldivmod.S.obj</name>
      </input_file>
      <input_file id="fl-125">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>comparesf2.S.obj</name>
      </input_file>
      <input_file id="fl-126">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>udivmoddi4.S.obj</name>
      </input_file>
      <input_file id="fl-127">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>ashldi3.S.obj</name>
      </input_file>
      <input_file id="fl-128">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>comparedf2.c.obj</name>
      </input_file>
      <input_file id="fl-129">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_div0.c.obj</name>
      </input_file>
      <input_file id="fl-12a">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>getdevice.c.obj</name>
      </input_file>
      <input_file id="fl-12b">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-12c">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
      <input_file id="fl-12d">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>strcmp-armv6m.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-b6">
         <name>.text.tft180_init</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0x288</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-6b">
         <name>.text.encoder_exti_callback</name>
         <load_address>0x348</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x348</run_address>
         <size>0x20c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-c5">
         <name>.text.openmv_display_data</name>
         <load_address>0x554</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x554</run_address>
         <size>0x204</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-e8">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0x758</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x758</run_address>
         <size>0x1ec</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1a1">
         <name>.text.func_float_to_str</name>
         <load_address>0x944</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x944</run_address>
         <size>0x1bc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-10b">
         <name>.text.tft180_show_char_color</name>
         <load_address>0xb00</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb00</run_address>
         <size>0x130</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.text.UART3_IRQHandler</name>
         <load_address>0xc30</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc30</run_address>
         <size>0x120</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-113">
         <name>.text.tft180_show_num_color</name>
         <load_address>0xd50</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd50</run_address>
         <size>0x10c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-15b">
         <name>.text.DL_Timer_initFourCCPWMMode</name>
         <load_address>0xe5c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe5c</run_address>
         <size>0x104</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-166">
         <name>.text.DL_Timer_initTimerMode</name>
         <load_address>0xf60</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf60</run_address>
         <size>0xe8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-19d">
         <name>.text</name>
         <load_address>0x1048</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1048</run_address>
         <size>0xd8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-10f"/>
      </object_component>
      <object_component id="oc-e7">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0x1120</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1120</run_address>
         <size>0xd4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-ea">
         <name>.text.SYSCFG_DL_PWM_6_init</name>
         <load_address>0x11f4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x11f4</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-eb">
         <name>.text.SYSCFG_DL_PWM_7_init</name>
         <load_address>0x1280</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1280</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-191">
         <name>.text.__mulsf3</name>
         <load_address>0x130c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x130c</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-113"/>
      </object_component>
      <object_component id="oc-154">
         <name>.text.__NVIC_SetPriority</name>
         <load_address>0x1398</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1398</run_address>
         <size>0x84</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.text:decompress:lzss</name>
         <load_address>0x141c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x141c</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-c0">
         <name>.text.tft180_clear_color</name>
         <load_address>0x1498</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1498</run_address>
         <size>0x78</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-1e3">
         <name>.text.__gedf2</name>
         <load_address>0x1510</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1510</run_address>
         <size>0x74</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-128"/>
      </object_component>
      <object_component id="oc-102">
         <name>.text.delay_us</name>
         <load_address>0x1584</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1584</run_address>
         <size>0x74</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-105">
         <name>.text.tft180_set_region</name>
         <load_address>0x15f8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x15f8</run_address>
         <size>0x6c</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-b1">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0x1664</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1664</run_address>
         <size>0x68</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1dd">
         <name>.text.__ledf2</name>
         <load_address>0x16cc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x16cc</run_address>
         <size>0x68</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-128"/>
      </object_component>
      <object_component id="oc-1cd">
         <name>.text.__aeabi_dcmp</name>
         <load_address>0x1734</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1734</run_address>
         <size>0x62</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11e"/>
      </object_component>
      <object_component id="oc-1be">
         <name>.text.__aeabi_idiv0</name>
         <load_address>0x1796</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1796</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-129"/>
      </object_component>
      <object_component id="oc-1c5">
         <name>.text.__aeabi_fcmp</name>
         <load_address>0x1798</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1798</run_address>
         <size>0x62</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11f"/>
      </object_component>
      <object_component id="oc-c2">
         <name>.text.tft180_show_string_color</name>
         <load_address>0x17fa</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x17fa</run_address>
         <size>0x62</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-93">
         <name>.text.main</name>
         <load_address>0x185c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x185c</run_address>
         <size>0x58</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-199">
         <name>.text.__aeabi_idivmod</name>
         <load_address>0x18b4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x18b4</run_address>
         <size>0x56</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-120"/>
      </object_component>
      <object_component id="oc-73">
         <name>.text.timerA_callback</name>
         <load_address>0x190a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x190a</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-ef">
         <name>.text.SYSCFG_DL_UART_1_init</name>
         <load_address>0x190c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x190c</run_address>
         <size>0x54</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-10e">
         <name>.text.openmv_is_data_valid</name>
         <load_address>0x1960</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1960</run_address>
         <size>0x50</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-174">
         <name>.text.DL_UART_setBaudRateDivisor</name>
         <load_address>0x19b0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x19b0</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-172">
         <name>.text.DL_UART_init</name>
         <load_address>0x19fc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x19fc</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-ee">
         <name>.text.SYSCFG_DL_UART_0_init</name>
         <load_address>0x1a44</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a44</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-f0">
         <name>.text.SYSCFG_DL_UART_3_init</name>
         <load_address>0x1a8c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a8c</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-183">
         <name>.text.DL_SPI_init</name>
         <load_address>0x1ad4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ad4</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-106">
         <name>.text.tft180_write_16bit_data</name>
         <load_address>0x1b18</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b18</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-f1">
         <name>.text.SYSCFG_DL_SPI_IMU660RB_init</name>
         <load_address>0x1b5c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b5c</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-f2">
         <name>.text.SYSCFG_DL_TFT_SPI_init</name>
         <load_address>0x1b9c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b9c</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-ec">
         <name>.text.SYSCFG_DL_TIMER_8_init</name>
         <load_address>0x1bdc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1bdc</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1c9">
         <name>.text.__extendsfdf2</name>
         <load_address>0x1c1c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c1c</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-116"/>
      </object_component>
      <object_component id="oc-15c">
         <name>.text.DL_Timer_setCounterControl</name>
         <load_address>0x1c5c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c5c</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-ed">
         <name>.text.SYSCFG_DL_TIMER_12_init</name>
         <load_address>0x1c98</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c98</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-10f">
         <name>.text.__floatsisf</name>
         <load_address>0x1cd4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1cd4</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11a"/>
      </object_component>
      <object_component id="oc-1da">
         <name>.text.__gtsf2</name>
         <load_address>0x1d10</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d10</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-125"/>
      </object_component>
      <object_component id="oc-ce">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0x1d4c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d4c</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-47"/>
      </object_component>
      <object_component id="oc-1d5">
         <name>.text.__eqsf2</name>
         <load_address>0x1d88</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d88</run_address>
         <size>0x3a</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-125"/>
      </object_component>
      <object_component id="oc-85">
         <name>.text.timerB_callback</name>
         <load_address>0x1dc2</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1dc2</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-1b8">
         <name>.text.__muldsi3</name>
         <load_address>0x1dc4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1dc4</run_address>
         <size>0x3a</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-112"/>
      </object_component>
      <object_component id="oc-195">
         <name>.text.__fixsfsi</name>
         <load_address>0x1e00</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e00</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-118"/>
      </object_component>
      <object_component id="oc-149">
         <name>.text.DL_GPIO_initDigitalInputFeatures</name>
         <load_address>0x1e38</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e38</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-79">
         <name>.text.openmv_analysis</name>
         <load_address>0x1e6c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e6c</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-185">
         <name>.text.DL_SPI_setFIFOThreshold</name>
         <load_address>0x1ea0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ea0</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-fd">
         <name>.text.tft180_write_index</name>
         <load_address>0x1ed0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ed0</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-109">
         <name>.text.__NVIC_ClearPendingIRQ</name>
         <load_address>0x1f00</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f00</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-10c">
         <name>.text.__NVIC_ClearPendingIRQ</name>
         <load_address>0x1f2c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f2c</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-10a">
         <name>.text.__NVIC_EnableIRQ</name>
         <load_address>0x1f58</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f58</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-10d">
         <name>.text.__NVIC_EnableIRQ</name>
         <load_address>0x1f84</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f84</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-fe">
         <name>.text.tft180_write_8bit_data</name>
         <load_address>0x1fb0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1fb0</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-1b1">
         <name>.text.DL_Common_updateReg</name>
         <load_address>0x1fdc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1fdc</run_address>
         <size>0x28</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-18b">
         <name>.text.DL_SYSTICK_init</name>
         <load_address>0x2004</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2004</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-39">
         <name>.text.TIMG8_IRQHandler</name>
         <load_address>0x202c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x202c</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-43">
         <name>.text.UART0_IRQHandler</name>
         <load_address>0x2054</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2054</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-114">
         <name>.text.__floatunsisf</name>
         <load_address>0x207c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x207c</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11b"/>
      </object_component>
      <object_component id="oc-64">
         <name>.text:_c_int00_noargs</name>
         <load_address>0x20a4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20a4</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-184">
         <name>.text.DL_SPI_setBitRateSerialClockDivider</name>
         <load_address>0x20cc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20cc</run_address>
         <size>0x24</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-e9">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0x20f0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20f0</run_address>
         <size>0x22</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-148">
         <name>.text.DL_GPIO_initPeripheralInputFunction</name>
         <load_address>0x2114</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2114</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-173">
         <name>.text.DL_UART_setOversampling</name>
         <load_address>0x2134</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2134</run_address>
         <size>0x1e</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-9f">
         <name>.text.DL_GPIO_clearInterruptStatus</name>
         <load_address>0x2154</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2154</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-14d">
         <name>.text.DL_GPIO_clearInterruptStatus</name>
         <load_address>0x2170</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2170</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-14e">
         <name>.text.DL_GPIO_enableInterrupt</name>
         <load_address>0x218c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x218c</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-146">
         <name>.text.DL_GPIO_initPeripheralOutputFunction</name>
         <load_address>0x21a8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x21a8</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-14b">
         <name>.text.DL_GPIO_setLowerPinsPolarity</name>
         <load_address>0x21c4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x21c4</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-152">
         <name>.text.DL_SYSCTL_setSYSOSCFreq</name>
         <load_address>0x21e0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x21e0</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-167">
         <name>.text.DL_Timer_enableInterrupt</name>
         <load_address>0x21fc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x21fc</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-15e">
         <name>.text.DL_Timer_setCaptCompUpdateMethod</name>
         <load_address>0x2218</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2218</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-155">
         <name>.text.DL_Timer_setClockConfig</name>
         <load_address>0x2234</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2234</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-175">
         <name>.text.DL_UART_enableInterrupt</name>
         <load_address>0x2250</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2250</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-48">
         <name>.text.TIMG12_IRQHandler</name>
         <load_address>0x226c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x226c</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-147">
         <name>.text.DL_GPIO_enableOutput</name>
         <load_address>0x2288</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2288</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-13e">
         <name>.text.DL_GPIO_enablePower</name>
         <load_address>0x22a0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x22a0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-9e">
         <name>.text.DL_GPIO_getEnabledInterruptStatus</name>
         <load_address>0x22b8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x22b8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-14a">
         <name>.text.DL_GPIO_initDigitalOutput</name>
         <load_address>0x22d0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x22d0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-13a">
         <name>.text.DL_GPIO_reset</name>
         <load_address>0x22e8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x22e8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-fc">
         <name>.text.DL_GPIO_setPins</name>
         <load_address>0x2300</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2300</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-150">
         <name>.text.DL_GPIO_setPins</name>
         <load_address>0x2318</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2318</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-14c">
         <name>.text.DL_GPIO_setUpperPinsPolarity</name>
         <load_address>0x2330</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2330</run_address>
         <size>0x18</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-186">
         <name>.text.DL_SPI_enable</name>
         <load_address>0x2348</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2348</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-141">
         <name>.text.DL_SPI_enablePower</name>
         <load_address>0x2360</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2360</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-18e">
         <name>.text.DL_SPI_isBusy</name>
         <load_address>0x2378</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2378</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-13d">
         <name>.text.DL_SPI_reset</name>
         <load_address>0x2390</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2390</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-151">
         <name>.text.DL_SYSCTL_setBORThreshold</name>
         <load_address>0x23a8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x23a8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-13f">
         <name>.text.DL_Timer_enablePower</name>
         <load_address>0x23c0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x23c0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-13b">
         <name>.text.DL_Timer_reset</name>
         <load_address>0x23d8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x23d8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-15d">
         <name>.text.DL_Timer_setCaptureCompareOutCtl</name>
         <load_address>0x23f0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x23f0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-140">
         <name>.text.DL_UART_enablePower</name>
         <load_address>0x2408</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2408</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-13c">
         <name>.text.DL_UART_reset</name>
         <load_address>0x2420</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2420</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-a0">
         <name>.text.DL_GPIO_readPins</name>
         <load_address>0x2438</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2438</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-18d">
         <name>.text.DL_SPI_transmitData8</name>
         <load_address>0x244e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x244e</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-176">
         <name>.text.DL_UART_enable</name>
         <load_address>0x2464</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2464</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-bb">
         <name>.text.delay_ms</name>
         <load_address>0x247a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x247a</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-c1">
         <name>.text.openmv_init</name>
         <load_address>0x2490</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2490</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-c3">
         <name>.text.timerA_init</name>
         <load_address>0x24a6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x24a6</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-c4">
         <name>.text.timerB_init</name>
         <load_address>0x24bc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x24bc</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-5b">
         <name>.text:decompress:ZI:__TI_zero_init_nomemset</name>
         <load_address>0x24d2</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x24d2</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4a"/>
      </object_component>
      <object_component id="oc-fb">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x24e8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x24e8</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-14f">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x24fc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x24fc</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-153">
         <name>.text.DL_SYSCTL_enableMFCLK</name>
         <load_address>0x2510</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2510</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-160">
         <name>.text.DL_Timer_enableClock</name>
         <load_address>0x2524</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2524</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-161">
         <name>.text.DL_Timer_setCCPDirection</name>
         <load_address>0x2538</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2538</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-81">
         <name>.text.DL_UART_receiveData</name>
         <load_address>0x254c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x254c</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-78">
         <name>.text.DL_UART_receiveData</name>
         <load_address>0x2560</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2560</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-17d">
         <name>.text.DL_SPI_setClockConfig</name>
         <load_address>0x2574</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2574</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-72">
         <name>.text.DL_Timer_getPendingInterrupt</name>
         <load_address>0x2586</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2586</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-80">
         <name>.text.DL_UART_getPendingInterrupt</name>
         <load_address>0x2598</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2598</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-77">
         <name>.text.DL_UART_getPendingInterrupt</name>
         <load_address>0x25aa</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x25aa</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-16c">
         <name>.text.DL_UART_setClockConfig</name>
         <load_address>0x25bc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x25bc</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-a7">
         <name>.text:TI_memcpy_small</name>
         <load_address>0x25ce</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x25ce</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12b"/>
      </object_component>
      <object_component id="oc-20">
         <name>.text:decompress:none</name>
         <load_address>0x25e0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x25e0</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4b"/>
      </object_component>
      <object_component id="oc-18c">
         <name>.text.DL_SYSTICK_enable</name>
         <load_address>0x25f4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x25f4</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-15f">
         <name>.text.DL_Timer_setCaptureCompareValue</name>
         <load_address>0x2604</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2604</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-f3">
         <name>.text.SYSCFG_DL_SYSTICK_init</name>
         <load_address>0x2614</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2614</run_address>
         <size>0x10</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-a5">
         <name>.text.get_system_time_ms</name>
         <load_address>0x2624</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2624</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-142">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0x2630</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2630</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-34">
         <name>.text.GROUP1_IRQHandler</name>
         <load_address>0x263a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x263a</run_address>
         <size>0x8</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-54">
         <name>.text.__aeabi_memcpy</name>
         <load_address>0x2644</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2644</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-121"/>
      </object_component>
      <object_component id="oc-c7">
         <name>.text:abort</name>
         <load_address>0x264c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x264c</run_address>
         <size>0x6</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-52"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x2652</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2652</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-129">
         <name>.text.HOSTexit</name>
         <load_address>0x2656</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2656</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-ff"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0x265a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x265a</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.text._system_pre_init</name>
         <load_address>0x265e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x265e</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-48"/>
      </object_component>
      <object_component id="oc-229">
         <name>.cinit..data.load</name>
         <load_address>0x2de0</load_address>
         <readonly>true</readonly>
         <run_address>0x2de0</run_address>
         <size>0xf</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-227">
         <name>__TI_handler_table</name>
         <load_address>0x2df0</load_address>
         <readonly>true</readonly>
         <run_address>0x2df0</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-22a">
         <name>.cinit..bss.load</name>
         <load_address>0x2dfc</load_address>
         <readonly>true</readonly>
         <run_address>0x2dfc</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-228">
         <name>__TI_cinit_table</name>
         <load_address>0x2e04</load_address>
         <readonly>true</readonly>
         <run_address>0x2e04</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-18f">
         <name>.rodata.ascii_font_8x16</name>
         <load_address>0x2668</load_address>
         <readonly>true</readonly>
         <run_address>0x2668</run_address>
         <size>0x5f0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-16b">
         <name>.rodata.gTIMER_12TimerConfig</name>
         <load_address>0x2c58</load_address>
         <readonly>true</readonly>
         <run_address>0x2c58</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-169">
         <name>.rodata.gTIMER_8TimerConfig</name>
         <load_address>0x2c6c</load_address>
         <readonly>true</readonly>
         <run_address>0x2c6c</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-c6">
         <name>.rodata.str1.5792233746214848027.1</name>
         <load_address>0x2c80</load_address>
         <readonly>true</readonly>
         <run_address>0x2c80</run_address>
         <size>0x14</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-118">
         <name>.rodata.str1.14055760531511630791.1</name>
         <load_address>0x2c94</load_address>
         <readonly>true</readonly>
         <run_address>0x2c94</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-11b">
         <name>.rodata.str1.10222307361326560281.1</name>
         <load_address>0x2ca7</load_address>
         <readonly>true</readonly>
         <run_address>0x2ca7</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-126">
         <name>.rodata.str1.10322375466862398049.1</name>
         <load_address>0x2cb9</load_address>
         <readonly>true</readonly>
         <run_address>0x2cb9</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-11a">
         <name>.rodata.str1.10499335994202400488.1</name>
         <load_address>0x2ccb</load_address>
         <readonly>true</readonly>
         <run_address>0x2ccb</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-11e">
         <name>.rodata.str1.11972700756869316087.1</name>
         <load_address>0x2cdd</load_address>
         <readonly>true</readonly>
         <run_address>0x2cdd</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-120">
         <name>.rodata.str1.12960560650680968270.1</name>
         <load_address>0x2cef</load_address>
         <readonly>true</readonly>
         <run_address>0x2cef</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-11c">
         <name>.rodata.str1.12983843792890534433.1</name>
         <load_address>0x2d01</load_address>
         <readonly>true</readonly>
         <run_address>0x2d01</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-128">
         <name>.rodata.str1.16410698957387474858.1</name>
         <load_address>0x2d13</load_address>
         <readonly>true</readonly>
         <run_address>0x2d13</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-11d">
         <name>.rodata.str1.4018680016288177859.1</name>
         <load_address>0x2d25</load_address>
         <readonly>true</readonly>
         <run_address>0x2d25</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-127">
         <name>.rodata.str1.5573925365973559781.1</name>
         <load_address>0x2d37</load_address>
         <readonly>true</readonly>
         <run_address>0x2d37</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-11f">
         <name>.rodata.str1.8871796710599046883.1</name>
         <load_address>0x2d49</load_address>
         <readonly>true</readonly>
         <run_address>0x2d49</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-119">
         <name>.rodata.str1.9558640640855864504.1</name>
         <load_address>0x2d5b</load_address>
         <readonly>true</readonly>
         <run_address>0x2d5b</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-188">
         <name>.rodata.gSPI_IMU660RB_config</name>
         <load_address>0x2d6e</load_address>
         <readonly>true</readonly>
         <run_address>0x2d6e</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-18a">
         <name>.rodata.gTFT_SPI_config</name>
         <load_address>0x2d78</load_address>
         <readonly>true</readonly>
         <run_address>0x2d78</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-178">
         <name>.rodata.gUART_0Config</name>
         <load_address>0x2d82</load_address>
         <readonly>true</readonly>
         <run_address>0x2d82</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-17a">
         <name>.rodata.gUART_1Config</name>
         <load_address>0x2d8c</load_address>
         <readonly>true</readonly>
         <run_address>0x2d8c</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-17c">
         <name>.rodata.gUART_3Config</name>
         <load_address>0x2d96</load_address>
         <readonly>true</readonly>
         <run_address>0x2d96</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-121">
         <name>.rodata.str1.16395811435273266920.1</name>
         <load_address>0x2da0</load_address>
         <readonly>true</readonly>
         <run_address>0x2da0</run_address>
         <size>0xa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-124">
         <name>.rodata.str1.9416414711272993270.1</name>
         <load_address>0x2daa</load_address>
         <readonly>true</readonly>
         <run_address>0x2daa</run_address>
         <size>0xa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-163">
         <name>.rodata.gPWM_6Config</name>
         <load_address>0x2db4</load_address>
         <readonly>true</readonly>
         <run_address>0x2db4</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-165">
         <name>.rodata.gPWM_7Config</name>
         <load_address>0x2dbc</load_address>
         <readonly>true</readonly>
         <run_address>0x2dbc</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-162">
         <name>.rodata.gPWM_6ClockConfig</name>
         <load_address>0x2dc4</load_address>
         <readonly>true</readonly>
         <run_address>0x2dc4</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-164">
         <name>.rodata.gPWM_7ClockConfig</name>
         <load_address>0x2dc7</load_address>
         <readonly>true</readonly>
         <run_address>0x2dc7</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-16a">
         <name>.rodata.gTIMER_12ClockConfig</name>
         <load_address>0x2dca</load_address>
         <readonly>true</readonly>
         <run_address>0x2dca</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-168">
         <name>.rodata.gTIMER_8ClockConfig</name>
         <load_address>0x2dcd</load_address>
         <readonly>true</readonly>
         <run_address>0x2dcd</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-125">
         <name>.rodata.str1.15289475315984735280.1</name>
         <load_address>0x2dd0</load_address>
         <readonly>true</readonly>
         <run_address>0x2dd0</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-187">
         <name>.rodata.gSPI_IMU660RB_clockConfig</name>
         <load_address>0x2dd3</load_address>
         <readonly>true</readonly>
         <run_address>0x2dd3</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-189">
         <name>.rodata.gTFT_SPI_clockConfig</name>
         <load_address>0x2dd5</load_address>
         <readonly>true</readonly>
         <run_address>0x2dd5</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-177">
         <name>.rodata.gUART_0ClockConfig</name>
         <load_address>0x2dd7</load_address>
         <readonly>true</readonly>
         <run_address>0x2dd7</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-179">
         <name>.rodata.gUART_1ClockConfig</name>
         <load_address>0x2dd9</load_address>
         <readonly>true</readonly>
         <run_address>0x2dd9</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-17b">
         <name>.rodata.gUART_3ClockConfig</name>
         <load_address>0x2ddb</load_address>
         <readonly>true</readonly>
         <run_address>0x2ddb</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1f1">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-a2">
         <name>.data.left_counter</name>
         <load_address>0x202001dc</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001dc</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-a1">
         <name>.data.right_counter</name>
         <load_address>0x202001de</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001de</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-7b">
         <name>.data.state</name>
         <load_address>0x202001e7</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001e7</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-7c">
         <name>.data.n</name>
         <load_address>0x202001e6</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001e6</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-122">
         <name>.data.tft180_bgcolor</name>
         <load_address>0x202001e0</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001e0</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-123">
         <name>.data.tft180_pencolor</name>
         <load_address>0x202001e4</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001e4</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-107">
         <name>.data.tft180_x_max</name>
         <load_address>0x202001e8</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001e8</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-108">
         <name>.data.tft180_y_max</name>
         <load_address>0x202001e9</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001e9</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-ff">
         <name>.data.tft180_bgcolor</name>
         <load_address>0x202001e2</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001e2</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-74">
         <name>.data.system_time_ms</name>
         <load_address>0x202001d8</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001d8</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-82">
         <name>.data.uart_data</name>
         <load_address>0x202001ea</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001ea</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-7d">
         <name>.bss.rx_buffer</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202001cc</run_address>
         <size>0x8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-7a">
         <name>.bss.data</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202001d4</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-f4">
         <name>.common:gPWM_6Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0xa0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-f5">
         <name>.common:gPWM_7Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202000a0</run_address>
         <size>0xa0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-f6">
         <name>.common:gUART_3Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200140</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-f7">
         <name>.common:gSPI_IMU660RBBackup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200170</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-f8">
         <name>.common:gTFT_SPIBackup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200198</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-a6">
         <name>.common:openmvData</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202001c0</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20203e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-22c">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20203e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-70">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xfa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-f9">
         <name>.debug_abbrev</name>
         <load_address>0xfa</load_address>
         <run_address>0xfa</run_address>
         <size>0x210</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_abbrev</name>
         <load_address>0x30a</load_address>
         <run_address>0x30a</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-a3">
         <name>.debug_abbrev</name>
         <load_address>0x377</load_address>
         <run_address>0x377</run_address>
         <size>0x185</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-7e">
         <name>.debug_abbrev</name>
         <load_address>0x4fc</load_address>
         <run_address>0x4fc</run_address>
         <size>0x205</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-100">
         <name>.debug_abbrev</name>
         <load_address>0x701</load_address>
         <run_address>0x701</run_address>
         <size>0x19d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-75">
         <name>.debug_abbrev</name>
         <load_address>0x89e</load_address>
         <run_address>0x89e</run_address>
         <size>0x18e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-83">
         <name>.debug_abbrev</name>
         <load_address>0xa2c</load_address>
         <run_address>0xa2c</run_address>
         <size>0x1c4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-103">
         <name>.debug_abbrev</name>
         <load_address>0xbf0</load_address>
         <run_address>0xbf0</run_address>
         <size>0xac</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-1ae">
         <name>.debug_abbrev</name>
         <load_address>0xc9c</load_address>
         <run_address>0xc9c</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-1b6">
         <name>.debug_abbrev</name>
         <load_address>0xcfe</load_address>
         <run_address>0xcfe</run_address>
         <size>0x277</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-1b2">
         <name>.debug_abbrev</name>
         <load_address>0xf75</load_address>
         <run_address>0xf75</run_address>
         <size>0x286</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-1b4">
         <name>.debug_abbrev</name>
         <load_address>0x11fb</load_address>
         <run_address>0x11fb</run_address>
         <size>0x29b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.debug_abbrev</name>
         <load_address>0x1496</load_address>
         <run_address>0x1496</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-133">
         <name>.debug_abbrev</name>
         <load_address>0x1545</load_address>
         <run_address>0x1545</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-47"/>
      </object_component>
      <object_component id="oc-af">
         <name>.debug_abbrev</name>
         <load_address>0x16b5</load_address>
         <run_address>0x16b5</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-48"/>
      </object_component>
      <object_component id="oc-88">
         <name>.debug_abbrev</name>
         <load_address>0x16ee</load_address>
         <run_address>0x16ee</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4a"/>
      </object_component>
      <object_component id="oc-58">
         <name>.debug_abbrev</name>
         <load_address>0x17b0</load_address>
         <run_address>0x17b0</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4b"/>
      </object_component>
      <object_component id="oc-50">
         <name>.debug_abbrev</name>
         <load_address>0x1820</load_address>
         <run_address>0x1820</run_address>
         <size>0x8d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-12d">
         <name>.debug_abbrev</name>
         <load_address>0x18ad</load_address>
         <run_address>0x18ad</run_address>
         <size>0x98</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-52"/>
      </object_component>
      <object_component id="oc-1a3">
         <name>.debug_abbrev</name>
         <load_address>0x1945</load_address>
         <run_address>0x1945</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ff"/>
      </object_component>
      <object_component id="oc-1c4">
         <name>.debug_abbrev</name>
         <load_address>0x1971</load_address>
         <run_address>0x1971</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10f"/>
      </object_component>
      <object_component id="oc-1d1">
         <name>.debug_abbrev</name>
         <load_address>0x1998</load_address>
         <run_address>0x1998</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-112"/>
      </object_component>
      <object_component id="oc-1bc">
         <name>.debug_abbrev</name>
         <load_address>0x19bf</load_address>
         <run_address>0x19bf</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-113"/>
      </object_component>
      <object_component id="oc-1dc">
         <name>.debug_abbrev</name>
         <load_address>0x19e6</load_address>
         <run_address>0x19e6</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-116"/>
      </object_component>
      <object_component id="oc-1bd">
         <name>.debug_abbrev</name>
         <load_address>0x1a0d</load_address>
         <run_address>0x1a0d</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-118"/>
      </object_component>
      <object_component id="oc-190">
         <name>.debug_abbrev</name>
         <load_address>0x1a34</load_address>
         <run_address>0x1a34</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11a"/>
      </object_component>
      <object_component id="oc-1a2">
         <name>.debug_abbrev</name>
         <load_address>0x1a5b</load_address>
         <run_address>0x1a5b</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11b"/>
      </object_component>
      <object_component id="oc-1e4">
         <name>.debug_abbrev</name>
         <load_address>0x1a82</load_address>
         <run_address>0x1a82</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11e"/>
      </object_component>
      <object_component id="oc-1db">
         <name>.debug_abbrev</name>
         <load_address>0x1aa9</load_address>
         <run_address>0x1aa9</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11f"/>
      </object_component>
      <object_component id="oc-1c3">
         <name>.debug_abbrev</name>
         <load_address>0x1ad0</load_address>
         <run_address>0x1ad0</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-120"/>
      </object_component>
      <object_component id="oc-87">
         <name>.debug_abbrev</name>
         <load_address>0x1af7</load_address>
         <run_address>0x1af7</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-121"/>
      </object_component>
      <object_component id="oc-1e5">
         <name>.debug_abbrev</name>
         <load_address>0x1b1e</load_address>
         <run_address>0x1b1e</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-125"/>
      </object_component>
      <object_component id="oc-1e6">
         <name>.debug_abbrev</name>
         <load_address>0x1b43</load_address>
         <run_address>0x1b43</run_address>
         <size>0xc8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-128"/>
      </object_component>
      <object_component id="oc-1d2">
         <name>.debug_abbrev</name>
         <load_address>0x1c0b</load_address>
         <run_address>0x1c0b</run_address>
         <size>0x59</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-129"/>
      </object_component>
      <object_component id="oc-e0">
         <name>.debug_abbrev</name>
         <load_address>0x1c64</load_address>
         <run_address>0x1c64</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12b"/>
      </object_component>
      <object_component id="oc-22e">
         <name>.debug_abbrev</name>
         <load_address>0x1c89</load_address>
         <run_address>0x1c89</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x5ad</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b2">
         <name>.debug_info</name>
         <load_address>0x5ad</load_address>
         <run_address>0x5ad</run_address>
         <size>0x3acb</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x4078</load_address>
         <run_address>0x4078</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-6e">
         <name>.debug_info</name>
         <load_address>0x40f8</load_address>
         <run_address>0x40f8</run_address>
         <size>0xb04</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_info</name>
         <load_address>0x4bfc</load_address>
         <run_address>0x4bfc</run_address>
         <size>0xd86</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-b7">
         <name>.debug_info</name>
         <load_address>0x5982</load_address>
         <run_address>0x5982</run_address>
         <size>0x14df</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_info</name>
         <load_address>0x6e61</load_address>
         <run_address>0x6e61</run_address>
         <size>0xa3c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-45">
         <name>.debug_info</name>
         <load_address>0x789d</load_address>
         <run_address>0x789d</run_address>
         <size>0xa81</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-bd">
         <name>.debug_info</name>
         <load_address>0x831e</load_address>
         <run_address>0x831e</run_address>
         <size>0x121</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-143">
         <name>.debug_info</name>
         <load_address>0x843f</load_address>
         <run_address>0x843f</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-181">
         <name>.debug_info</name>
         <load_address>0x84b4</load_address>
         <run_address>0x84b4</run_address>
         <size>0x1142</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-159">
         <name>.debug_info</name>
         <load_address>0x95f6</load_address>
         <run_address>0x95f6</run_address>
         <size>0x3172</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-16e">
         <name>.debug_info</name>
         <load_address>0xc768</load_address>
         <run_address>0xc768</run_address>
         <size>0x12a6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0xda0e</load_address>
         <run_address>0xda0e</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-d0">
         <name>.debug_info</name>
         <load_address>0xde31</load_address>
         <run_address>0xde31</run_address>
         <size>0x744</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-47"/>
      </object_component>
      <object_component id="oc-90">
         <name>.debug_info</name>
         <load_address>0xe575</load_address>
         <run_address>0xe575</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-48"/>
      </object_component>
      <object_component id="oc-5d">
         <name>.debug_info</name>
         <load_address>0xe5bb</load_address>
         <run_address>0xe5bb</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4a"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_info</name>
         <load_address>0xe74d</load_address>
         <run_address>0xe74d</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4b"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_info</name>
         <load_address>0xe813</load_address>
         <run_address>0xe813</run_address>
         <size>0x17c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-c9">
         <name>.debug_info</name>
         <load_address>0xe98f</load_address>
         <run_address>0xe98f</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-52"/>
      </object_component>
      <object_component id="oc-12a">
         <name>.debug_info</name>
         <load_address>0xea87</load_address>
         <run_address>0xea87</run_address>
         <size>0x3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ff"/>
      </object_component>
      <object_component id="oc-19e">
         <name>.debug_info</name>
         <load_address>0xeac2</load_address>
         <run_address>0xeac2</run_address>
         <size>0x1a7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10f"/>
      </object_component>
      <object_component id="oc-1b9">
         <name>.debug_info</name>
         <load_address>0xec69</load_address>
         <run_address>0xec69</run_address>
         <size>0x18f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-112"/>
      </object_component>
      <object_component id="oc-194">
         <name>.debug_info</name>
         <load_address>0xedf8</load_address>
         <run_address>0xedf8</run_address>
         <size>0x18d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-113"/>
      </object_component>
      <object_component id="oc-1ca">
         <name>.debug_info</name>
         <load_address>0xef85</load_address>
         <run_address>0xef85</run_address>
         <size>0x197</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-116"/>
      </object_component>
      <object_component id="oc-197">
         <name>.debug_info</name>
         <load_address>0xf11c</load_address>
         <run_address>0xf11c</run_address>
         <size>0x18f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-118"/>
      </object_component>
      <object_component id="oc-110">
         <name>.debug_info</name>
         <load_address>0xf2ab</load_address>
         <run_address>0xf2ab</run_address>
         <size>0x193</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11a"/>
      </object_component>
      <object_component id="oc-117">
         <name>.debug_info</name>
         <load_address>0xf43e</load_address>
         <run_address>0xf43e</run_address>
         <size>0x197</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11b"/>
      </object_component>
      <object_component id="oc-1cf">
         <name>.debug_info</name>
         <load_address>0xf5d5</load_address>
         <run_address>0xf5d5</run_address>
         <size>0x217</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11e"/>
      </object_component>
      <object_component id="oc-1c8">
         <name>.debug_info</name>
         <load_address>0xf7ec</load_address>
         <run_address>0xf7ec</run_address>
         <size>0x217</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11f"/>
      </object_component>
      <object_component id="oc-19a">
         <name>.debug_info</name>
         <load_address>0xfa03</load_address>
         <run_address>0xfa03</run_address>
         <size>0x1b9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-120"/>
      </object_component>
      <object_component id="oc-55">
         <name>.debug_info</name>
         <load_address>0xfbbc</load_address>
         <run_address>0xfbbc</run_address>
         <size>0x199</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-121"/>
      </object_component>
      <object_component id="oc-1d9">
         <name>.debug_info</name>
         <load_address>0xfd55</load_address>
         <run_address>0xfd55</run_address>
         <size>0x1c1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-125"/>
      </object_component>
      <object_component id="oc-1e0">
         <name>.debug_info</name>
         <load_address>0xff16</load_address>
         <run_address>0xff16</run_address>
         <size>0x2f9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-128"/>
      </object_component>
      <object_component id="oc-1c1">
         <name>.debug_info</name>
         <load_address>0x1020f</load_address>
         <run_address>0x1020f</run_address>
         <size>0x85</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-129"/>
      </object_component>
      <object_component id="oc-a8">
         <name>.debug_info</name>
         <load_address>0x10294</load_address>
         <run_address>0x10294</run_address>
         <size>0x2fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12b"/>
      </object_component>
      <object_component id="oc-22d">
         <name>.debug_info</name>
         <load_address>0x1058e</load_address>
         <run_address>0x1058e</run_address>
         <size>0xbc</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b3">
         <name>.debug_ranges</name>
         <load_address>0x50</load_address>
         <run_address>0x50</run_address>
         <size>0x1b0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_ranges</name>
         <load_address>0x200</load_address>
         <run_address>0x200</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-6d">
         <name>.debug_ranges</name>
         <load_address>0x218</load_address>
         <run_address>0x218</run_address>
         <size>0x40</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.debug_ranges</name>
         <load_address>0x258</load_address>
         <run_address>0x258</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-b8">
         <name>.debug_ranges</name>
         <load_address>0x2d8</load_address>
         <run_address>0x2d8</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_ranges</name>
         <load_address>0x358</load_address>
         <run_address>0x358</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-46">
         <name>.debug_ranges</name>
         <load_address>0x3a0</load_address>
         <run_address>0x3a0</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-be">
         <name>.debug_ranges</name>
         <load_address>0x410</load_address>
         <run_address>0x410</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-17f">
         <name>.debug_ranges</name>
         <load_address>0x428</load_address>
         <run_address>0x428</run_address>
         <size>0x390</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-157">
         <name>.debug_ranges</name>
         <load_address>0x7b8</load_address>
         <run_address>0x7b8</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-170">
         <name>.debug_ranges</name>
         <load_address>0x990</load_address>
         <run_address>0x990</run_address>
         <size>0x1a8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_ranges</name>
         <load_address>0xb38</load_address>
         <run_address>0xb38</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-d1">
         <name>.debug_ranges</name>
         <load_address>0xb80</load_address>
         <run_address>0xb80</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-47"/>
      </object_component>
      <object_component id="oc-5e">
         <name>.debug_ranges</name>
         <load_address>0xbc8</load_address>
         <run_address>0xbc8</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4a"/>
      </object_component>
      <object_component id="oc-53">
         <name>.debug_ranges</name>
         <load_address>0xbe0</load_address>
         <run_address>0xbe0</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-c8">
         <name>.debug_ranges</name>
         <load_address>0xc30</load_address>
         <run_address>0xc30</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-52"/>
      </object_component>
      <object_component id="oc-1d6">
         <name>.debug_ranges</name>
         <load_address>0xc48</load_address>
         <run_address>0xc48</run_address>
         <size>0x38</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-125"/>
      </object_component>
      <object_component id="oc-1df">
         <name>.debug_ranges</name>
         <load_address>0xc80</load_address>
         <run_address>0xc80</run_address>
         <size>0x38</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-128"/>
      </object_component>
      <object_component id="oc-1c2">
         <name>.debug_ranges</name>
         <load_address>0xcb8</load_address>
         <run_address>0xcb8</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-129"/>
      </object_component>
      <object_component id="oc-ab">
         <name>.debug_ranges</name>
         <load_address>0xcd0</load_address>
         <run_address>0xcd0</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12b"/>
      </object_component>
      <object_component id="oc-71">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x368</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-fa">
         <name>.debug_str</name>
         <load_address>0x368</load_address>
         <run_address>0x368</run_address>
         <size>0x3086</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_str</name>
         <load_address>0x33ee</load_address>
         <run_address>0x33ee</run_address>
         <size>0x173</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-a4">
         <name>.debug_str</name>
         <load_address>0x3561</load_address>
         <run_address>0x3561</run_address>
         <size>0x7de</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-7f">
         <name>.debug_str</name>
         <load_address>0x3d3f</load_address>
         <run_address>0x3d3f</run_address>
         <size>0xa33</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-101">
         <name>.debug_str</name>
         <load_address>0x4772</load_address>
         <run_address>0x4772</run_address>
         <size>0x770</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-76">
         <name>.debug_str</name>
         <load_address>0x4ee2</load_address>
         <run_address>0x4ee2</run_address>
         <size>0x89e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-84">
         <name>.debug_str</name>
         <load_address>0x5780</load_address>
         <run_address>0x5780</run_address>
         <size>0x8af</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-104">
         <name>.debug_str</name>
         <load_address>0x602f</load_address>
         <run_address>0x602f</run_address>
         <size>0x140</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-1af">
         <name>.debug_str</name>
         <load_address>0x616f</load_address>
         <run_address>0x616f</run_address>
         <size>0x178</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-1b7">
         <name>.debug_str</name>
         <load_address>0x62e7</load_address>
         <run_address>0x62e7</run_address>
         <size>0xc46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-1b3">
         <name>.debug_str</name>
         <load_address>0x6f2d</load_address>
         <run_address>0x6f2d</run_address>
         <size>0x1dd7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-1b5">
         <name>.debug_str</name>
         <load_address>0x8d04</load_address>
         <run_address>0x8d04</run_address>
         <size>0xcee</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-4c">
         <name>.debug_str</name>
         <load_address>0x99f2</load_address>
         <run_address>0x99f2</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-134">
         <name>.debug_str</name>
         <load_address>0x9c17</load_address>
         <run_address>0x9c17</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-47"/>
      </object_component>
      <object_component id="oc-b0">
         <name>.debug_str</name>
         <load_address>0x9f46</load_address>
         <run_address>0x9f46</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-48"/>
      </object_component>
      <object_component id="oc-89">
         <name>.debug_str</name>
         <load_address>0xa03b</load_address>
         <run_address>0xa03b</run_address>
         <size>0x19b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4a"/>
      </object_component>
      <object_component id="oc-59">
         <name>.debug_str</name>
         <load_address>0xa1d6</load_address>
         <run_address>0xa1d6</run_address>
         <size>0x168</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4b"/>
      </object_component>
      <object_component id="oc-51">
         <name>.debug_str</name>
         <load_address>0xa33e</load_address>
         <run_address>0xa33e</run_address>
         <size>0x1d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-12e">
         <name>.debug_str</name>
         <load_address>0xa513</load_address>
         <run_address>0xa513</run_address>
         <size>0x148</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-52"/>
      </object_component>
      <object_component id="oc-1a4">
         <name>.debug_str</name>
         <load_address>0xa65b</load_address>
         <run_address>0xa65b</run_address>
         <size>0xe9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ff"/>
      </object_component>
      <object_component id="oc-1e7">
         <name>.debug_str</name>
         <load_address>0xa744</load_address>
         <run_address>0xa744</run_address>
         <size>0x276</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-128"/>
      </object_component>
      <object_component id="oc-1d3">
         <name>.debug_str</name>
         <load_address>0xa9ba</load_address>
         <run_address>0xa9ba</run_address>
         <size>0x193</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-129"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xf4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b4">
         <name>.debug_frame</name>
         <load_address>0xf4</load_address>
         <run_address>0xf4</run_address>
         <size>0x4e8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_frame</name>
         <load_address>0x5dc</load_address>
         <run_address>0x5dc</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-6c">
         <name>.debug_frame</name>
         <load_address>0x60c</load_address>
         <run_address>0x60c</run_address>
         <size>0xa8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_frame</name>
         <load_address>0x6b4</load_address>
         <run_address>0x6b4</run_address>
         <size>0x16c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-b9">
         <name>.debug_frame</name>
         <load_address>0x820</load_address>
         <run_address>0x820</run_address>
         <size>0x198</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_frame</name>
         <load_address>0x9b8</load_address>
         <run_address>0x9b8</run_address>
         <size>0xc4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_frame</name>
         <load_address>0xa7c</load_address>
         <run_address>0xa7c</run_address>
         <size>0x140</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-bc">
         <name>.debug_frame</name>
         <load_address>0xbbc</load_address>
         <run_address>0xbbc</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-144">
         <name>.debug_frame</name>
         <load_address>0xbfc</load_address>
         <run_address>0xbfc</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-180">
         <name>.debug_frame</name>
         <load_address>0xc1c</load_address>
         <run_address>0xc1c</run_address>
         <size>0x234</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-158">
         <name>.debug_frame</name>
         <load_address>0xe50</load_address>
         <run_address>0xe50</run_address>
         <size>0x408</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-171">
         <name>.debug_frame</name>
         <load_address>0x1258</load_address>
         <run_address>0x1258</run_address>
         <size>0x1b8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-63">
         <name>.debug_frame</name>
         <load_address>0x1410</load_address>
         <run_address>0x1410</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-cf">
         <name>.debug_frame</name>
         <load_address>0x14a0</load_address>
         <run_address>0x14a0</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-47"/>
      </object_component>
      <object_component id="oc-91">
         <name>.debug_frame</name>
         <load_address>0x15a0</load_address>
         <run_address>0x15a0</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-48"/>
      </object_component>
      <object_component id="oc-60">
         <name>.debug_frame</name>
         <load_address>0x15c0</load_address>
         <run_address>0x15c0</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4a"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_frame</name>
         <load_address>0x15f8</load_address>
         <run_address>0x15f8</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4b"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_frame</name>
         <load_address>0x1620</load_address>
         <run_address>0x1620</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-ca">
         <name>.debug_frame</name>
         <load_address>0x1650</load_address>
         <run_address>0x1650</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-52"/>
      </object_component>
      <object_component id="oc-12c">
         <name>.debug_frame</name>
         <load_address>0x1680</load_address>
         <run_address>0x1680</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-ff"/>
      </object_component>
      <object_component id="oc-1e1">
         <name>.debug_frame</name>
         <load_address>0x16a0</load_address>
         <run_address>0x16a0</run_address>
         <size>0x6c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-128"/>
      </object_component>
      <object_component id="oc-1c0">
         <name>.debug_frame</name>
         <load_address>0x170c</load_address>
         <run_address>0x170c</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-129"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x304</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b5">
         <name>.debug_line</name>
         <load_address>0x304</load_address>
         <run_address>0x304</run_address>
         <size>0xd76</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_line</name>
         <load_address>0x107a</load_address>
         <run_address>0x107a</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-6f">
         <name>.debug_line</name>
         <load_address>0x1132</load_address>
         <run_address>0x1132</run_address>
         <size>0x48b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_line</name>
         <load_address>0x15bd</load_address>
         <run_address>0x15bd</run_address>
         <size>0x6be</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-ba">
         <name>.debug_line</name>
         <load_address>0x1c7b</load_address>
         <run_address>0x1c7b</run_address>
         <size>0xad6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_line</name>
         <load_address>0x2751</load_address>
         <run_address>0x2751</run_address>
         <size>0x338</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-44">
         <name>.debug_line</name>
         <load_address>0x2a89</load_address>
         <run_address>0x2a89</run_address>
         <size>0x493</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-bf">
         <name>.debug_line</name>
         <load_address>0x2f1c</load_address>
         <run_address>0x2f1c</run_address>
         <size>0x1f1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-145">
         <name>.debug_line</name>
         <load_address>0x310d</load_address>
         <run_address>0x310d</run_address>
         <size>0x179</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-17e">
         <name>.debug_line</name>
         <load_address>0x3286</load_address>
         <run_address>0x3286</run_address>
         <size>0xc1b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-156">
         <name>.debug_line</name>
         <load_address>0x3ea1</load_address>
         <run_address>0x3ea1</run_address>
         <size>0x176f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-16f">
         <name>.debug_line</name>
         <load_address>0x5610</load_address>
         <run_address>0x5610</run_address>
         <size>0xa18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-4d">
         <name>.debug_line</name>
         <load_address>0x6028</load_address>
         <run_address>0x6028</run_address>
         <size>0x1dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-d3">
         <name>.debug_line</name>
         <load_address>0x6204</load_address>
         <run_address>0x6204</run_address>
         <size>0x51a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-47"/>
      </object_component>
      <object_component id="oc-92">
         <name>.debug_line</name>
         <load_address>0x671e</load_address>
         <run_address>0x671e</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-48"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.debug_line</name>
         <load_address>0x675c</load_address>
         <run_address>0x675c</run_address>
         <size>0xfe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4a"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_line</name>
         <load_address>0x685a</load_address>
         <run_address>0x685a</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4b"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_line</name>
         <load_address>0x691a</load_address>
         <run_address>0x691a</run_address>
         <size>0x1c8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-cb">
         <name>.debug_line</name>
         <load_address>0x6ae2</load_address>
         <run_address>0x6ae2</run_address>
         <size>0x67</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-52"/>
      </object_component>
      <object_component id="oc-12b">
         <name>.debug_line</name>
         <load_address>0x6b49</load_address>
         <run_address>0x6b49</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ff"/>
      </object_component>
      <object_component id="oc-1a0">
         <name>.debug_line</name>
         <load_address>0x6b8a</load_address>
         <run_address>0x6b8a</run_address>
         <size>0x107</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10f"/>
      </object_component>
      <object_component id="oc-1bb">
         <name>.debug_line</name>
         <load_address>0x6c91</load_address>
         <run_address>0x6c91</run_address>
         <size>0xb9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-112"/>
      </object_component>
      <object_component id="oc-192">
         <name>.debug_line</name>
         <load_address>0x6d4a</load_address>
         <run_address>0x6d4a</run_address>
         <size>0xe0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-113"/>
      </object_component>
      <object_component id="oc-1cc">
         <name>.debug_line</name>
         <load_address>0x6e2a</load_address>
         <run_address>0x6e2a</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-116"/>
      </object_component>
      <object_component id="oc-198">
         <name>.debug_line</name>
         <load_address>0x6eea</load_address>
         <run_address>0x6eea</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-118"/>
      </object_component>
      <object_component id="oc-112">
         <name>.debug_line</name>
         <load_address>0x6fa2</load_address>
         <run_address>0x6fa2</run_address>
         <size>0xbc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11a"/>
      </object_component>
      <object_component id="oc-115">
         <name>.debug_line</name>
         <load_address>0x705e</load_address>
         <run_address>0x705e</run_address>
         <size>0xb4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11b"/>
      </object_component>
      <object_component id="oc-1ce">
         <name>.debug_line</name>
         <load_address>0x7112</load_address>
         <run_address>0x7112</run_address>
         <size>0xc7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11e"/>
      </object_component>
      <object_component id="oc-1c7">
         <name>.debug_line</name>
         <load_address>0x71d9</load_address>
         <run_address>0x71d9</run_address>
         <size>0xc7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11f"/>
      </object_component>
      <object_component id="oc-19c">
         <name>.debug_line</name>
         <load_address>0x72a0</load_address>
         <run_address>0x72a0</run_address>
         <size>0xcc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-120"/>
      </object_component>
      <object_component id="oc-57">
         <name>.debug_line</name>
         <load_address>0x736c</load_address>
         <run_address>0x736c</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-121"/>
      </object_component>
      <object_component id="oc-1d8">
         <name>.debug_line</name>
         <load_address>0x7410</load_address>
         <run_address>0x7410</run_address>
         <size>0x104</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-125"/>
      </object_component>
      <object_component id="oc-1e2">
         <name>.debug_line</name>
         <load_address>0x7514</load_address>
         <run_address>0x7514</run_address>
         <size>0x2ef</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-128"/>
      </object_component>
      <object_component id="oc-1bf">
         <name>.debug_line</name>
         <load_address>0x7803</load_address>
         <run_address>0x7803</run_address>
         <size>0xb5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-129"/>
      </object_component>
      <object_component id="oc-aa">
         <name>.debug_line</name>
         <load_address>0x78b8</load_address>
         <run_address>0x78b8</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12b"/>
      </object_component>
      <object_component id="oc-1b0">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-182">
         <name>.debug_loc</name>
         <load_address>0x13</load_address>
         <run_address>0x13</run_address>
         <size>0x816</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-15a">
         <name>.debug_loc</name>
         <load_address>0x829</load_address>
         <run_address>0x829</run_address>
         <size>0x1a27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-16d">
         <name>.debug_loc</name>
         <load_address>0x2250</load_address>
         <run_address>0x2250</run_address>
         <size>0x7bc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.debug_loc</name>
         <load_address>0x2a0c</load_address>
         <run_address>0x2a0c</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-d2">
         <name>.debug_loc</name>
         <load_address>0x2ae4</load_address>
         <run_address>0x2ae4</run_address>
         <size>0x424</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-47"/>
      </object_component>
      <object_component id="oc-5c">
         <name>.debug_loc</name>
         <load_address>0x2f08</load_address>
         <run_address>0x2f08</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4a"/>
      </object_component>
      <object_component id="oc-5a">
         <name>.debug_loc</name>
         <load_address>0x3074</load_address>
         <run_address>0x3074</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4b"/>
      </object_component>
      <object_component id="oc-52">
         <name>.debug_loc</name>
         <load_address>0x30e3</load_address>
         <run_address>0x30e3</run_address>
         <size>0x167</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.debug_loc</name>
         <load_address>0x324a</load_address>
         <run_address>0x324a</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-52"/>
      </object_component>
      <object_component id="oc-1de">
         <name>.debug_loc</name>
         <load_address>0x3270</load_address>
         <run_address>0x3270</run_address>
         <size>0x363</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-128"/>
      </object_component>
      <object_component id="oc-1d4">
         <name>.debug_loc</name>
         <load_address>0x35d3</load_address>
         <run_address>0x35d3</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-129"/>
      </object_component>
      <object_component id="oc-19f">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10f"/>
      </object_component>
      <object_component id="oc-1ba">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-112"/>
      </object_component>
      <object_component id="oc-193">
         <name>.debug_aranges</name>
         <load_address>0x40</load_address>
         <run_address>0x40</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-113"/>
      </object_component>
      <object_component id="oc-1cb">
         <name>.debug_aranges</name>
         <load_address>0x60</load_address>
         <run_address>0x60</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-116"/>
      </object_component>
      <object_component id="oc-196">
         <name>.debug_aranges</name>
         <load_address>0x80</load_address>
         <run_address>0x80</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-118"/>
      </object_component>
      <object_component id="oc-111">
         <name>.debug_aranges</name>
         <load_address>0xa0</load_address>
         <run_address>0xa0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11a"/>
      </object_component>
      <object_component id="oc-116">
         <name>.debug_aranges</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11b"/>
      </object_component>
      <object_component id="oc-1d0">
         <name>.debug_aranges</name>
         <load_address>0xe0</load_address>
         <run_address>0xe0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11e"/>
      </object_component>
      <object_component id="oc-1c6">
         <name>.debug_aranges</name>
         <load_address>0x100</load_address>
         <run_address>0x100</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11f"/>
      </object_component>
      <object_component id="oc-19b">
         <name>.debug_aranges</name>
         <load_address>0x120</load_address>
         <run_address>0x120</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-120"/>
      </object_component>
      <object_component id="oc-56">
         <name>.debug_aranges</name>
         <load_address>0x140</load_address>
         <run_address>0x140</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-121"/>
      </object_component>
      <object_component id="oc-1d7">
         <name>.debug_aranges</name>
         <load_address>0x160</load_address>
         <run_address>0x160</run_address>
         <size>0x30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-125"/>
      </object_component>
      <object_component id="oc-a9">
         <name>.debug_aranges</name>
         <load_address>0x190</load_address>
         <run_address>0x190</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12b"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x25a8</size>
         <contents>
            <object_component_ref idref="oc-b6"/>
            <object_component_ref idref="oc-6b"/>
            <object_component_ref idref="oc-c5"/>
            <object_component_ref idref="oc-e8"/>
            <object_component_ref idref="oc-1a1"/>
            <object_component_ref idref="oc-10b"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-113"/>
            <object_component_ref idref="oc-15b"/>
            <object_component_ref idref="oc-166"/>
            <object_component_ref idref="oc-19d"/>
            <object_component_ref idref="oc-e7"/>
            <object_component_ref idref="oc-ea"/>
            <object_component_ref idref="oc-eb"/>
            <object_component_ref idref="oc-191"/>
            <object_component_ref idref="oc-154"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-c0"/>
            <object_component_ref idref="oc-1e3"/>
            <object_component_ref idref="oc-102"/>
            <object_component_ref idref="oc-105"/>
            <object_component_ref idref="oc-b1"/>
            <object_component_ref idref="oc-1dd"/>
            <object_component_ref idref="oc-1cd"/>
            <object_component_ref idref="oc-1be"/>
            <object_component_ref idref="oc-1c5"/>
            <object_component_ref idref="oc-c2"/>
            <object_component_ref idref="oc-93"/>
            <object_component_ref idref="oc-199"/>
            <object_component_ref idref="oc-73"/>
            <object_component_ref idref="oc-ef"/>
            <object_component_ref idref="oc-10e"/>
            <object_component_ref idref="oc-174"/>
            <object_component_ref idref="oc-172"/>
            <object_component_ref idref="oc-ee"/>
            <object_component_ref idref="oc-f0"/>
            <object_component_ref idref="oc-183"/>
            <object_component_ref idref="oc-106"/>
            <object_component_ref idref="oc-f1"/>
            <object_component_ref idref="oc-f2"/>
            <object_component_ref idref="oc-ec"/>
            <object_component_ref idref="oc-1c9"/>
            <object_component_ref idref="oc-15c"/>
            <object_component_ref idref="oc-ed"/>
            <object_component_ref idref="oc-10f"/>
            <object_component_ref idref="oc-1da"/>
            <object_component_ref idref="oc-ce"/>
            <object_component_ref idref="oc-1d5"/>
            <object_component_ref idref="oc-85"/>
            <object_component_ref idref="oc-1b8"/>
            <object_component_ref idref="oc-195"/>
            <object_component_ref idref="oc-149"/>
            <object_component_ref idref="oc-79"/>
            <object_component_ref idref="oc-185"/>
            <object_component_ref idref="oc-fd"/>
            <object_component_ref idref="oc-109"/>
            <object_component_ref idref="oc-10c"/>
            <object_component_ref idref="oc-10a"/>
            <object_component_ref idref="oc-10d"/>
            <object_component_ref idref="oc-fe"/>
            <object_component_ref idref="oc-1b1"/>
            <object_component_ref idref="oc-18b"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-114"/>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-184"/>
            <object_component_ref idref="oc-e9"/>
            <object_component_ref idref="oc-148"/>
            <object_component_ref idref="oc-173"/>
            <object_component_ref idref="oc-9f"/>
            <object_component_ref idref="oc-14d"/>
            <object_component_ref idref="oc-14e"/>
            <object_component_ref idref="oc-146"/>
            <object_component_ref idref="oc-14b"/>
            <object_component_ref idref="oc-152"/>
            <object_component_ref idref="oc-167"/>
            <object_component_ref idref="oc-15e"/>
            <object_component_ref idref="oc-155"/>
            <object_component_ref idref="oc-175"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-147"/>
            <object_component_ref idref="oc-13e"/>
            <object_component_ref idref="oc-9e"/>
            <object_component_ref idref="oc-14a"/>
            <object_component_ref idref="oc-13a"/>
            <object_component_ref idref="oc-fc"/>
            <object_component_ref idref="oc-150"/>
            <object_component_ref idref="oc-14c"/>
            <object_component_ref idref="oc-186"/>
            <object_component_ref idref="oc-141"/>
            <object_component_ref idref="oc-18e"/>
            <object_component_ref idref="oc-13d"/>
            <object_component_ref idref="oc-151"/>
            <object_component_ref idref="oc-13f"/>
            <object_component_ref idref="oc-13b"/>
            <object_component_ref idref="oc-15d"/>
            <object_component_ref idref="oc-140"/>
            <object_component_ref idref="oc-13c"/>
            <object_component_ref idref="oc-a0"/>
            <object_component_ref idref="oc-18d"/>
            <object_component_ref idref="oc-176"/>
            <object_component_ref idref="oc-bb"/>
            <object_component_ref idref="oc-c1"/>
            <object_component_ref idref="oc-c3"/>
            <object_component_ref idref="oc-c4"/>
            <object_component_ref idref="oc-5b"/>
            <object_component_ref idref="oc-fb"/>
            <object_component_ref idref="oc-14f"/>
            <object_component_ref idref="oc-153"/>
            <object_component_ref idref="oc-160"/>
            <object_component_ref idref="oc-161"/>
            <object_component_ref idref="oc-81"/>
            <object_component_ref idref="oc-78"/>
            <object_component_ref idref="oc-17d"/>
            <object_component_ref idref="oc-72"/>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-77"/>
            <object_component_ref idref="oc-16c"/>
            <object_component_ref idref="oc-a7"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-18c"/>
            <object_component_ref idref="oc-15f"/>
            <object_component_ref idref="oc-f3"/>
            <object_component_ref idref="oc-a5"/>
            <object_component_ref idref="oc-142"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-c7"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-129"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-8f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x2de0</load_address>
         <run_address>0x2de0</run_address>
         <size>0x38</size>
         <contents>
            <object_component_ref idref="oc-229"/>
            <object_component_ref idref="oc-227"/>
            <object_component_ref idref="oc-22a"/>
            <object_component_ref idref="oc-228"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0x2668</load_address>
         <run_address>0x2668</run_address>
         <size>0x778</size>
         <contents>
            <object_component_ref idref="oc-18f"/>
            <object_component_ref idref="oc-16b"/>
            <object_component_ref idref="oc-169"/>
            <object_component_ref idref="oc-c6"/>
            <object_component_ref idref="oc-118"/>
            <object_component_ref idref="oc-11b"/>
            <object_component_ref idref="oc-126"/>
            <object_component_ref idref="oc-11a"/>
            <object_component_ref idref="oc-11e"/>
            <object_component_ref idref="oc-120"/>
            <object_component_ref idref="oc-11c"/>
            <object_component_ref idref="oc-128"/>
            <object_component_ref idref="oc-11d"/>
            <object_component_ref idref="oc-127"/>
            <object_component_ref idref="oc-11f"/>
            <object_component_ref idref="oc-119"/>
            <object_component_ref idref="oc-188"/>
            <object_component_ref idref="oc-18a"/>
            <object_component_ref idref="oc-178"/>
            <object_component_ref idref="oc-17a"/>
            <object_component_ref idref="oc-17c"/>
            <object_component_ref idref="oc-121"/>
            <object_component_ref idref="oc-124"/>
            <object_component_ref idref="oc-163"/>
            <object_component_ref idref="oc-165"/>
            <object_component_ref idref="oc-162"/>
            <object_component_ref idref="oc-164"/>
            <object_component_ref idref="oc-16a"/>
            <object_component_ref idref="oc-168"/>
            <object_component_ref idref="oc-125"/>
            <object_component_ref idref="oc-187"/>
            <object_component_ref idref="oc-189"/>
            <object_component_ref idref="oc-177"/>
            <object_component_ref idref="oc-179"/>
            <object_component_ref idref="oc-17b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-1f1"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x202001d8</run_address>
         <size>0x13</size>
         <contents>
            <object_component_ref idref="oc-a2"/>
            <object_component_ref idref="oc-a1"/>
            <object_component_ref idref="oc-7b"/>
            <object_component_ref idref="oc-7c"/>
            <object_component_ref idref="oc-122"/>
            <object_component_ref idref="oc-123"/>
            <object_component_ref idref="oc-107"/>
            <object_component_ref idref="oc-108"/>
            <object_component_ref idref="oc-ff"/>
            <object_component_ref idref="oc-74"/>
            <object_component_ref idref="oc-82"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x20200000</run_address>
         <size>0x1d5</size>
         <contents>
            <object_component_ref idref="oc-7d"/>
            <object_component_ref idref="oc-7a"/>
            <object_component_ref idref="oc-f4"/>
            <object_component_ref idref="oc-f5"/>
            <object_component_ref idref="oc-f6"/>
            <object_component_ref idref="oc-f7"/>
            <object_component_ref idref="oc-f8"/>
            <object_component_ref idref="oc-a6"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20203e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-22c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1e8" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1e9" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1ea" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1eb" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1ec" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1ed" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1ef" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-20b" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1c98</size>
         <contents>
            <object_component_ref idref="oc-70"/>
            <object_component_ref idref="oc-f9"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-a3"/>
            <object_component_ref idref="oc-7e"/>
            <object_component_ref idref="oc-100"/>
            <object_component_ref idref="oc-75"/>
            <object_component_ref idref="oc-83"/>
            <object_component_ref idref="oc-103"/>
            <object_component_ref idref="oc-1ae"/>
            <object_component_ref idref="oc-1b6"/>
            <object_component_ref idref="oc-1b2"/>
            <object_component_ref idref="oc-1b4"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-133"/>
            <object_component_ref idref="oc-af"/>
            <object_component_ref idref="oc-88"/>
            <object_component_ref idref="oc-58"/>
            <object_component_ref idref="oc-50"/>
            <object_component_ref idref="oc-12d"/>
            <object_component_ref idref="oc-1a3"/>
            <object_component_ref idref="oc-1c4"/>
            <object_component_ref idref="oc-1d1"/>
            <object_component_ref idref="oc-1bc"/>
            <object_component_ref idref="oc-1dc"/>
            <object_component_ref idref="oc-1bd"/>
            <object_component_ref idref="oc-190"/>
            <object_component_ref idref="oc-1a2"/>
            <object_component_ref idref="oc-1e4"/>
            <object_component_ref idref="oc-1db"/>
            <object_component_ref idref="oc-1c3"/>
            <object_component_ref idref="oc-87"/>
            <object_component_ref idref="oc-1e5"/>
            <object_component_ref idref="oc-1e6"/>
            <object_component_ref idref="oc-1d2"/>
            <object_component_ref idref="oc-e0"/>
            <object_component_ref idref="oc-22e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-20d" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1064a</size>
         <contents>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-b2"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-6e"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-b7"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-bd"/>
            <object_component_ref idref="oc-143"/>
            <object_component_ref idref="oc-181"/>
            <object_component_ref idref="oc-159"/>
            <object_component_ref idref="oc-16e"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-d0"/>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-5d"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-c9"/>
            <object_component_ref idref="oc-12a"/>
            <object_component_ref idref="oc-19e"/>
            <object_component_ref idref="oc-1b9"/>
            <object_component_ref idref="oc-194"/>
            <object_component_ref idref="oc-1ca"/>
            <object_component_ref idref="oc-197"/>
            <object_component_ref idref="oc-110"/>
            <object_component_ref idref="oc-117"/>
            <object_component_ref idref="oc-1cf"/>
            <object_component_ref idref="oc-1c8"/>
            <object_component_ref idref="oc-19a"/>
            <object_component_ref idref="oc-55"/>
            <object_component_ref idref="oc-1d9"/>
            <object_component_ref idref="oc-1e0"/>
            <object_component_ref idref="oc-1c1"/>
            <object_component_ref idref="oc-a8"/>
            <object_component_ref idref="oc-22d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-20f" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xcf8</size>
         <contents>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-b3"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-6d"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-b8"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-be"/>
            <object_component_ref idref="oc-17f"/>
            <object_component_ref idref="oc-157"/>
            <object_component_ref idref="oc-170"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-d1"/>
            <object_component_ref idref="oc-5e"/>
            <object_component_ref idref="oc-53"/>
            <object_component_ref idref="oc-c8"/>
            <object_component_ref idref="oc-1d6"/>
            <object_component_ref idref="oc-1df"/>
            <object_component_ref idref="oc-1c2"/>
            <object_component_ref idref="oc-ab"/>
         </contents>
      </logical_group>
      <logical_group id="lg-211" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xab4d</size>
         <contents>
            <object_component_ref idref="oc-71"/>
            <object_component_ref idref="oc-fa"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-a4"/>
            <object_component_ref idref="oc-7f"/>
            <object_component_ref idref="oc-101"/>
            <object_component_ref idref="oc-76"/>
            <object_component_ref idref="oc-84"/>
            <object_component_ref idref="oc-104"/>
            <object_component_ref idref="oc-1af"/>
            <object_component_ref idref="oc-1b7"/>
            <object_component_ref idref="oc-1b3"/>
            <object_component_ref idref="oc-1b5"/>
            <object_component_ref idref="oc-4c"/>
            <object_component_ref idref="oc-134"/>
            <object_component_ref idref="oc-b0"/>
            <object_component_ref idref="oc-89"/>
            <object_component_ref idref="oc-59"/>
            <object_component_ref idref="oc-51"/>
            <object_component_ref idref="oc-12e"/>
            <object_component_ref idref="oc-1a4"/>
            <object_component_ref idref="oc-1e7"/>
            <object_component_ref idref="oc-1d3"/>
         </contents>
      </logical_group>
      <logical_group id="lg-213" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x173c</size>
         <contents>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-b4"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-6c"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-b9"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-bc"/>
            <object_component_ref idref="oc-144"/>
            <object_component_ref idref="oc-180"/>
            <object_component_ref idref="oc-158"/>
            <object_component_ref idref="oc-171"/>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-cf"/>
            <object_component_ref idref="oc-91"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-ca"/>
            <object_component_ref idref="oc-12c"/>
            <object_component_ref idref="oc-1e1"/>
            <object_component_ref idref="oc-1c0"/>
         </contents>
      </logical_group>
      <logical_group id="lg-215" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x7958</size>
         <contents>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-b5"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-6f"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-ba"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-bf"/>
            <object_component_ref idref="oc-145"/>
            <object_component_ref idref="oc-17e"/>
            <object_component_ref idref="oc-156"/>
            <object_component_ref idref="oc-16f"/>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-d3"/>
            <object_component_ref idref="oc-92"/>
            <object_component_ref idref="oc-5f"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-cb"/>
            <object_component_ref idref="oc-12b"/>
            <object_component_ref idref="oc-1a0"/>
            <object_component_ref idref="oc-1bb"/>
            <object_component_ref idref="oc-192"/>
            <object_component_ref idref="oc-1cc"/>
            <object_component_ref idref="oc-198"/>
            <object_component_ref idref="oc-112"/>
            <object_component_ref idref="oc-115"/>
            <object_component_ref idref="oc-1ce"/>
            <object_component_ref idref="oc-1c7"/>
            <object_component_ref idref="oc-19c"/>
            <object_component_ref idref="oc-57"/>
            <object_component_ref idref="oc-1d8"/>
            <object_component_ref idref="oc-1e2"/>
            <object_component_ref idref="oc-1bf"/>
            <object_component_ref idref="oc-aa"/>
         </contents>
      </logical_group>
      <logical_group id="lg-217" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x35f3</size>
         <contents>
            <object_component_ref idref="oc-1b0"/>
            <object_component_ref idref="oc-182"/>
            <object_component_ref idref="oc-15a"/>
            <object_component_ref idref="oc-16d"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-d2"/>
            <object_component_ref idref="oc-5c"/>
            <object_component_ref idref="oc-5a"/>
            <object_component_ref idref="oc-52"/>
            <object_component_ref idref="oc-cd"/>
            <object_component_ref idref="oc-1de"/>
            <object_component_ref idref="oc-1d4"/>
         </contents>
      </logical_group>
      <logical_group id="lg-221" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1b8</size>
         <contents>
            <object_component_ref idref="oc-19f"/>
            <object_component_ref idref="oc-1ba"/>
            <object_component_ref idref="oc-193"/>
            <object_component_ref idref="oc-1cb"/>
            <object_component_ref idref="oc-196"/>
            <object_component_ref idref="oc-111"/>
            <object_component_ref idref="oc-116"/>
            <object_component_ref idref="oc-1d0"/>
            <object_component_ref idref="oc-1c6"/>
            <object_component_ref idref="oc-19b"/>
            <object_component_ref idref="oc-56"/>
            <object_component_ref idref="oc-1d7"/>
            <object_component_ref idref="oc-a9"/>
         </contents>
      </logical_group>
      <logical_group id="lg-22b" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-239" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2e18</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-23a" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0x1eb</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-f"/>
            <logical_group_ref idref="lg-e"/>
         </contents>
      </load_segment>
      <load_segment id="lg-23b" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20203e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x8000</length>
         <used_space>0x2e18</used_space>
         <unused_space>0x51e8</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0x25a8</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x2668</start_address>
               <size>0x778</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x2de0</start_address>
               <size>0x38</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0x2e18</start_address>
               <size>0x51e8</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x4000</length>
         <used_space>0x3e8</used_space>
         <unused_space>0x3c18</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-1ed"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-1ef"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x1d5</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <available_space>
               <start_address>0x202001d5</start_address>
               <size>0x3</size>
            </available_space>
            <allocated_space>
               <start_address>0x202001d8</start_address>
               <size>0x13</size>
               <logical_group_ref idref="lg-e"/>
            </allocated_space>
            <available_space>
               <start_address>0x202001eb</start_address>
               <size>0x3c15</size>
            </available_space>
            <allocated_space>
               <start_address>0x20203e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0xff</length>
         <used_space>0x0</used_space>
         <unused_space>0xff</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.data</name>
            <load_address>0x2de0</load_address>
            <load_size>0xf</load_size>
            <run_address>0x202001d8</run_address>
            <run_size>0x13</run_size>
            <compression>lzss</compression>
         </cprec>
         <cprec>
            <name>.bss</name>
            <load_address>0x2dfc</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0x1d5</run_size>
            <compression>zero_init</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_decompress_lzss</name>
      </handler>
      <handler>
         <index>0x1</index>
         <name>__TI_decompress_none</name>
      </handler>
      <handler>
         <index>0x2</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0x2e04</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0x2e14</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0x2e14</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0x2df0</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0x2dfc</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20204000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-43">
         <name>main</name>
         <value>0x185d</value>
         <object_component_ref idref="oc-93"/>
      </symbol>
      <symbol id="sm-44">
         <name>timerA_callback</name>
         <value>0x190b</value>
         <object_component_ref idref="oc-73"/>
      </symbol>
      <symbol id="sm-45">
         <name>timerB_callback</name>
         <value>0x1dc3</value>
         <object_component_ref idref="oc-85"/>
      </symbol>
      <symbol id="sm-46">
         <name>GROUP1_IRQHandler</name>
         <value>0x263b</value>
         <object_component_ref idref="oc-34"/>
      </symbol>
      <symbol id="sm-127">
         <name>SYSCFG_DL_init</name>
         <value>0x1665</value>
         <object_component_ref idref="oc-b1"/>
      </symbol>
      <symbol id="sm-128">
         <name>SYSCFG_DL_initPower</name>
         <value>0x1121</value>
         <object_component_ref idref="oc-e7"/>
      </symbol>
      <symbol id="sm-129">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0x759</value>
         <object_component_ref idref="oc-e8"/>
      </symbol>
      <symbol id="sm-12a">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0x20f1</value>
         <object_component_ref idref="oc-e9"/>
      </symbol>
      <symbol id="sm-12b">
         <name>SYSCFG_DL_PWM_6_init</name>
         <value>0x11f5</value>
         <object_component_ref idref="oc-ea"/>
      </symbol>
      <symbol id="sm-12c">
         <name>SYSCFG_DL_PWM_7_init</name>
         <value>0x1281</value>
         <object_component_ref idref="oc-eb"/>
      </symbol>
      <symbol id="sm-12d">
         <name>SYSCFG_DL_TIMER_8_init</name>
         <value>0x1bdd</value>
         <object_component_ref idref="oc-ec"/>
      </symbol>
      <symbol id="sm-12e">
         <name>SYSCFG_DL_TIMER_12_init</name>
         <value>0x1c99</value>
         <object_component_ref idref="oc-ed"/>
      </symbol>
      <symbol id="sm-12f">
         <name>SYSCFG_DL_UART_0_init</name>
         <value>0x1a45</value>
         <object_component_ref idref="oc-ee"/>
      </symbol>
      <symbol id="sm-130">
         <name>SYSCFG_DL_UART_1_init</name>
         <value>0x190d</value>
         <object_component_ref idref="oc-ef"/>
      </symbol>
      <symbol id="sm-131">
         <name>SYSCFG_DL_UART_3_init</name>
         <value>0x1a8d</value>
         <object_component_ref idref="oc-f0"/>
      </symbol>
      <symbol id="sm-132">
         <name>SYSCFG_DL_SPI_IMU660RB_init</name>
         <value>0x1b5d</value>
         <object_component_ref idref="oc-f1"/>
      </symbol>
      <symbol id="sm-133">
         <name>SYSCFG_DL_TFT_SPI_init</name>
         <value>0x1b9d</value>
         <object_component_ref idref="oc-f2"/>
      </symbol>
      <symbol id="sm-134">
         <name>SYSCFG_DL_SYSTICK_init</name>
         <value>0x2615</value>
         <object_component_ref idref="oc-f3"/>
      </symbol>
      <symbol id="sm-135">
         <name>gPWM_6Backup</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-136">
         <name>gPWM_7Backup</name>
         <value>0x202000a0</value>
      </symbol>
      <symbol id="sm-137">
         <name>gUART_3Backup</name>
         <value>0x20200140</value>
      </symbol>
      <symbol id="sm-138">
         <name>gSPI_IMU660RBBackup</name>
         <value>0x20200170</value>
      </symbol>
      <symbol id="sm-139">
         <name>gTFT_SPIBackup</name>
         <value>0x20200198</value>
      </symbol>
      <symbol id="sm-144">
         <name>Default_Handler</name>
         <value>0x2653</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-145">
         <name>Reset_Handler</name>
         <value>0x265b</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-146">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-147">
         <name>NMI_Handler</name>
         <value>0x2653</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-148">
         <name>HardFault_Handler</name>
         <value>0x2653</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-149">
         <name>SVC_Handler</name>
         <value>0x2653</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-14a">
         <name>PendSV_Handler</name>
         <value>0x2653</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-14b">
         <name>SysTick_Handler</name>
         <value>0x2653</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-14c">
         <name>GROUP0_IRQHandler</name>
         <value>0x2653</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-14d">
         <name>ADC0_IRQHandler</name>
         <value>0x2653</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-14e">
         <name>ADC1_IRQHandler</name>
         <value>0x2653</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-14f">
         <name>CANFD0_IRQHandler</name>
         <value>0x2653</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-150">
         <name>DAC0_IRQHandler</name>
         <value>0x2653</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-151">
         <name>SPI0_IRQHandler</name>
         <value>0x2653</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-152">
         <name>SPI1_IRQHandler</name>
         <value>0x2653</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-153">
         <name>UART1_IRQHandler</name>
         <value>0x2653</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-154">
         <name>UART2_IRQHandler</name>
         <value>0x2653</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-155">
         <name>TIMG0_IRQHandler</name>
         <value>0x2653</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-156">
         <name>TIMG6_IRQHandler</name>
         <value>0x2653</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-157">
         <name>TIMA0_IRQHandler</name>
         <value>0x2653</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-158">
         <name>TIMA1_IRQHandler</name>
         <value>0x2653</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-159">
         <name>TIMG7_IRQHandler</name>
         <value>0x2653</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-15a">
         <name>I2C0_IRQHandler</name>
         <value>0x2653</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-15b">
         <name>I2C1_IRQHandler</name>
         <value>0x2653</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-15c">
         <name>AES_IRQHandler</name>
         <value>0x2653</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-15d">
         <name>RTC_IRQHandler</name>
         <value>0x2653</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-15e">
         <name>DMA_IRQHandler</name>
         <value>0x2653</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-173">
         <name>left_counter</name>
         <value>0x202001dc</value>
         <object_component_ref idref="oc-a2"/>
      </symbol>
      <symbol id="sm-174">
         <name>right_counter</name>
         <value>0x202001de</value>
         <object_component_ref idref="oc-a1"/>
      </symbol>
      <symbol id="sm-175">
         <name>encoder_exti_callback</name>
         <value>0x349</value>
         <object_component_ref idref="oc-6b"/>
      </symbol>
      <symbol id="sm-1b9">
         <name>openmv_init</name>
         <value>0x2491</value>
         <object_component_ref idref="oc-c1"/>
      </symbol>
      <symbol id="sm-1ba">
         <name>openmv_analysis</name>
         <value>0x1e6d</value>
         <object_component_ref idref="oc-79"/>
      </symbol>
      <symbol id="sm-1bb">
         <name>openmvData</name>
         <value>0x202001c0</value>
      </symbol>
      <symbol id="sm-1bc">
         <name>UART3_IRQHandler</name>
         <value>0xc31</value>
         <object_component_ref idref="oc-3e"/>
      </symbol>
      <symbol id="sm-1bd">
         <name>openmv_is_data_valid</name>
         <value>0x1961</value>
         <object_component_ref idref="oc-10e"/>
      </symbol>
      <symbol id="sm-1be">
         <name>openmv_display_data</name>
         <value>0x555</value>
         <object_component_ref idref="oc-c5"/>
      </symbol>
      <symbol id="sm-1f8">
         <name>tft180_write_8bit_data</name>
         <value>0x1fb1</value>
         <object_component_ref idref="oc-fe"/>
      </symbol>
      <symbol id="sm-1f9">
         <name>tft180_write_16bit_data</name>
         <value>0x1b19</value>
         <object_component_ref idref="oc-106"/>
      </symbol>
      <symbol id="sm-1fa">
         <name>tft180_clear_color</name>
         <value>0x1499</value>
         <object_component_ref idref="oc-c0"/>
      </symbol>
      <symbol id="sm-1fb">
         <name>tft180_init</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-b6"/>
      </symbol>
      <symbol id="sm-1fc">
         <name>tft180_show_char_color</name>
         <value>0xb01</value>
         <object_component_ref idref="oc-10b"/>
      </symbol>
      <symbol id="sm-1fd">
         <name>ascii_font_8x16</name>
         <value>0x2668</value>
         <object_component_ref idref="oc-18f"/>
      </symbol>
      <symbol id="sm-1fe">
         <name>func_float_to_str</name>
         <value>0x945</value>
         <object_component_ref idref="oc-1a1"/>
      </symbol>
      <symbol id="sm-1ff">
         <name>tft180_show_num_color</name>
         <value>0xd51</value>
         <object_component_ref idref="oc-113"/>
      </symbol>
      <symbol id="sm-200">
         <name>tft180_show_string_color</name>
         <value>0x17fb</value>
         <object_component_ref idref="oc-c2"/>
      </symbol>
      <symbol id="sm-221">
         <name>timerA_init</name>
         <value>0x24a7</value>
         <object_component_ref idref="oc-c3"/>
      </symbol>
      <symbol id="sm-222">
         <name>timerB_init</name>
         <value>0x24bd</value>
         <object_component_ref idref="oc-c4"/>
      </symbol>
      <symbol id="sm-223">
         <name>TIMG8_IRQHandler</name>
         <value>0x202d</value>
         <object_component_ref idref="oc-39"/>
      </symbol>
      <symbol id="sm-224">
         <name>TIMG12_IRQHandler</name>
         <value>0x226d</value>
         <object_component_ref idref="oc-48"/>
      </symbol>
      <symbol id="sm-225">
         <name>get_system_time_ms</name>
         <value>0x2625</value>
         <object_component_ref idref="oc-a5"/>
      </symbol>
      <symbol id="sm-236">
         <name>UART0_IRQHandler</name>
         <value>0x2055</value>
         <object_component_ref idref="oc-43"/>
      </symbol>
      <symbol id="sm-237">
         <name>uart_data</name>
         <value>0x202001ea</value>
         <object_component_ref idref="oc-82"/>
      </symbol>
      <symbol id="sm-243">
         <name>delay_ms</name>
         <value>0x247b</value>
         <object_component_ref idref="oc-bb"/>
      </symbol>
      <symbol id="sm-244">
         <name>delay_us</name>
         <value>0x1585</value>
         <object_component_ref idref="oc-102"/>
      </symbol>
      <symbol id="sm-24a">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-24b">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-24c">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-24d">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-24e">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-24f">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-250">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-251">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-252">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-25b">
         <name>DL_Common_delayCycles</name>
         <value>0x2631</value>
         <object_component_ref idref="oc-142"/>
      </symbol>
      <symbol id="sm-268">
         <name>DL_SPI_init</name>
         <value>0x1ad5</value>
         <object_component_ref idref="oc-183"/>
      </symbol>
      <symbol id="sm-269">
         <name>DL_SPI_setClockConfig</name>
         <value>0x2575</value>
         <object_component_ref idref="oc-17d"/>
      </symbol>
      <symbol id="sm-285">
         <name>DL_Timer_setClockConfig</name>
         <value>0x2235</value>
         <object_component_ref idref="oc-155"/>
      </symbol>
      <symbol id="sm-286">
         <name>DL_Timer_initTimerMode</name>
         <value>0xf61</value>
         <object_component_ref idref="oc-166"/>
      </symbol>
      <symbol id="sm-287">
         <name>DL_Timer_setCaptureCompareValue</name>
         <value>0x2605</value>
         <object_component_ref idref="oc-15f"/>
      </symbol>
      <symbol id="sm-288">
         <name>DL_Timer_setCaptCompUpdateMethod</name>
         <value>0x2219</value>
         <object_component_ref idref="oc-15e"/>
      </symbol>
      <symbol id="sm-289">
         <name>DL_Timer_setCaptureCompareOutCtl</name>
         <value>0x23f1</value>
         <object_component_ref idref="oc-15d"/>
      </symbol>
      <symbol id="sm-28a">
         <name>DL_Timer_initFourCCPWMMode</name>
         <value>0xe5d</value>
         <object_component_ref idref="oc-15b"/>
      </symbol>
      <symbol id="sm-297">
         <name>DL_UART_init</name>
         <value>0x19fd</value>
         <object_component_ref idref="oc-172"/>
      </symbol>
      <symbol id="sm-298">
         <name>DL_UART_setClockConfig</name>
         <value>0x25bd</value>
         <object_component_ref idref="oc-16c"/>
      </symbol>
      <symbol id="sm-2ac">
         <name>_c_int00_noargs</name>
         <value>0x20a5</value>
         <object_component_ref idref="oc-64"/>
      </symbol>
      <symbol id="sm-2ad">
         <name>__stack</name>
         <value>0x20203e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-2b9">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0x1d4d</value>
         <object_component_ref idref="oc-ce"/>
      </symbol>
      <symbol id="sm-2c1">
         <name>_system_pre_init</name>
         <value>0x265f</value>
         <object_component_ref idref="oc-8f"/>
      </symbol>
      <symbol id="sm-2cc">
         <name>__TI_zero_init_nomemset</name>
         <value>0x24d3</value>
         <object_component_ref idref="oc-5b"/>
      </symbol>
      <symbol id="sm-2d5">
         <name>__TI_decompress_none</name>
         <value>0x25e1</value>
         <object_component_ref idref="oc-20"/>
      </symbol>
      <symbol id="sm-2e0">
         <name>__TI_decompress_lzss</name>
         <value>0x141d</value>
         <object_component_ref idref="oc-1c"/>
      </symbol>
      <symbol id="sm-2ef">
         <name>abort</name>
         <value>0x264d</value>
         <object_component_ref idref="oc-c7"/>
      </symbol>
      <symbol id="sm-303">
         <name>HOSTexit</name>
         <value>0x2657</value>
         <object_component_ref idref="oc-129"/>
      </symbol>
      <symbol id="sm-304">
         <name>C$$EXIT</name>
         <value>0x2656</value>
         <object_component_ref idref="oc-129"/>
      </symbol>
      <symbol id="sm-319">
         <name>__aeabi_fadd</name>
         <value>0x1053</value>
         <object_component_ref idref="oc-19d"/>
      </symbol>
      <symbol id="sm-31a">
         <name>__addsf3</name>
         <value>0x1053</value>
         <object_component_ref idref="oc-19d"/>
      </symbol>
      <symbol id="sm-31b">
         <name>__aeabi_fsub</name>
         <value>0x1049</value>
         <object_component_ref idref="oc-19d"/>
      </symbol>
      <symbol id="sm-31c">
         <name>__subsf3</name>
         <value>0x1049</value>
         <object_component_ref idref="oc-19d"/>
      </symbol>
      <symbol id="sm-322">
         <name>__muldsi3</name>
         <value>0x1dc5</value>
         <object_component_ref idref="oc-1b8"/>
      </symbol>
      <symbol id="sm-328">
         <name>__aeabi_fmul</name>
         <value>0x130d</value>
         <object_component_ref idref="oc-191"/>
      </symbol>
      <symbol id="sm-329">
         <name>__mulsf3</name>
         <value>0x130d</value>
         <object_component_ref idref="oc-191"/>
      </symbol>
      <symbol id="sm-32f">
         <name>__aeabi_f2d</name>
         <value>0x1c1d</value>
         <object_component_ref idref="oc-1c9"/>
      </symbol>
      <symbol id="sm-330">
         <name>__extendsfdf2</name>
         <value>0x1c1d</value>
         <object_component_ref idref="oc-1c9"/>
      </symbol>
      <symbol id="sm-336">
         <name>__aeabi_f2iz</name>
         <value>0x1e01</value>
         <object_component_ref idref="oc-195"/>
      </symbol>
      <symbol id="sm-337">
         <name>__fixsfsi</name>
         <value>0x1e01</value>
         <object_component_ref idref="oc-195"/>
      </symbol>
      <symbol id="sm-33d">
         <name>__aeabi_i2f</name>
         <value>0x1cd5</value>
         <object_component_ref idref="oc-10f"/>
      </symbol>
      <symbol id="sm-33e">
         <name>__floatsisf</name>
         <value>0x1cd5</value>
         <object_component_ref idref="oc-10f"/>
      </symbol>
      <symbol id="sm-344">
         <name>__aeabi_ui2f</name>
         <value>0x207d</value>
         <object_component_ref idref="oc-114"/>
      </symbol>
      <symbol id="sm-345">
         <name>__floatunsisf</name>
         <value>0x207d</value>
         <object_component_ref idref="oc-114"/>
      </symbol>
      <symbol id="sm-34b">
         <name>__aeabi_dcmpeq</name>
         <value>0x1735</value>
         <object_component_ref idref="oc-1cd"/>
      </symbol>
      <symbol id="sm-34c">
         <name>__aeabi_dcmplt</name>
         <value>0x1749</value>
         <object_component_ref idref="oc-1cd"/>
      </symbol>
      <symbol id="sm-34d">
         <name>__aeabi_dcmple</name>
         <value>0x175d</value>
         <object_component_ref idref="oc-1cd"/>
      </symbol>
      <symbol id="sm-34e">
         <name>__aeabi_dcmpge</name>
         <value>0x1771</value>
         <object_component_ref idref="oc-1cd"/>
      </symbol>
      <symbol id="sm-34f">
         <name>__aeabi_dcmpgt</name>
         <value>0x1785</value>
         <object_component_ref idref="oc-1cd"/>
      </symbol>
      <symbol id="sm-355">
         <name>__aeabi_fcmpeq</name>
         <value>0x1799</value>
         <object_component_ref idref="oc-1c5"/>
      </symbol>
      <symbol id="sm-356">
         <name>__aeabi_fcmplt</name>
         <value>0x17ad</value>
         <object_component_ref idref="oc-1c5"/>
      </symbol>
      <symbol id="sm-357">
         <name>__aeabi_fcmple</name>
         <value>0x17c1</value>
         <object_component_ref idref="oc-1c5"/>
      </symbol>
      <symbol id="sm-358">
         <name>__aeabi_fcmpge</name>
         <value>0x17d5</value>
         <object_component_ref idref="oc-1c5"/>
      </symbol>
      <symbol id="sm-359">
         <name>__aeabi_fcmpgt</name>
         <value>0x17e9</value>
         <object_component_ref idref="oc-1c5"/>
      </symbol>
      <symbol id="sm-35f">
         <name>__aeabi_idiv</name>
         <value>0x18b5</value>
         <object_component_ref idref="oc-199"/>
      </symbol>
      <symbol id="sm-360">
         <name>__aeabi_idivmod</name>
         <value>0x18b5</value>
         <object_component_ref idref="oc-199"/>
      </symbol>
      <symbol id="sm-366">
         <name>__aeabi_memcpy</name>
         <value>0x2645</value>
         <object_component_ref idref="oc-54"/>
      </symbol>
      <symbol id="sm-367">
         <name>__aeabi_memcpy4</name>
         <value>0x2645</value>
         <object_component_ref idref="oc-54"/>
      </symbol>
      <symbol id="sm-368">
         <name>__aeabi_memcpy8</name>
         <value>0x2645</value>
         <object_component_ref idref="oc-54"/>
      </symbol>
      <symbol id="sm-371">
         <name>__eqsf2</name>
         <value>0x1d89</value>
         <object_component_ref idref="oc-1d5"/>
      </symbol>
      <symbol id="sm-372">
         <name>__lesf2</name>
         <value>0x1d89</value>
         <object_component_ref idref="oc-1d5"/>
      </symbol>
      <symbol id="sm-373">
         <name>__ltsf2</name>
         <value>0x1d89</value>
         <object_component_ref idref="oc-1d5"/>
      </symbol>
      <symbol id="sm-374">
         <name>__nesf2</name>
         <value>0x1d89</value>
         <object_component_ref idref="oc-1d5"/>
      </symbol>
      <symbol id="sm-375">
         <name>__cmpsf2</name>
         <value>0x1d89</value>
         <object_component_ref idref="oc-1d5"/>
      </symbol>
      <symbol id="sm-376">
         <name>__gtsf2</name>
         <value>0x1d11</value>
         <object_component_ref idref="oc-1da"/>
      </symbol>
      <symbol id="sm-377">
         <name>__gesf2</name>
         <value>0x1d11</value>
         <object_component_ref idref="oc-1da"/>
      </symbol>
      <symbol id="sm-385">
         <name>__ledf2</name>
         <value>0x16cd</value>
         <object_component_ref idref="oc-1dd"/>
      </symbol>
      <symbol id="sm-386">
         <name>__gedf2</name>
         <value>0x1511</value>
         <object_component_ref idref="oc-1e3"/>
      </symbol>
      <symbol id="sm-387">
         <name>__cmpdf2</name>
         <value>0x16cd</value>
         <object_component_ref idref="oc-1dd"/>
      </symbol>
      <symbol id="sm-388">
         <name>__eqdf2</name>
         <value>0x16cd</value>
         <object_component_ref idref="oc-1dd"/>
      </symbol>
      <symbol id="sm-389">
         <name>__ltdf2</name>
         <value>0x16cd</value>
         <object_component_ref idref="oc-1dd"/>
      </symbol>
      <symbol id="sm-38a">
         <name>__nedf2</name>
         <value>0x16cd</value>
         <object_component_ref idref="oc-1dd"/>
      </symbol>
      <symbol id="sm-38b">
         <name>__gtdf2</name>
         <value>0x1511</value>
         <object_component_ref idref="oc-1e3"/>
      </symbol>
      <symbol id="sm-395">
         <name>__aeabi_idiv0</name>
         <value>0x1797</value>
         <object_component_ref idref="oc-1be"/>
      </symbol>
      <symbol id="sm-39f">
         <name>TI_memcpy_small</name>
         <value>0x25cf</value>
         <object_component_ref idref="oc-a7"/>
      </symbol>
      <symbol id="sm-3a0">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-3a3">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-3a4">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
