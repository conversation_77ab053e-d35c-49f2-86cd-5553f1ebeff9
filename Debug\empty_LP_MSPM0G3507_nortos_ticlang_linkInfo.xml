<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v4.0.3.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <command_line>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\bin\tiarmlnk -ID:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib -o empty_LP_MSPM0G3507_nortos_ticlang.out -mempty_LP_MSPM0G3507_nortos_ticlang.map -iC:/ti/mspm0_sdk_2_05_01_00/source -iC:/Users/<USER>/workspace_ccstheia/empty_LP_MSPM0G3507_nortos_ticlang -iC:/Users/<USER>/workspace_ccstheia/empty_LP_MSPM0G3507_nortos_ticlang/Debug/syscfg -iD:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib --diag_wrap=off --display_error_number --warn_sections --xml_link_info=empty_LP_MSPM0G3507_nortos_ticlang_linkInfo.xml --rom_model ./empty.o ./ti_msp_dl_config.o ./startup_mspm0g350x_ticlang.o ./drivers/encoder.o ./drivers/flash.o ./drivers/imu660rb.o ./drivers/key.o ./drivers/lsm6dsr_reg.o ./drivers/motor.o ./drivers/openmv.o ./drivers/quaternion.o ./drivers/tft180.o ./drivers/timer.o ./soft/debug.o ./soft/delay.o ./soft/menu.o ./soft/pid.o ./soft/protocol.o ./soft/task.o ./soft/vofa.o -l./device_linker.cmd -ldevice.cmd.genlibs -llibc.a --start-group -llibc++.a -llibc++abi.a -llibc.a -llibsys.a -llibsysbm.a -llibclang_rt.builtins.a -llibclang_rt.profile.a --end-group --cg_opt_level=0</command_line>
   <link_time>0x688b3595</link_time>
   <link_errors>0x0</link_errors>
   <output_file>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\empty_LP_MSPM0G3507_nortos_ticlang.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0x2199</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>empty.o</file>
         <name>empty.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-4">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>encoder.o</file>
         <name>encoder.o</name>
      </input_file>
      <input_file id="fl-5">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>flash.o</file>
         <name>flash.o</name>
      </input_file>
      <input_file id="fl-6">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>imu660rb.o</file>
         <name>imu660rb.o</name>
      </input_file>
      <input_file id="fl-7">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>key.o</file>
         <name>key.o</name>
      </input_file>
      <input_file id="fl-8">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>lsm6dsr_reg.o</file>
         <name>lsm6dsr_reg.o</name>
      </input_file>
      <input_file id="fl-9">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>motor.o</file>
         <name>motor.o</name>
      </input_file>
      <input_file id="fl-a">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>openmv.o</file>
         <name>openmv.o</name>
      </input_file>
      <input_file id="fl-b">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>quaternion.o</file>
         <name>quaternion.o</name>
      </input_file>
      <input_file id="fl-c">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>tft180.o</file>
         <name>tft180.o</name>
      </input_file>
      <input_file id="fl-d">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>timer.o</file>
         <name>timer.o</name>
      </input_file>
      <input_file id="fl-e">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\soft\</path>
         <kind>object</kind>
         <file>debug.o</file>
         <name>debug.o</name>
      </input_file>
      <input_file id="fl-f">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\soft\</path>
         <kind>object</kind>
         <file>delay.o</file>
         <name>delay.o</name>
      </input_file>
      <input_file id="fl-10">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\soft\</path>
         <kind>object</kind>
         <file>menu.o</file>
         <name>menu.o</name>
      </input_file>
      <input_file id="fl-11">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\soft\</path>
         <kind>object</kind>
         <file>pid.o</file>
         <name>pid.o</name>
      </input_file>
      <input_file id="fl-12">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\soft\</path>
         <kind>object</kind>
         <file>protocol.o</file>
         <name>protocol.o</name>
      </input_file>
      <input_file id="fl-13">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\soft\</path>
         <kind>object</kind>
         <file>task.o</file>
         <name>task.o</name>
      </input_file>
      <input_file id="fl-14">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\soft\</path>
         <kind>object</kind>
         <file>vofa.o</file>
         <name>vofa.o</name>
      </input_file>
      <input_file id="fl-21">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-22">
         <path>C:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-23">
         <path>C:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_spi.o</name>
      </input_file>
      <input_file id="fl-24">
         <path>C:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_timer.o</name>
      </input_file>
      <input_file id="fl-25">
         <path>C:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_uart.o</name>
      </input_file>
      <input_file id="fl-3c">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>e_asin.c.obj</name>
      </input_file>
      <input_file id="fl-3d">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>e_atan2.c.obj</name>
      </input_file>
      <input_file id="fl-3e">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>e_sqrt.c.obj</name>
      </input_file>
      <input_file id="fl-3f">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>s_atan.c.obj</name>
      </input_file>
      <input_file id="fl-40">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>aeabi_portable.c.obj</name>
      </input_file>
      <input_file id="fl-41">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>assert.c.obj</name>
      </input_file>
      <input_file id="fl-42">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>defs.c.obj</name>
      </input_file>
      <input_file id="fl-43">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memory.c.obj</name>
      </input_file>
      <input_file id="fl-44">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>strlen.c.obj</name>
      </input_file>
      <input_file id="fl-45">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-46">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-47">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-48">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-49">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-4a">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-4b">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-4c">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-4d">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>sprintf.c.obj</name>
      </input_file>
      <input_file id="fl-4e">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fflush.c.obj</name>
      </input_file>
      <input_file id="fl-4f">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fputs.c.obj</name>
      </input_file>
      <input_file id="fl-50">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_io_perm.c.obj</name>
      </input_file>
      <input_file id="fl-51">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>setvbuf.c.obj</name>
      </input_file>
      <input_file id="fl-52">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-53">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-54">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-55">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_printfi.c.obj</name>
      </input_file>
      <input_file id="fl-56">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fopen.c.obj</name>
      </input_file>
      <input_file id="fl-57">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fseek.c.obj</name>
      </input_file>
      <input_file id="fl-58">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>s_frexp.c.obj</name>
      </input_file>
      <input_file id="fl-59">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>s_scalbn.c.obj</name>
      </input_file>
      <input_file id="fl-5a">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>wcslen.c.obj</name>
      </input_file>
      <input_file id="fl-5b">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_ltoa.c.obj</name>
      </input_file>
      <input_file id="fl-5c">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>atoi.c.obj</name>
      </input_file>
      <input_file id="fl-5d">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memccpy.c.obj</name>
      </input_file>
      <input_file id="fl-5e">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fclose.c.obj</name>
      </input_file>
      <input_file id="fl-5f">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>aeabi_ctype.S.obj</name>
      </input_file>
      <input_file id="fl-ff">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostexit.c.obj</name>
      </input_file>
      <input_file id="fl-100">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>write.c.obj</name>
      </input_file>
      <input_file id="fl-101">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>host_device.c.obj</name>
      </input_file>
      <input_file id="fl-102">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>remove.c.obj</name>
      </input_file>
      <input_file id="fl-103">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>open.c.obj</name>
      </input_file>
      <input_file id="fl-104">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>lseek.c.obj</name>
      </input_file>
      <input_file id="fl-105">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>close.c.obj</name>
      </input_file>
      <input_file id="fl-106">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>unlink.c.obj</name>
      </input_file>
      <input_file id="fl-107">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostclose.c.obj</name>
      </input_file>
      <input_file id="fl-108">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostlseek.c.obj</name>
      </input_file>
      <input_file id="fl-109">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostopen.c.obj</name>
      </input_file>
      <input_file id="fl-10a">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostread.c.obj</name>
      </input_file>
      <input_file id="fl-10b">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostrename.c.obj</name>
      </input_file>
      <input_file id="fl-10c">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostunlink.c.obj</name>
      </input_file>
      <input_file id="fl-10d">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostwrite.c.obj</name>
      </input_file>
      <input_file id="fl-10e">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>trgmsg.c.obj</name>
      </input_file>
      <input_file id="fl-10f">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>addsf3.S.obj</name>
      </input_file>
      <input_file id="fl-110">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>adddf3.S.obj</name>
      </input_file>
      <input_file id="fl-111">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldf3.S.obj</name>
      </input_file>
      <input_file id="fl-112">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldsi3.S.obj</name>
      </input_file>
      <input_file id="fl-113">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>mulsf3.S.obj</name>
      </input_file>
      <input_file id="fl-114">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>divsf3.S.obj</name>
      </input_file>
      <input_file id="fl-115">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>divdf3.S.obj</name>
      </input_file>
      <input_file id="fl-116">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>extendsfdf2.S.obj</name>
      </input_file>
      <input_file id="fl-117">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixdfsi.S.obj</name>
      </input_file>
      <input_file id="fl-118">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixsfsi.S.obj</name>
      </input_file>
      <input_file id="fl-119">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatsidf.S.obj</name>
      </input_file>
      <input_file id="fl-11a">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatsisf.S.obj</name>
      </input_file>
      <input_file id="fl-11b">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatunsisf.S.obj</name>
      </input_file>
      <input_file id="fl-11c">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldi3.S.obj</name>
      </input_file>
      <input_file id="fl-11d">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>truncdfsf2.S.obj</name>
      </input_file>
      <input_file id="fl-11e">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_dcmp.S.obj</name>
      </input_file>
      <input_file id="fl-11f">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_fcmp.S.obj</name>
      </input_file>
      <input_file id="fl-120">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_idivmod.S.obj</name>
      </input_file>
      <input_file id="fl-121">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-122">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-123">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uidivmod.S.obj</name>
      </input_file>
      <input_file id="fl-124">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uldivmod.S.obj</name>
      </input_file>
      <input_file id="fl-125">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>comparesf2.S.obj</name>
      </input_file>
      <input_file id="fl-126">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>udivmoddi4.S.obj</name>
      </input_file>
      <input_file id="fl-127">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>ashldi3.S.obj</name>
      </input_file>
      <input_file id="fl-128">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>comparedf2.c.obj</name>
      </input_file>
      <input_file id="fl-129">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_div0.c.obj</name>
      </input_file>
      <input_file id="fl-12a">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>getdevice.c.obj</name>
      </input_file>
      <input_file id="fl-12b">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-12c">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
      <input_file id="fl-12d">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>strcmp-armv6m.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-b6">
         <name>.text.tft180_init</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0x288</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-c5">
         <name>.text.openmv_display_data</name>
         <load_address>0x348</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x348</run_address>
         <size>0x24c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-6b">
         <name>.text.encoder_exti_callback</name>
         <load_address>0x594</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x594</run_address>
         <size>0x20c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-e8">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0x7a0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7a0</run_address>
         <size>0x1ec</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1b8">
         <name>.text.func_float_to_str</name>
         <load_address>0x98c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x98c</run_address>
         <size>0x1bc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-10b">
         <name>.text.tft180_show_char_color</name>
         <load_address>0xb48</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb48</run_address>
         <size>0x130</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.text.UART3_IRQHandler</name>
         <load_address>0xc78</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc78</run_address>
         <size>0x120</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-18d">
         <name>.text.tft180_show_num_color</name>
         <load_address>0xd98</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd98</run_address>
         <size>0x10c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-154">
         <name>.text.DL_Timer_initFourCCPWMMode</name>
         <load_address>0xea4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xea4</run_address>
         <size>0x104</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-15f">
         <name>.text.DL_Timer_initTimerMode</name>
         <load_address>0xfa8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xfa8</run_address>
         <size>0xe8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-10f">
         <name>.text.display_signed_int_clear_area</name>
         <load_address>0x1090</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1090</run_address>
         <size>0xd8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-1b4">
         <name>.text</name>
         <load_address>0x1168</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1168</run_address>
         <size>0xd8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-10f"/>
      </object_component>
      <object_component id="oc-e7">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0x1240</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1240</run_address>
         <size>0xd4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-ea">
         <name>.text.SYSCFG_DL_PWM_6_init</name>
         <load_address>0x1314</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1314</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-eb">
         <name>.text.SYSCFG_DL_PWM_7_init</name>
         <load_address>0x13a0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x13a0</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1a8">
         <name>.text.__mulsf3</name>
         <load_address>0x142c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x142c</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-113"/>
      </object_component>
      <object_component id="oc-14d">
         <name>.text.__NVIC_SetPriority</name>
         <load_address>0x14b8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x14b8</run_address>
         <size>0x84</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.text:decompress:lzss</name>
         <load_address>0x153c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x153c</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-c0">
         <name>.text.tft180_clear_color</name>
         <load_address>0x15b8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x15b8</run_address>
         <size>0x78</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-1e4">
         <name>.text.__gedf2</name>
         <load_address>0x1630</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1630</run_address>
         <size>0x74</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-128"/>
      </object_component>
      <object_component id="oc-102">
         <name>.text.delay_us</name>
         <load_address>0x16a4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x16a4</run_address>
         <size>0x74</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-105">
         <name>.text.tft180_set_region</name>
         <load_address>0x1718</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1718</run_address>
         <size>0x6c</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-b1">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0x1784</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1784</run_address>
         <size>0x68</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1de">
         <name>.text.__ledf2</name>
         <load_address>0x17ec</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x17ec</run_address>
         <size>0x68</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-128"/>
      </object_component>
      <object_component id="oc-1ce">
         <name>.text.__aeabi_dcmp</name>
         <load_address>0x1854</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1854</run_address>
         <size>0x62</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11e"/>
      </object_component>
      <object_component id="oc-1bf">
         <name>.text.__aeabi_idiv0</name>
         <load_address>0x18b6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x18b6</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-129"/>
      </object_component>
      <object_component id="oc-1c6">
         <name>.text.__aeabi_fcmp</name>
         <load_address>0x18b8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x18b8</run_address>
         <size>0x62</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11f"/>
      </object_component>
      <object_component id="oc-c2">
         <name>.text.tft180_show_string_color</name>
         <load_address>0x191a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x191a</run_address>
         <size>0x62</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-93">
         <name>.text.main</name>
         <load_address>0x197c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x197c</run_address>
         <size>0x58</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-1b0">
         <name>.text.__aeabi_idivmod</name>
         <load_address>0x19d4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x19d4</run_address>
         <size>0x56</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-120"/>
      </object_component>
      <object_component id="oc-73">
         <name>.text.timerA_callback</name>
         <load_address>0x1a2a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a2a</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-ef">
         <name>.text.SYSCFG_DL_UART_1_init</name>
         <load_address>0x1a2c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a2c</run_address>
         <size>0x54</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-10e">
         <name>.text.openmv_is_data_valid</name>
         <load_address>0x1a80</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a80</run_address>
         <size>0x50</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-16d">
         <name>.text.DL_UART_setBaudRateDivisor</name>
         <load_address>0x1ad0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ad0</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-16b">
         <name>.text.DL_UART_init</name>
         <load_address>0x1b1c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b1c</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-ee">
         <name>.text.SYSCFG_DL_UART_0_init</name>
         <load_address>0x1b64</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b64</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-f0">
         <name>.text.SYSCFG_DL_UART_3_init</name>
         <load_address>0x1bac</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1bac</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-17c">
         <name>.text.DL_SPI_init</name>
         <load_address>0x1bf4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1bf4</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-106">
         <name>.text.tft180_write_16bit_data</name>
         <load_address>0x1c38</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c38</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-f1">
         <name>.text.SYSCFG_DL_SPI_IMU660RB_init</name>
         <load_address>0x1c7c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c7c</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-f2">
         <name>.text.SYSCFG_DL_TFT_SPI_init</name>
         <load_address>0x1cbc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1cbc</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-ec">
         <name>.text.SYSCFG_DL_TIMER_8_init</name>
         <load_address>0x1cfc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1cfc</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1ca">
         <name>.text.__extendsfdf2</name>
         <load_address>0x1d3c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d3c</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-116"/>
      </object_component>
      <object_component id="oc-155">
         <name>.text.DL_Timer_setCounterControl</name>
         <load_address>0x1d7c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d7c</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-ed">
         <name>.text.SYSCFG_DL_TIMER_12_init</name>
         <load_address>0x1db8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1db8</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-189">
         <name>.text.__floatsisf</name>
         <load_address>0x1df4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1df4</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11a"/>
      </object_component>
      <object_component id="oc-1db">
         <name>.text.__gtsf2</name>
         <load_address>0x1e30</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e30</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-125"/>
      </object_component>
      <object_component id="oc-ce">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0x1e6c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e6c</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-47"/>
      </object_component>
      <object_component id="oc-1d6">
         <name>.text.__eqsf2</name>
         <load_address>0x1ea8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ea8</run_address>
         <size>0x3a</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-125"/>
      </object_component>
      <object_component id="oc-85">
         <name>.text.timerB_callback</name>
         <load_address>0x1ee2</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ee2</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-1b9">
         <name>.text.__muldsi3</name>
         <load_address>0x1ee4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ee4</run_address>
         <size>0x3a</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-112"/>
      </object_component>
      <object_component id="oc-1ac">
         <name>.text.__fixsfsi</name>
         <load_address>0x1f20</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f20</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-118"/>
      </object_component>
      <object_component id="oc-142">
         <name>.text.DL_GPIO_initDigitalInputFeatures</name>
         <load_address>0x1f58</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f58</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-17e">
         <name>.text.DL_SPI_setFIFOThreshold</name>
         <load_address>0x1f8c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f8c</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-79">
         <name>.text.openmv_analysis</name>
         <load_address>0x1fbc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1fbc</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-fd">
         <name>.text.tft180_write_index</name>
         <load_address>0x1fec</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1fec</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-109">
         <name>.text.__NVIC_ClearPendingIRQ</name>
         <load_address>0x201c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x201c</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-10c">
         <name>.text.__NVIC_ClearPendingIRQ</name>
         <load_address>0x2048</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2048</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-10a">
         <name>.text.__NVIC_EnableIRQ</name>
         <load_address>0x2074</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2074</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-10d">
         <name>.text.__NVIC_EnableIRQ</name>
         <load_address>0x20a0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20a0</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-fe">
         <name>.text.tft180_write_8bit_data</name>
         <load_address>0x20cc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20cc</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-1a0">
         <name>.text.DL_Common_updateReg</name>
         <load_address>0x20f8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20f8</run_address>
         <size>0x28</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-184">
         <name>.text.DL_SYSTICK_init</name>
         <load_address>0x2120</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2120</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-39">
         <name>.text.TIMG8_IRQHandler</name>
         <load_address>0x2148</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2148</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-43">
         <name>.text.UART0_IRQHandler</name>
         <load_address>0x2170</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2170</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-64">
         <name>.text:_c_int00_noargs</name>
         <load_address>0x2198</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2198</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-17d">
         <name>.text.DL_SPI_setBitRateSerialClockDivider</name>
         <load_address>0x21c0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x21c0</run_address>
         <size>0x24</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-e9">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0x21e4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x21e4</run_address>
         <size>0x22</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-141">
         <name>.text.DL_GPIO_initPeripheralInputFunction</name>
         <load_address>0x2208</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2208</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-16c">
         <name>.text.DL_UART_setOversampling</name>
         <load_address>0x2228</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2228</run_address>
         <size>0x1e</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-9f">
         <name>.text.DL_GPIO_clearInterruptStatus</name>
         <load_address>0x2248</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2248</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-146">
         <name>.text.DL_GPIO_clearInterruptStatus</name>
         <load_address>0x2264</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2264</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-147">
         <name>.text.DL_GPIO_enableInterrupt</name>
         <load_address>0x2280</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2280</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-13f">
         <name>.text.DL_GPIO_initPeripheralOutputFunction</name>
         <load_address>0x229c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x229c</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-144">
         <name>.text.DL_GPIO_setLowerPinsPolarity</name>
         <load_address>0x22b8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x22b8</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-14b">
         <name>.text.DL_SYSCTL_setSYSOSCFreq</name>
         <load_address>0x22d4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x22d4</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-160">
         <name>.text.DL_Timer_enableInterrupt</name>
         <load_address>0x22f0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x22f0</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-157">
         <name>.text.DL_Timer_setCaptCompUpdateMethod</name>
         <load_address>0x230c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x230c</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-14e">
         <name>.text.DL_Timer_setClockConfig</name>
         <load_address>0x2328</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2328</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-16e">
         <name>.text.DL_UART_enableInterrupt</name>
         <load_address>0x2344</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2344</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-48">
         <name>.text.TIMG12_IRQHandler</name>
         <load_address>0x2360</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2360</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-140">
         <name>.text.DL_GPIO_enableOutput</name>
         <load_address>0x237c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x237c</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-137">
         <name>.text.DL_GPIO_enablePower</name>
         <load_address>0x2394</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2394</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-9e">
         <name>.text.DL_GPIO_getEnabledInterruptStatus</name>
         <load_address>0x23ac</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x23ac</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-143">
         <name>.text.DL_GPIO_initDigitalOutput</name>
         <load_address>0x23c4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x23c4</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-133">
         <name>.text.DL_GPIO_reset</name>
         <load_address>0x23dc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x23dc</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-fc">
         <name>.text.DL_GPIO_setPins</name>
         <load_address>0x23f4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x23f4</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-149">
         <name>.text.DL_GPIO_setPins</name>
         <load_address>0x240c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x240c</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-145">
         <name>.text.DL_GPIO_setUpperPinsPolarity</name>
         <load_address>0x2424</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2424</run_address>
         <size>0x18</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-17f">
         <name>.text.DL_SPI_enable</name>
         <load_address>0x243c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x243c</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-13a">
         <name>.text.DL_SPI_enablePower</name>
         <load_address>0x2454</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2454</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-187">
         <name>.text.DL_SPI_isBusy</name>
         <load_address>0x246c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x246c</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-136">
         <name>.text.DL_SPI_reset</name>
         <load_address>0x2484</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2484</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-14a">
         <name>.text.DL_SYSCTL_setBORThreshold</name>
         <load_address>0x249c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x249c</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-138">
         <name>.text.DL_Timer_enablePower</name>
         <load_address>0x24b4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x24b4</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-134">
         <name>.text.DL_Timer_reset</name>
         <load_address>0x24cc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x24cc</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-156">
         <name>.text.DL_Timer_setCaptureCompareOutCtl</name>
         <load_address>0x24e4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x24e4</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-139">
         <name>.text.DL_UART_enablePower</name>
         <load_address>0x24fc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x24fc</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-135">
         <name>.text.DL_UART_reset</name>
         <load_address>0x2514</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2514</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-a0">
         <name>.text.DL_GPIO_readPins</name>
         <load_address>0x252c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x252c</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-186">
         <name>.text.DL_SPI_transmitData8</name>
         <load_address>0x2542</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2542</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-16f">
         <name>.text.DL_UART_enable</name>
         <load_address>0x2558</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2558</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-bb">
         <name>.text.delay_ms</name>
         <load_address>0x256e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x256e</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-c1">
         <name>.text.openmv_init</name>
         <load_address>0x2584</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2584</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-c3">
         <name>.text.timerA_init</name>
         <load_address>0x259a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x259a</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-c4">
         <name>.text.timerB_init</name>
         <load_address>0x25b0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x25b0</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-5b">
         <name>.text:decompress:ZI:__TI_zero_init_nomemset</name>
         <load_address>0x25c6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x25c6</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4a"/>
      </object_component>
      <object_component id="oc-fb">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x25dc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x25dc</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-148">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x25f0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x25f0</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-14c">
         <name>.text.DL_SYSCTL_enableMFCLK</name>
         <load_address>0x2604</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2604</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-159">
         <name>.text.DL_Timer_enableClock</name>
         <load_address>0x2618</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2618</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-15a">
         <name>.text.DL_Timer_setCCPDirection</name>
         <load_address>0x262c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x262c</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-81">
         <name>.text.DL_UART_receiveData</name>
         <load_address>0x2640</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2640</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-78">
         <name>.text.DL_UART_receiveData</name>
         <load_address>0x2654</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2654</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-176">
         <name>.text.DL_SPI_setClockConfig</name>
         <load_address>0x2668</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2668</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-72">
         <name>.text.DL_Timer_getPendingInterrupt</name>
         <load_address>0x267a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x267a</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-80">
         <name>.text.DL_UART_getPendingInterrupt</name>
         <load_address>0x268c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x268c</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-77">
         <name>.text.DL_UART_getPendingInterrupt</name>
         <load_address>0x269e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x269e</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-165">
         <name>.text.DL_UART_setClockConfig</name>
         <load_address>0x26b0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x26b0</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-a7">
         <name>.text:TI_memcpy_small</name>
         <load_address>0x26c2</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x26c2</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12b"/>
      </object_component>
      <object_component id="oc-20">
         <name>.text:decompress:none</name>
         <load_address>0x26d4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x26d4</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4b"/>
      </object_component>
      <object_component id="oc-185">
         <name>.text.DL_SYSTICK_enable</name>
         <load_address>0x26e8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x26e8</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-158">
         <name>.text.DL_Timer_setCaptureCompareValue</name>
         <load_address>0x26f8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x26f8</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-f3">
         <name>.text.SYSCFG_DL_SYSTICK_init</name>
         <load_address>0x2708</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2708</run_address>
         <size>0x10</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-a5">
         <name>.text.get_system_time_ms</name>
         <load_address>0x2718</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2718</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-13b">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0x2724</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2724</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-34">
         <name>.text.GROUP1_IRQHandler</name>
         <load_address>0x272e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x272e</run_address>
         <size>0x8</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-54">
         <name>.text.__aeabi_memcpy</name>
         <load_address>0x2738</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2738</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-121"/>
      </object_component>
      <object_component id="oc-c7">
         <name>.text:abort</name>
         <load_address>0x2740</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2740</run_address>
         <size>0x6</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-52"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x2746</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2746</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-122">
         <name>.text.HOSTexit</name>
         <load_address>0x274a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x274a</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-ff"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0x274e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x274e</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.text._system_pre_init</name>
         <load_address>0x2752</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2752</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-48"/>
      </object_component>
      <object_component id="oc-22a">
         <name>.cinit..data.load</name>
         <load_address>0x2f20</load_address>
         <readonly>true</readonly>
         <run_address>0x2f20</run_address>
         <size>0xf</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-228">
         <name>__TI_handler_table</name>
         <load_address>0x2f30</load_address>
         <readonly>true</readonly>
         <run_address>0x2f30</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-22b">
         <name>.cinit..bss.load</name>
         <load_address>0x2f3c</load_address>
         <readonly>true</readonly>
         <run_address>0x2f3c</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-229">
         <name>__TI_cinit_table</name>
         <load_address>0x2f44</load_address>
         <readonly>true</readonly>
         <run_address>0x2f44</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-188">
         <name>.rodata.ascii_font_8x16</name>
         <load_address>0x2758</load_address>
         <readonly>true</readonly>
         <run_address>0x2758</run_address>
         <size>0x5f0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-164">
         <name>.rodata.gTIMER_12TimerConfig</name>
         <load_address>0x2d48</load_address>
         <readonly>true</readonly>
         <run_address>0x2d48</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-162">
         <name>.rodata.gTIMER_8TimerConfig</name>
         <load_address>0x2d5c</load_address>
         <readonly>true</readonly>
         <run_address>0x2d5c</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-c6">
         <name>.rodata.str1.5792233746214848027.1</name>
         <load_address>0x2d70</load_address>
         <readonly>true</readonly>
         <run_address>0x2d70</run_address>
         <size>0x14</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-110">
         <name>.rodata.str1.14055760531511630791.1</name>
         <load_address>0x2d84</load_address>
         <readonly>true</readonly>
         <run_address>0x2d84</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-118">
         <name>.rodata.str1.10222307361326560281.1</name>
         <load_address>0x2d97</load_address>
         <readonly>true</readonly>
         <run_address>0x2d97</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-121">
         <name>.rodata.str1.10322375466862398049.1</name>
         <load_address>0x2da9</load_address>
         <readonly>true</readonly>
         <run_address>0x2da9</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-117">
         <name>.rodata.str1.10499335994202400488.1</name>
         <load_address>0x2dbb</load_address>
         <readonly>true</readonly>
         <run_address>0x2dbb</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-114">
         <name>.rodata.str1.11972700756869316087.1</name>
         <load_address>0x2dcd</load_address>
         <readonly>true</readonly>
         <run_address>0x2dcd</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-119">
         <name>.rodata.str1.12960560650680968270.1</name>
         <load_address>0x2ddf</load_address>
         <readonly>true</readonly>
         <run_address>0x2ddf</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-120">
         <name>.rodata.str1.12983843792890534433.1</name>
         <load_address>0x2df1</load_address>
         <readonly>true</readonly>
         <run_address>0x2df1</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-11e">
         <name>.rodata.str1.15289475315984735280.1</name>
         <load_address>0x2e03</load_address>
         <readonly>true</readonly>
         <run_address>0x2e03</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-11d">
         <name>.rodata.str1.16410698957387474858.1</name>
         <load_address>0x2e15</load_address>
         <readonly>true</readonly>
         <run_address>0x2e15</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-113">
         <name>.rodata.str1.16964012482489148156.1</name>
         <load_address>0x2e27</load_address>
         <readonly>true</readonly>
         <run_address>0x2e27</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-11f">
         <name>.rodata.str1.4018680016288177859.1</name>
         <load_address>0x2e39</load_address>
         <readonly>true</readonly>
         <run_address>0x2e39</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-111">
         <name>.rodata.str1.5388014517103437970.1</name>
         <load_address>0x2e4b</load_address>
         <readonly>true</readonly>
         <run_address>0x2e4b</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-11c">
         <name>.rodata.str1.5573925365973559781.1</name>
         <load_address>0x2e5d</load_address>
         <readonly>true</readonly>
         <run_address>0x2e5d</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-112">
         <name>.rodata.str1.5732439976852354875.1</name>
         <load_address>0x2e6f</load_address>
         <readonly>true</readonly>
         <run_address>0x2e6f</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-115">
         <name>.rodata.str1.8871796710599046883.1</name>
         <load_address>0x2e81</load_address>
         <readonly>true</readonly>
         <run_address>0x2e81</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-116">
         <name>.rodata.str1.9558640640855864504.1</name>
         <load_address>0x2e93</load_address>
         <readonly>true</readonly>
         <run_address>0x2e93</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-181">
         <name>.rodata.gSPI_IMU660RB_config</name>
         <load_address>0x2ea6</load_address>
         <readonly>true</readonly>
         <run_address>0x2ea6</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-183">
         <name>.rodata.gTFT_SPI_config</name>
         <load_address>0x2eb0</load_address>
         <readonly>true</readonly>
         <run_address>0x2eb0</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-171">
         <name>.rodata.gUART_0Config</name>
         <load_address>0x2eba</load_address>
         <readonly>true</readonly>
         <run_address>0x2eba</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-173">
         <name>.rodata.gUART_1Config</name>
         <load_address>0x2ec4</load_address>
         <readonly>true</readonly>
         <run_address>0x2ec4</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-175">
         <name>.rodata.gUART_3Config</name>
         <load_address>0x2ece</load_address>
         <readonly>true</readonly>
         <run_address>0x2ece</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-11a">
         <name>.rodata.str1.16395811435273266920.1</name>
         <load_address>0x2ed8</load_address>
         <readonly>true</readonly>
         <run_address>0x2ed8</run_address>
         <size>0xa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-11b">
         <name>.rodata.str1.9416414711272993270.1</name>
         <load_address>0x2ee2</load_address>
         <readonly>true</readonly>
         <run_address>0x2ee2</run_address>
         <size>0xa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-15c">
         <name>.rodata.gPWM_6Config</name>
         <load_address>0x2eec</load_address>
         <readonly>true</readonly>
         <run_address>0x2eec</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-15e">
         <name>.rodata.gPWM_7Config</name>
         <load_address>0x2ef4</load_address>
         <readonly>true</readonly>
         <run_address>0x2ef4</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-18e">
         <name>.rodata.str1.8815565852085528469.1</name>
         <load_address>0x2efc</load_address>
         <readonly>true</readonly>
         <run_address>0x2efc</run_address>
         <size>0x6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-15b">
         <name>.rodata.gPWM_6ClockConfig</name>
         <load_address>0x2f02</load_address>
         <readonly>true</readonly>
         <run_address>0x2f02</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-15d">
         <name>.rodata.gPWM_7ClockConfig</name>
         <load_address>0x2f05</load_address>
         <readonly>true</readonly>
         <run_address>0x2f05</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-163">
         <name>.rodata.gTIMER_12ClockConfig</name>
         <load_address>0x2f08</load_address>
         <readonly>true</readonly>
         <run_address>0x2f08</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-161">
         <name>.rodata.gTIMER_8ClockConfig</name>
         <load_address>0x2f0b</load_address>
         <readonly>true</readonly>
         <run_address>0x2f0b</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-180">
         <name>.rodata.gSPI_IMU660RB_clockConfig</name>
         <load_address>0x2f0e</load_address>
         <readonly>true</readonly>
         <run_address>0x2f0e</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-182">
         <name>.rodata.gTFT_SPI_clockConfig</name>
         <load_address>0x2f10</load_address>
         <readonly>true</readonly>
         <run_address>0x2f10</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-170">
         <name>.rodata.gUART_0ClockConfig</name>
         <load_address>0x2f12</load_address>
         <readonly>true</readonly>
         <run_address>0x2f12</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-172">
         <name>.rodata.gUART_1ClockConfig</name>
         <load_address>0x2f14</load_address>
         <readonly>true</readonly>
         <run_address>0x2f14</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-174">
         <name>.rodata.gUART_3ClockConfig</name>
         <load_address>0x2f16</load_address>
         <readonly>true</readonly>
         <run_address>0x2f16</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-191">
         <name>.rodata.str1.10536413716522492838.1</name>
         <load_address>0x2f18</load_address>
         <readonly>true</readonly>
         <run_address>0x2f18</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-1f2">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-a2">
         <name>.data.left_counter</name>
         <load_address>0x202001d8</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001d8</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-a1">
         <name>.data.right_counter</name>
         <load_address>0x202001da</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001da</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-7b">
         <name>.data.state</name>
         <load_address>0x202001e3</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001e3</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-7c">
         <name>.data.n</name>
         <load_address>0x202001e2</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001e2</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-18f">
         <name>.data.tft180_bgcolor</name>
         <load_address>0x202001dc</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001dc</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-190">
         <name>.data.tft180_pencolor</name>
         <load_address>0x202001e0</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001e0</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-107">
         <name>.data.tft180_x_max</name>
         <load_address>0x202001e4</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001e4</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-108">
         <name>.data.tft180_y_max</name>
         <load_address>0x202001e5</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001e5</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-ff">
         <name>.data.tft180_bgcolor</name>
         <load_address>0x202001de</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001de</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-74">
         <name>.data.system_time_ms</name>
         <load_address>0x202001d4</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001d4</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-82">
         <name>.data.uart_data</name>
         <load_address>0x202001e6</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001e6</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-7d">
         <name>.bss.rx_buffer</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202001c0</run_address>
         <size>0x8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-7a">
         <name>.bss.data</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202001d0</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-f4">
         <name>.common:gPWM_6Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0xa0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-f5">
         <name>.common:gPWM_7Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202000a0</run_address>
         <size>0xa0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-f6">
         <name>.common:gUART_3Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200140</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-f7">
         <name>.common:gSPI_IMU660RBBackup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200170</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-f8">
         <name>.common:gTFT_SPIBackup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200198</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-a6">
         <name>.common:openmvData</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202001c8</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20203e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-22d">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20203e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-70">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xfa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-f9">
         <name>.debug_abbrev</name>
         <load_address>0xfa</load_address>
         <run_address>0xfa</run_address>
         <size>0x210</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_abbrev</name>
         <load_address>0x30a</load_address>
         <run_address>0x30a</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-a3">
         <name>.debug_abbrev</name>
         <load_address>0x377</load_address>
         <run_address>0x377</run_address>
         <size>0x185</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-7e">
         <name>.debug_abbrev</name>
         <load_address>0x4fc</load_address>
         <run_address>0x4fc</run_address>
         <size>0x21b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-100">
         <name>.debug_abbrev</name>
         <load_address>0x717</load_address>
         <run_address>0x717</run_address>
         <size>0x19d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-75">
         <name>.debug_abbrev</name>
         <load_address>0x8b4</load_address>
         <run_address>0x8b4</run_address>
         <size>0x18e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-83">
         <name>.debug_abbrev</name>
         <load_address>0xa42</load_address>
         <run_address>0xa42</run_address>
         <size>0x1c4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-103">
         <name>.debug_abbrev</name>
         <load_address>0xc06</load_address>
         <run_address>0xc06</run_address>
         <size>0xac</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-19d">
         <name>.debug_abbrev</name>
         <load_address>0xcb2</load_address>
         <run_address>0xcb2</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-1a5">
         <name>.debug_abbrev</name>
         <load_address>0xd14</load_address>
         <run_address>0xd14</run_address>
         <size>0x277</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-1a1">
         <name>.debug_abbrev</name>
         <load_address>0xf8b</load_address>
         <run_address>0xf8b</run_address>
         <size>0x286</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-1a3">
         <name>.debug_abbrev</name>
         <load_address>0x1211</load_address>
         <run_address>0x1211</run_address>
         <size>0x29b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.debug_abbrev</name>
         <load_address>0x14ac</load_address>
         <run_address>0x14ac</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-12c">
         <name>.debug_abbrev</name>
         <load_address>0x155b</load_address>
         <run_address>0x155b</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-47"/>
      </object_component>
      <object_component id="oc-af">
         <name>.debug_abbrev</name>
         <load_address>0x16cb</load_address>
         <run_address>0x16cb</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-48"/>
      </object_component>
      <object_component id="oc-88">
         <name>.debug_abbrev</name>
         <load_address>0x1704</load_address>
         <run_address>0x1704</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4a"/>
      </object_component>
      <object_component id="oc-58">
         <name>.debug_abbrev</name>
         <load_address>0x17c6</load_address>
         <run_address>0x17c6</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4b"/>
      </object_component>
      <object_component id="oc-50">
         <name>.debug_abbrev</name>
         <load_address>0x1836</load_address>
         <run_address>0x1836</run_address>
         <size>0x8d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-126">
         <name>.debug_abbrev</name>
         <load_address>0x18c3</load_address>
         <run_address>0x18c3</run_address>
         <size>0x98</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-52"/>
      </object_component>
      <object_component id="oc-192">
         <name>.debug_abbrev</name>
         <load_address>0x195b</load_address>
         <run_address>0x195b</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ff"/>
      </object_component>
      <object_component id="oc-1c5">
         <name>.debug_abbrev</name>
         <load_address>0x1987</load_address>
         <run_address>0x1987</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10f"/>
      </object_component>
      <object_component id="oc-1d2">
         <name>.debug_abbrev</name>
         <load_address>0x19ae</load_address>
         <run_address>0x19ae</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-112"/>
      </object_component>
      <object_component id="oc-1bd">
         <name>.debug_abbrev</name>
         <load_address>0x19d5</load_address>
         <run_address>0x19d5</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-113"/>
      </object_component>
      <object_component id="oc-1dd">
         <name>.debug_abbrev</name>
         <load_address>0x19fc</load_address>
         <run_address>0x19fc</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-116"/>
      </object_component>
      <object_component id="oc-1be">
         <name>.debug_abbrev</name>
         <load_address>0x1a23</load_address>
         <run_address>0x1a23</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-118"/>
      </object_component>
      <object_component id="oc-1a7">
         <name>.debug_abbrev</name>
         <load_address>0x1a4a</load_address>
         <run_address>0x1a4a</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11a"/>
      </object_component>
      <object_component id="oc-1e5">
         <name>.debug_abbrev</name>
         <load_address>0x1a71</load_address>
         <run_address>0x1a71</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11e"/>
      </object_component>
      <object_component id="oc-1dc">
         <name>.debug_abbrev</name>
         <load_address>0x1a98</load_address>
         <run_address>0x1a98</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11f"/>
      </object_component>
      <object_component id="oc-1c4">
         <name>.debug_abbrev</name>
         <load_address>0x1abf</load_address>
         <run_address>0x1abf</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-120"/>
      </object_component>
      <object_component id="oc-87">
         <name>.debug_abbrev</name>
         <load_address>0x1ae6</load_address>
         <run_address>0x1ae6</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-121"/>
      </object_component>
      <object_component id="oc-1e6">
         <name>.debug_abbrev</name>
         <load_address>0x1b0d</load_address>
         <run_address>0x1b0d</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-125"/>
      </object_component>
      <object_component id="oc-1e7">
         <name>.debug_abbrev</name>
         <load_address>0x1b32</load_address>
         <run_address>0x1b32</run_address>
         <size>0xc8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-128"/>
      </object_component>
      <object_component id="oc-1d3">
         <name>.debug_abbrev</name>
         <load_address>0x1bfa</load_address>
         <run_address>0x1bfa</run_address>
         <size>0x59</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-129"/>
      </object_component>
      <object_component id="oc-e0">
         <name>.debug_abbrev</name>
         <load_address>0x1c53</load_address>
         <run_address>0x1c53</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12b"/>
      </object_component>
      <object_component id="oc-22f">
         <name>.debug_abbrev</name>
         <load_address>0x1c78</load_address>
         <run_address>0x1c78</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x5ad</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b2">
         <name>.debug_info</name>
         <load_address>0x5ad</load_address>
         <run_address>0x5ad</run_address>
         <size>0x3acb</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x4078</load_address>
         <run_address>0x4078</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-6d">
         <name>.debug_info</name>
         <load_address>0x40f8</load_address>
         <run_address>0x40f8</run_address>
         <size>0xb04</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.debug_info</name>
         <load_address>0x4bfc</load_address>
         <run_address>0x4bfc</run_address>
         <size>0xe9a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-b9">
         <name>.debug_info</name>
         <load_address>0x5a96</load_address>
         <run_address>0x5a96</run_address>
         <size>0x14df</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_info</name>
         <load_address>0x6f75</load_address>
         <run_address>0x6f75</run_address>
         <size>0xa3c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-45">
         <name>.debug_info</name>
         <load_address>0x79b1</load_address>
         <run_address>0x79b1</run_address>
         <size>0xa81</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-bc">
         <name>.debug_info</name>
         <load_address>0x8432</load_address>
         <run_address>0x8432</run_address>
         <size>0x121</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-13e">
         <name>.debug_info</name>
         <load_address>0x8553</load_address>
         <run_address>0x8553</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-17a">
         <name>.debug_info</name>
         <load_address>0x85c8</load_address>
         <run_address>0x85c8</run_address>
         <size>0x1142</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-152">
         <name>.debug_info</name>
         <load_address>0x970a</load_address>
         <run_address>0x970a</run_address>
         <size>0x3172</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-16a">
         <name>.debug_info</name>
         <load_address>0xc87c</load_address>
         <run_address>0xc87c</run_address>
         <size>0x12a6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0xdb22</load_address>
         <run_address>0xdb22</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-cf">
         <name>.debug_info</name>
         <load_address>0xdf45</load_address>
         <run_address>0xdf45</run_address>
         <size>0x744</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-47"/>
      </object_component>
      <object_component id="oc-92">
         <name>.debug_info</name>
         <load_address>0xe689</load_address>
         <run_address>0xe689</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-48"/>
      </object_component>
      <object_component id="oc-5d">
         <name>.debug_info</name>
         <load_address>0xe6cf</load_address>
         <run_address>0xe6cf</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4a"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_info</name>
         <load_address>0xe861</load_address>
         <run_address>0xe861</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4b"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_info</name>
         <load_address>0xe927</load_address>
         <run_address>0xe927</run_address>
         <size>0x17c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-ca">
         <name>.debug_info</name>
         <load_address>0xeaa3</load_address>
         <run_address>0xeaa3</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-52"/>
      </object_component>
      <object_component id="oc-123">
         <name>.debug_info</name>
         <load_address>0xeb9b</load_address>
         <run_address>0xeb9b</run_address>
         <size>0x3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ff"/>
      </object_component>
      <object_component id="oc-1b5">
         <name>.debug_info</name>
         <load_address>0xebd6</load_address>
         <run_address>0xebd6</run_address>
         <size>0x1a7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10f"/>
      </object_component>
      <object_component id="oc-1ba">
         <name>.debug_info</name>
         <load_address>0xed7d</load_address>
         <run_address>0xed7d</run_address>
         <size>0x18f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-112"/>
      </object_component>
      <object_component id="oc-1a9">
         <name>.debug_info</name>
         <load_address>0xef0c</load_address>
         <run_address>0xef0c</run_address>
         <size>0x18d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-113"/>
      </object_component>
      <object_component id="oc-1cb">
         <name>.debug_info</name>
         <load_address>0xf099</load_address>
         <run_address>0xf099</run_address>
         <size>0x197</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-116"/>
      </object_component>
      <object_component id="oc-1ae">
         <name>.debug_info</name>
         <load_address>0xf230</load_address>
         <run_address>0xf230</run_address>
         <size>0x18f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-118"/>
      </object_component>
      <object_component id="oc-18a">
         <name>.debug_info</name>
         <load_address>0xf3bf</load_address>
         <run_address>0xf3bf</run_address>
         <size>0x193</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11a"/>
      </object_component>
      <object_component id="oc-1d1">
         <name>.debug_info</name>
         <load_address>0xf552</load_address>
         <run_address>0xf552</run_address>
         <size>0x217</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11e"/>
      </object_component>
      <object_component id="oc-1c7">
         <name>.debug_info</name>
         <load_address>0xf769</load_address>
         <run_address>0xf769</run_address>
         <size>0x217</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11f"/>
      </object_component>
      <object_component id="oc-1b3">
         <name>.debug_info</name>
         <load_address>0xf980</load_address>
         <run_address>0xf980</run_address>
         <size>0x1b9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-120"/>
      </object_component>
      <object_component id="oc-56">
         <name>.debug_info</name>
         <load_address>0xfb39</load_address>
         <run_address>0xfb39</run_address>
         <size>0x199</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-121"/>
      </object_component>
      <object_component id="oc-1d7">
         <name>.debug_info</name>
         <load_address>0xfcd2</load_address>
         <run_address>0xfcd2</run_address>
         <size>0x1c1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-125"/>
      </object_component>
      <object_component id="oc-1e1">
         <name>.debug_info</name>
         <load_address>0xfe93</load_address>
         <run_address>0xfe93</run_address>
         <size>0x2f9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-128"/>
      </object_component>
      <object_component id="oc-1c3">
         <name>.debug_info</name>
         <load_address>0x1018c</load_address>
         <run_address>0x1018c</run_address>
         <size>0x85</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-129"/>
      </object_component>
      <object_component id="oc-a8">
         <name>.debug_info</name>
         <load_address>0x10211</load_address>
         <run_address>0x10211</run_address>
         <size>0x2fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12b"/>
      </object_component>
      <object_component id="oc-22e">
         <name>.debug_info</name>
         <load_address>0x1050b</load_address>
         <run_address>0x1050b</run_address>
         <size>0xbc</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b3">
         <name>.debug_ranges</name>
         <load_address>0x50</load_address>
         <run_address>0x50</run_address>
         <size>0x1b0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_ranges</name>
         <load_address>0x200</load_address>
         <run_address>0x200</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-6f">
         <name>.debug_ranges</name>
         <load_address>0x218</load_address>
         <run_address>0x218</run_address>
         <size>0x40</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_ranges</name>
         <load_address>0x258</load_address>
         <run_address>0x258</run_address>
         <size>0x88</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-b8">
         <name>.debug_ranges</name>
         <load_address>0x2e0</load_address>
         <run_address>0x2e0</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_ranges</name>
         <load_address>0x360</load_address>
         <run_address>0x360</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-46">
         <name>.debug_ranges</name>
         <load_address>0x3a8</load_address>
         <run_address>0x3a8</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-bd">
         <name>.debug_ranges</name>
         <load_address>0x418</load_address>
         <run_address>0x418</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-179">
         <name>.debug_ranges</name>
         <load_address>0x430</load_address>
         <run_address>0x430</run_address>
         <size>0x390</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-150">
         <name>.debug_ranges</name>
         <load_address>0x7c0</load_address>
         <run_address>0x7c0</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-169">
         <name>.debug_ranges</name>
         <load_address>0x998</load_address>
         <run_address>0x998</run_address>
         <size>0x1a8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_ranges</name>
         <load_address>0xb40</load_address>
         <run_address>0xb40</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-d3">
         <name>.debug_ranges</name>
         <load_address>0xb88</load_address>
         <run_address>0xb88</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-47"/>
      </object_component>
      <object_component id="oc-5e">
         <name>.debug_ranges</name>
         <load_address>0xbd0</load_address>
         <run_address>0xbd0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4a"/>
      </object_component>
      <object_component id="oc-53">
         <name>.debug_ranges</name>
         <load_address>0xbe8</load_address>
         <run_address>0xbe8</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-c9">
         <name>.debug_ranges</name>
         <load_address>0xc38</load_address>
         <run_address>0xc38</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-52"/>
      </object_component>
      <object_component id="oc-1d9">
         <name>.debug_ranges</name>
         <load_address>0xc50</load_address>
         <run_address>0xc50</run_address>
         <size>0x38</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-125"/>
      </object_component>
      <object_component id="oc-1df">
         <name>.debug_ranges</name>
         <load_address>0xc88</load_address>
         <run_address>0xc88</run_address>
         <size>0x38</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-128"/>
      </object_component>
      <object_component id="oc-1c2">
         <name>.debug_ranges</name>
         <load_address>0xcc0</load_address>
         <run_address>0xcc0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-129"/>
      </object_component>
      <object_component id="oc-aa">
         <name>.debug_ranges</name>
         <load_address>0xcd8</load_address>
         <run_address>0xcd8</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12b"/>
      </object_component>
      <object_component id="oc-71">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x368</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-fa">
         <name>.debug_str</name>
         <load_address>0x368</load_address>
         <run_address>0x368</run_address>
         <size>0x3086</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_str</name>
         <load_address>0x33ee</load_address>
         <run_address>0x33ee</run_address>
         <size>0x173</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-a4">
         <name>.debug_str</name>
         <load_address>0x3561</load_address>
         <run_address>0x3561</run_address>
         <size>0x7de</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-7f">
         <name>.debug_str</name>
         <load_address>0x3d3f</load_address>
         <run_address>0x3d3f</run_address>
         <size>0xa84</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-101">
         <name>.debug_str</name>
         <load_address>0x47c3</load_address>
         <run_address>0x47c3</run_address>
         <size>0x770</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-76">
         <name>.debug_str</name>
         <load_address>0x4f33</load_address>
         <run_address>0x4f33</run_address>
         <size>0x89e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-84">
         <name>.debug_str</name>
         <load_address>0x57d1</load_address>
         <run_address>0x57d1</run_address>
         <size>0x8af</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-104">
         <name>.debug_str</name>
         <load_address>0x6080</load_address>
         <run_address>0x6080</run_address>
         <size>0x140</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-19e">
         <name>.debug_str</name>
         <load_address>0x61c0</load_address>
         <run_address>0x61c0</run_address>
         <size>0x178</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-1a6">
         <name>.debug_str</name>
         <load_address>0x6338</load_address>
         <run_address>0x6338</run_address>
         <size>0xc46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-1a2">
         <name>.debug_str</name>
         <load_address>0x6f7e</load_address>
         <run_address>0x6f7e</run_address>
         <size>0x1dd7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-1a4">
         <name>.debug_str</name>
         <load_address>0x8d55</load_address>
         <run_address>0x8d55</run_address>
         <size>0xcee</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-4c">
         <name>.debug_str</name>
         <load_address>0x9a43</load_address>
         <run_address>0x9a43</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-12d">
         <name>.debug_str</name>
         <load_address>0x9c68</load_address>
         <run_address>0x9c68</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-47"/>
      </object_component>
      <object_component id="oc-b0">
         <name>.debug_str</name>
         <load_address>0x9f97</load_address>
         <run_address>0x9f97</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-48"/>
      </object_component>
      <object_component id="oc-89">
         <name>.debug_str</name>
         <load_address>0xa08c</load_address>
         <run_address>0xa08c</run_address>
         <size>0x19b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4a"/>
      </object_component>
      <object_component id="oc-59">
         <name>.debug_str</name>
         <load_address>0xa227</load_address>
         <run_address>0xa227</run_address>
         <size>0x168</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4b"/>
      </object_component>
      <object_component id="oc-51">
         <name>.debug_str</name>
         <load_address>0xa38f</load_address>
         <run_address>0xa38f</run_address>
         <size>0x1d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-127">
         <name>.debug_str</name>
         <load_address>0xa564</load_address>
         <run_address>0xa564</run_address>
         <size>0x148</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-52"/>
      </object_component>
      <object_component id="oc-193">
         <name>.debug_str</name>
         <load_address>0xa6ac</load_address>
         <run_address>0xa6ac</run_address>
         <size>0xe9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ff"/>
      </object_component>
      <object_component id="oc-1e8">
         <name>.debug_str</name>
         <load_address>0xa795</load_address>
         <run_address>0xa795</run_address>
         <size>0x276</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-128"/>
      </object_component>
      <object_component id="oc-1d4">
         <name>.debug_str</name>
         <load_address>0xaa0b</load_address>
         <run_address>0xaa0b</run_address>
         <size>0x193</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-129"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xf4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b4">
         <name>.debug_frame</name>
         <load_address>0xf4</load_address>
         <run_address>0xf4</run_address>
         <size>0x4e8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_frame</name>
         <load_address>0x5dc</load_address>
         <run_address>0x5dc</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-6c">
         <name>.debug_frame</name>
         <load_address>0x60c</load_address>
         <run_address>0x60c</run_address>
         <size>0xa8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_frame</name>
         <load_address>0x6b4</load_address>
         <run_address>0x6b4</run_address>
         <size>0x18c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-b7">
         <name>.debug_frame</name>
         <load_address>0x840</load_address>
         <run_address>0x840</run_address>
         <size>0x198</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_frame</name>
         <load_address>0x9d8</load_address>
         <run_address>0x9d8</run_address>
         <size>0xc4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-44">
         <name>.debug_frame</name>
         <load_address>0xa9c</load_address>
         <run_address>0xa9c</run_address>
         <size>0x140</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-bf">
         <name>.debug_frame</name>
         <load_address>0xbdc</load_address>
         <run_address>0xbdc</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-13d">
         <name>.debug_frame</name>
         <load_address>0xc1c</load_address>
         <run_address>0xc1c</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-17b">
         <name>.debug_frame</name>
         <load_address>0xc3c</load_address>
         <run_address>0xc3c</run_address>
         <size>0x234</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-151">
         <name>.debug_frame</name>
         <load_address>0xe70</load_address>
         <run_address>0xe70</run_address>
         <size>0x408</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-166">
         <name>.debug_frame</name>
         <load_address>0x1278</load_address>
         <run_address>0x1278</run_address>
         <size>0x1b8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-63">
         <name>.debug_frame</name>
         <load_address>0x1430</load_address>
         <run_address>0x1430</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-d1">
         <name>.debug_frame</name>
         <load_address>0x14c0</load_address>
         <run_address>0x14c0</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-47"/>
      </object_component>
      <object_component id="oc-91">
         <name>.debug_frame</name>
         <load_address>0x15c0</load_address>
         <run_address>0x15c0</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-48"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.debug_frame</name>
         <load_address>0x15e0</load_address>
         <run_address>0x15e0</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4a"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_frame</name>
         <load_address>0x1618</load_address>
         <run_address>0x1618</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4b"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_frame</name>
         <load_address>0x1640</load_address>
         <run_address>0x1640</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-c8">
         <name>.debug_frame</name>
         <load_address>0x1670</load_address>
         <run_address>0x1670</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-52"/>
      </object_component>
      <object_component id="oc-125">
         <name>.debug_frame</name>
         <load_address>0x16a0</load_address>
         <run_address>0x16a0</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-ff"/>
      </object_component>
      <object_component id="oc-1e2">
         <name>.debug_frame</name>
         <load_address>0x16c0</load_address>
         <run_address>0x16c0</run_address>
         <size>0x6c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-128"/>
      </object_component>
      <object_component id="oc-1c0">
         <name>.debug_frame</name>
         <load_address>0x172c</load_address>
         <run_address>0x172c</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-129"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x304</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b5">
         <name>.debug_line</name>
         <load_address>0x304</load_address>
         <run_address>0x304</run_address>
         <size>0xd76</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_line</name>
         <load_address>0x107a</load_address>
         <run_address>0x107a</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-6e">
         <name>.debug_line</name>
         <load_address>0x1132</load_address>
         <run_address>0x1132</run_address>
         <size>0x48b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_line</name>
         <load_address>0x15bd</load_address>
         <run_address>0x15bd</run_address>
         <size>0x766</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-ba">
         <name>.debug_line</name>
         <load_address>0x1d23</load_address>
         <run_address>0x1d23</run_address>
         <size>0xad6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_line</name>
         <load_address>0x27f9</load_address>
         <run_address>0x27f9</run_address>
         <size>0x338</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_line</name>
         <load_address>0x2b31</load_address>
         <run_address>0x2b31</run_address>
         <size>0x493</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-be">
         <name>.debug_line</name>
         <load_address>0x2fc4</load_address>
         <run_address>0x2fc4</run_address>
         <size>0x1f1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-13c">
         <name>.debug_line</name>
         <load_address>0x31b5</load_address>
         <run_address>0x31b5</run_address>
         <size>0x179</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-178">
         <name>.debug_line</name>
         <load_address>0x332e</load_address>
         <run_address>0x332e</run_address>
         <size>0xc1b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-153">
         <name>.debug_line</name>
         <load_address>0x3f49</load_address>
         <run_address>0x3f49</run_address>
         <size>0x176f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-168">
         <name>.debug_line</name>
         <load_address>0x56b8</load_address>
         <run_address>0x56b8</run_address>
         <size>0xa18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-4d">
         <name>.debug_line</name>
         <load_address>0x60d0</load_address>
         <run_address>0x60d0</run_address>
         <size>0x1dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-d2">
         <name>.debug_line</name>
         <load_address>0x62ac</load_address>
         <run_address>0x62ac</run_address>
         <size>0x51a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-47"/>
      </object_component>
      <object_component id="oc-90">
         <name>.debug_line</name>
         <load_address>0x67c6</load_address>
         <run_address>0x67c6</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-48"/>
      </object_component>
      <object_component id="oc-60">
         <name>.debug_line</name>
         <load_address>0x6804</load_address>
         <run_address>0x6804</run_address>
         <size>0xfe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4a"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_line</name>
         <load_address>0x6902</load_address>
         <run_address>0x6902</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4b"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_line</name>
         <load_address>0x69c2</load_address>
         <run_address>0x69c2</run_address>
         <size>0x1c8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-cb">
         <name>.debug_line</name>
         <load_address>0x6b8a</load_address>
         <run_address>0x6b8a</run_address>
         <size>0x67</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-52"/>
      </object_component>
      <object_component id="oc-124">
         <name>.debug_line</name>
         <load_address>0x6bf1</load_address>
         <run_address>0x6bf1</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ff"/>
      </object_component>
      <object_component id="oc-1b7">
         <name>.debug_line</name>
         <load_address>0x6c32</load_address>
         <run_address>0x6c32</run_address>
         <size>0x107</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10f"/>
      </object_component>
      <object_component id="oc-1bc">
         <name>.debug_line</name>
         <load_address>0x6d39</load_address>
         <run_address>0x6d39</run_address>
         <size>0xb9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-112"/>
      </object_component>
      <object_component id="oc-1ab">
         <name>.debug_line</name>
         <load_address>0x6df2</load_address>
         <run_address>0x6df2</run_address>
         <size>0xe0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-113"/>
      </object_component>
      <object_component id="oc-1cd">
         <name>.debug_line</name>
         <load_address>0x6ed2</load_address>
         <run_address>0x6ed2</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-116"/>
      </object_component>
      <object_component id="oc-1ad">
         <name>.debug_line</name>
         <load_address>0x6f92</load_address>
         <run_address>0x6f92</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-118"/>
      </object_component>
      <object_component id="oc-18c">
         <name>.debug_line</name>
         <load_address>0x704a</load_address>
         <run_address>0x704a</run_address>
         <size>0xbc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11a"/>
      </object_component>
      <object_component id="oc-1d0">
         <name>.debug_line</name>
         <load_address>0x7106</load_address>
         <run_address>0x7106</run_address>
         <size>0xc7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11e"/>
      </object_component>
      <object_component id="oc-1c9">
         <name>.debug_line</name>
         <load_address>0x71cd</load_address>
         <run_address>0x71cd</run_address>
         <size>0xc7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11f"/>
      </object_component>
      <object_component id="oc-1b1">
         <name>.debug_line</name>
         <load_address>0x7294</load_address>
         <run_address>0x7294</run_address>
         <size>0xcc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-120"/>
      </object_component>
      <object_component id="oc-55">
         <name>.debug_line</name>
         <load_address>0x7360</load_address>
         <run_address>0x7360</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-121"/>
      </object_component>
      <object_component id="oc-1da">
         <name>.debug_line</name>
         <load_address>0x7404</load_address>
         <run_address>0x7404</run_address>
         <size>0x104</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-125"/>
      </object_component>
      <object_component id="oc-1e3">
         <name>.debug_line</name>
         <load_address>0x7508</load_address>
         <run_address>0x7508</run_address>
         <size>0x2ef</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-128"/>
      </object_component>
      <object_component id="oc-1c1">
         <name>.debug_line</name>
         <load_address>0x77f7</load_address>
         <run_address>0x77f7</run_address>
         <size>0xb5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-129"/>
      </object_component>
      <object_component id="oc-ab">
         <name>.debug_line</name>
         <load_address>0x78ac</load_address>
         <run_address>0x78ac</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12b"/>
      </object_component>
      <object_component id="oc-19f">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-177">
         <name>.debug_loc</name>
         <load_address>0x13</load_address>
         <run_address>0x13</run_address>
         <size>0x816</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-14f">
         <name>.debug_loc</name>
         <load_address>0x829</load_address>
         <run_address>0x829</run_address>
         <size>0x1a27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-167">
         <name>.debug_loc</name>
         <load_address>0x2250</load_address>
         <run_address>0x2250</run_address>
         <size>0x7bc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.debug_loc</name>
         <load_address>0x2a0c</load_address>
         <run_address>0x2a0c</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-d0">
         <name>.debug_loc</name>
         <load_address>0x2ae4</load_address>
         <run_address>0x2ae4</run_address>
         <size>0x424</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-47"/>
      </object_component>
      <object_component id="oc-5c">
         <name>.debug_loc</name>
         <load_address>0x2f08</load_address>
         <run_address>0x2f08</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4a"/>
      </object_component>
      <object_component id="oc-5a">
         <name>.debug_loc</name>
         <load_address>0x3074</load_address>
         <run_address>0x3074</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4b"/>
      </object_component>
      <object_component id="oc-52">
         <name>.debug_loc</name>
         <load_address>0x30e3</load_address>
         <run_address>0x30e3</run_address>
         <size>0x167</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.debug_loc</name>
         <load_address>0x324a</load_address>
         <run_address>0x324a</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-52"/>
      </object_component>
      <object_component id="oc-1e0">
         <name>.debug_loc</name>
         <load_address>0x3270</load_address>
         <run_address>0x3270</run_address>
         <size>0x363</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-128"/>
      </object_component>
      <object_component id="oc-1d5">
         <name>.debug_loc</name>
         <load_address>0x35d3</load_address>
         <run_address>0x35d3</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-129"/>
      </object_component>
      <object_component id="oc-1b6">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10f"/>
      </object_component>
      <object_component id="oc-1bb">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-112"/>
      </object_component>
      <object_component id="oc-1aa">
         <name>.debug_aranges</name>
         <load_address>0x40</load_address>
         <run_address>0x40</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-113"/>
      </object_component>
      <object_component id="oc-1cc">
         <name>.debug_aranges</name>
         <load_address>0x60</load_address>
         <run_address>0x60</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-116"/>
      </object_component>
      <object_component id="oc-1af">
         <name>.debug_aranges</name>
         <load_address>0x80</load_address>
         <run_address>0x80</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-118"/>
      </object_component>
      <object_component id="oc-18b">
         <name>.debug_aranges</name>
         <load_address>0xa0</load_address>
         <run_address>0xa0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11a"/>
      </object_component>
      <object_component id="oc-1cf">
         <name>.debug_aranges</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11e"/>
      </object_component>
      <object_component id="oc-1c8">
         <name>.debug_aranges</name>
         <load_address>0xe0</load_address>
         <run_address>0xe0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11f"/>
      </object_component>
      <object_component id="oc-1b2">
         <name>.debug_aranges</name>
         <load_address>0x100</load_address>
         <run_address>0x100</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-120"/>
      </object_component>
      <object_component id="oc-57">
         <name>.debug_aranges</name>
         <load_address>0x120</load_address>
         <run_address>0x120</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-121"/>
      </object_component>
      <object_component id="oc-1d8">
         <name>.debug_aranges</name>
         <load_address>0x140</load_address>
         <run_address>0x140</run_address>
         <size>0x30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-125"/>
      </object_component>
      <object_component id="oc-a9">
         <name>.debug_aranges</name>
         <load_address>0x170</load_address>
         <run_address>0x170</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12b"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x2698</size>
         <contents>
            <object_component_ref idref="oc-b6"/>
            <object_component_ref idref="oc-c5"/>
            <object_component_ref idref="oc-6b"/>
            <object_component_ref idref="oc-e8"/>
            <object_component_ref idref="oc-1b8"/>
            <object_component_ref idref="oc-10b"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-18d"/>
            <object_component_ref idref="oc-154"/>
            <object_component_ref idref="oc-15f"/>
            <object_component_ref idref="oc-10f"/>
            <object_component_ref idref="oc-1b4"/>
            <object_component_ref idref="oc-e7"/>
            <object_component_ref idref="oc-ea"/>
            <object_component_ref idref="oc-eb"/>
            <object_component_ref idref="oc-1a8"/>
            <object_component_ref idref="oc-14d"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-c0"/>
            <object_component_ref idref="oc-1e4"/>
            <object_component_ref idref="oc-102"/>
            <object_component_ref idref="oc-105"/>
            <object_component_ref idref="oc-b1"/>
            <object_component_ref idref="oc-1de"/>
            <object_component_ref idref="oc-1ce"/>
            <object_component_ref idref="oc-1bf"/>
            <object_component_ref idref="oc-1c6"/>
            <object_component_ref idref="oc-c2"/>
            <object_component_ref idref="oc-93"/>
            <object_component_ref idref="oc-1b0"/>
            <object_component_ref idref="oc-73"/>
            <object_component_ref idref="oc-ef"/>
            <object_component_ref idref="oc-10e"/>
            <object_component_ref idref="oc-16d"/>
            <object_component_ref idref="oc-16b"/>
            <object_component_ref idref="oc-ee"/>
            <object_component_ref idref="oc-f0"/>
            <object_component_ref idref="oc-17c"/>
            <object_component_ref idref="oc-106"/>
            <object_component_ref idref="oc-f1"/>
            <object_component_ref idref="oc-f2"/>
            <object_component_ref idref="oc-ec"/>
            <object_component_ref idref="oc-1ca"/>
            <object_component_ref idref="oc-155"/>
            <object_component_ref idref="oc-ed"/>
            <object_component_ref idref="oc-189"/>
            <object_component_ref idref="oc-1db"/>
            <object_component_ref idref="oc-ce"/>
            <object_component_ref idref="oc-1d6"/>
            <object_component_ref idref="oc-85"/>
            <object_component_ref idref="oc-1b9"/>
            <object_component_ref idref="oc-1ac"/>
            <object_component_ref idref="oc-142"/>
            <object_component_ref idref="oc-17e"/>
            <object_component_ref idref="oc-79"/>
            <object_component_ref idref="oc-fd"/>
            <object_component_ref idref="oc-109"/>
            <object_component_ref idref="oc-10c"/>
            <object_component_ref idref="oc-10a"/>
            <object_component_ref idref="oc-10d"/>
            <object_component_ref idref="oc-fe"/>
            <object_component_ref idref="oc-1a0"/>
            <object_component_ref idref="oc-184"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-17d"/>
            <object_component_ref idref="oc-e9"/>
            <object_component_ref idref="oc-141"/>
            <object_component_ref idref="oc-16c"/>
            <object_component_ref idref="oc-9f"/>
            <object_component_ref idref="oc-146"/>
            <object_component_ref idref="oc-147"/>
            <object_component_ref idref="oc-13f"/>
            <object_component_ref idref="oc-144"/>
            <object_component_ref idref="oc-14b"/>
            <object_component_ref idref="oc-160"/>
            <object_component_ref idref="oc-157"/>
            <object_component_ref idref="oc-14e"/>
            <object_component_ref idref="oc-16e"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-140"/>
            <object_component_ref idref="oc-137"/>
            <object_component_ref idref="oc-9e"/>
            <object_component_ref idref="oc-143"/>
            <object_component_ref idref="oc-133"/>
            <object_component_ref idref="oc-fc"/>
            <object_component_ref idref="oc-149"/>
            <object_component_ref idref="oc-145"/>
            <object_component_ref idref="oc-17f"/>
            <object_component_ref idref="oc-13a"/>
            <object_component_ref idref="oc-187"/>
            <object_component_ref idref="oc-136"/>
            <object_component_ref idref="oc-14a"/>
            <object_component_ref idref="oc-138"/>
            <object_component_ref idref="oc-134"/>
            <object_component_ref idref="oc-156"/>
            <object_component_ref idref="oc-139"/>
            <object_component_ref idref="oc-135"/>
            <object_component_ref idref="oc-a0"/>
            <object_component_ref idref="oc-186"/>
            <object_component_ref idref="oc-16f"/>
            <object_component_ref idref="oc-bb"/>
            <object_component_ref idref="oc-c1"/>
            <object_component_ref idref="oc-c3"/>
            <object_component_ref idref="oc-c4"/>
            <object_component_ref idref="oc-5b"/>
            <object_component_ref idref="oc-fb"/>
            <object_component_ref idref="oc-148"/>
            <object_component_ref idref="oc-14c"/>
            <object_component_ref idref="oc-159"/>
            <object_component_ref idref="oc-15a"/>
            <object_component_ref idref="oc-81"/>
            <object_component_ref idref="oc-78"/>
            <object_component_ref idref="oc-176"/>
            <object_component_ref idref="oc-72"/>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-77"/>
            <object_component_ref idref="oc-165"/>
            <object_component_ref idref="oc-a7"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-185"/>
            <object_component_ref idref="oc-158"/>
            <object_component_ref idref="oc-f3"/>
            <object_component_ref idref="oc-a5"/>
            <object_component_ref idref="oc-13b"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-c7"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-122"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-8f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x2f20</load_address>
         <run_address>0x2f20</run_address>
         <size>0x38</size>
         <contents>
            <object_component_ref idref="oc-22a"/>
            <object_component_ref idref="oc-228"/>
            <object_component_ref idref="oc-22b"/>
            <object_component_ref idref="oc-229"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0x2758</load_address>
         <run_address>0x2758</run_address>
         <size>0x7c8</size>
         <contents>
            <object_component_ref idref="oc-188"/>
            <object_component_ref idref="oc-164"/>
            <object_component_ref idref="oc-162"/>
            <object_component_ref idref="oc-c6"/>
            <object_component_ref idref="oc-110"/>
            <object_component_ref idref="oc-118"/>
            <object_component_ref idref="oc-121"/>
            <object_component_ref idref="oc-117"/>
            <object_component_ref idref="oc-114"/>
            <object_component_ref idref="oc-119"/>
            <object_component_ref idref="oc-120"/>
            <object_component_ref idref="oc-11e"/>
            <object_component_ref idref="oc-11d"/>
            <object_component_ref idref="oc-113"/>
            <object_component_ref idref="oc-11f"/>
            <object_component_ref idref="oc-111"/>
            <object_component_ref idref="oc-11c"/>
            <object_component_ref idref="oc-112"/>
            <object_component_ref idref="oc-115"/>
            <object_component_ref idref="oc-116"/>
            <object_component_ref idref="oc-181"/>
            <object_component_ref idref="oc-183"/>
            <object_component_ref idref="oc-171"/>
            <object_component_ref idref="oc-173"/>
            <object_component_ref idref="oc-175"/>
            <object_component_ref idref="oc-11a"/>
            <object_component_ref idref="oc-11b"/>
            <object_component_ref idref="oc-15c"/>
            <object_component_ref idref="oc-15e"/>
            <object_component_ref idref="oc-18e"/>
            <object_component_ref idref="oc-15b"/>
            <object_component_ref idref="oc-15d"/>
            <object_component_ref idref="oc-163"/>
            <object_component_ref idref="oc-161"/>
            <object_component_ref idref="oc-180"/>
            <object_component_ref idref="oc-182"/>
            <object_component_ref idref="oc-170"/>
            <object_component_ref idref="oc-172"/>
            <object_component_ref idref="oc-174"/>
            <object_component_ref idref="oc-191"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-1f2"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x202001d4</run_address>
         <size>0x13</size>
         <contents>
            <object_component_ref idref="oc-a2"/>
            <object_component_ref idref="oc-a1"/>
            <object_component_ref idref="oc-7b"/>
            <object_component_ref idref="oc-7c"/>
            <object_component_ref idref="oc-18f"/>
            <object_component_ref idref="oc-190"/>
            <object_component_ref idref="oc-107"/>
            <object_component_ref idref="oc-108"/>
            <object_component_ref idref="oc-ff"/>
            <object_component_ref idref="oc-74"/>
            <object_component_ref idref="oc-82"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x20200000</run_address>
         <size>0x1d1</size>
         <contents>
            <object_component_ref idref="oc-7d"/>
            <object_component_ref idref="oc-7a"/>
            <object_component_ref idref="oc-f4"/>
            <object_component_ref idref="oc-f5"/>
            <object_component_ref idref="oc-f6"/>
            <object_component_ref idref="oc-f7"/>
            <object_component_ref idref="oc-f8"/>
            <object_component_ref idref="oc-a6"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20203e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-22d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1e9" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1ea" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1eb" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1ec" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1ed" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1ee" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1f0" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-20c" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1c87</size>
         <contents>
            <object_component_ref idref="oc-70"/>
            <object_component_ref idref="oc-f9"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-a3"/>
            <object_component_ref idref="oc-7e"/>
            <object_component_ref idref="oc-100"/>
            <object_component_ref idref="oc-75"/>
            <object_component_ref idref="oc-83"/>
            <object_component_ref idref="oc-103"/>
            <object_component_ref idref="oc-19d"/>
            <object_component_ref idref="oc-1a5"/>
            <object_component_ref idref="oc-1a1"/>
            <object_component_ref idref="oc-1a3"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-12c"/>
            <object_component_ref idref="oc-af"/>
            <object_component_ref idref="oc-88"/>
            <object_component_ref idref="oc-58"/>
            <object_component_ref idref="oc-50"/>
            <object_component_ref idref="oc-126"/>
            <object_component_ref idref="oc-192"/>
            <object_component_ref idref="oc-1c5"/>
            <object_component_ref idref="oc-1d2"/>
            <object_component_ref idref="oc-1bd"/>
            <object_component_ref idref="oc-1dd"/>
            <object_component_ref idref="oc-1be"/>
            <object_component_ref idref="oc-1a7"/>
            <object_component_ref idref="oc-1e5"/>
            <object_component_ref idref="oc-1dc"/>
            <object_component_ref idref="oc-1c4"/>
            <object_component_ref idref="oc-87"/>
            <object_component_ref idref="oc-1e6"/>
            <object_component_ref idref="oc-1e7"/>
            <object_component_ref idref="oc-1d3"/>
            <object_component_ref idref="oc-e0"/>
            <object_component_ref idref="oc-22f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-20e" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x105c7</size>
         <contents>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-b2"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-6d"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-b9"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-bc"/>
            <object_component_ref idref="oc-13e"/>
            <object_component_ref idref="oc-17a"/>
            <object_component_ref idref="oc-152"/>
            <object_component_ref idref="oc-16a"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-cf"/>
            <object_component_ref idref="oc-92"/>
            <object_component_ref idref="oc-5d"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-ca"/>
            <object_component_ref idref="oc-123"/>
            <object_component_ref idref="oc-1b5"/>
            <object_component_ref idref="oc-1ba"/>
            <object_component_ref idref="oc-1a9"/>
            <object_component_ref idref="oc-1cb"/>
            <object_component_ref idref="oc-1ae"/>
            <object_component_ref idref="oc-18a"/>
            <object_component_ref idref="oc-1d1"/>
            <object_component_ref idref="oc-1c7"/>
            <object_component_ref idref="oc-1b3"/>
            <object_component_ref idref="oc-56"/>
            <object_component_ref idref="oc-1d7"/>
            <object_component_ref idref="oc-1e1"/>
            <object_component_ref idref="oc-1c3"/>
            <object_component_ref idref="oc-a8"/>
            <object_component_ref idref="oc-22e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-210" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xd00</size>
         <contents>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-b3"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-6f"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-b8"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-bd"/>
            <object_component_ref idref="oc-179"/>
            <object_component_ref idref="oc-150"/>
            <object_component_ref idref="oc-169"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-d3"/>
            <object_component_ref idref="oc-5e"/>
            <object_component_ref idref="oc-53"/>
            <object_component_ref idref="oc-c9"/>
            <object_component_ref idref="oc-1d9"/>
            <object_component_ref idref="oc-1df"/>
            <object_component_ref idref="oc-1c2"/>
            <object_component_ref idref="oc-aa"/>
         </contents>
      </logical_group>
      <logical_group id="lg-212" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xab9e</size>
         <contents>
            <object_component_ref idref="oc-71"/>
            <object_component_ref idref="oc-fa"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-a4"/>
            <object_component_ref idref="oc-7f"/>
            <object_component_ref idref="oc-101"/>
            <object_component_ref idref="oc-76"/>
            <object_component_ref idref="oc-84"/>
            <object_component_ref idref="oc-104"/>
            <object_component_ref idref="oc-19e"/>
            <object_component_ref idref="oc-1a6"/>
            <object_component_ref idref="oc-1a2"/>
            <object_component_ref idref="oc-1a4"/>
            <object_component_ref idref="oc-4c"/>
            <object_component_ref idref="oc-12d"/>
            <object_component_ref idref="oc-b0"/>
            <object_component_ref idref="oc-89"/>
            <object_component_ref idref="oc-59"/>
            <object_component_ref idref="oc-51"/>
            <object_component_ref idref="oc-127"/>
            <object_component_ref idref="oc-193"/>
            <object_component_ref idref="oc-1e8"/>
            <object_component_ref idref="oc-1d4"/>
         </contents>
      </logical_group>
      <logical_group id="lg-214" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x175c</size>
         <contents>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-b4"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-6c"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-b7"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-bf"/>
            <object_component_ref idref="oc-13d"/>
            <object_component_ref idref="oc-17b"/>
            <object_component_ref idref="oc-151"/>
            <object_component_ref idref="oc-166"/>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-d1"/>
            <object_component_ref idref="oc-91"/>
            <object_component_ref idref="oc-5f"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-c8"/>
            <object_component_ref idref="oc-125"/>
            <object_component_ref idref="oc-1e2"/>
            <object_component_ref idref="oc-1c0"/>
         </contents>
      </logical_group>
      <logical_group id="lg-216" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x794c</size>
         <contents>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-b5"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-6e"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-ba"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-be"/>
            <object_component_ref idref="oc-13c"/>
            <object_component_ref idref="oc-178"/>
            <object_component_ref idref="oc-153"/>
            <object_component_ref idref="oc-168"/>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-d2"/>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-cb"/>
            <object_component_ref idref="oc-124"/>
            <object_component_ref idref="oc-1b7"/>
            <object_component_ref idref="oc-1bc"/>
            <object_component_ref idref="oc-1ab"/>
            <object_component_ref idref="oc-1cd"/>
            <object_component_ref idref="oc-1ad"/>
            <object_component_ref idref="oc-18c"/>
            <object_component_ref idref="oc-1d0"/>
            <object_component_ref idref="oc-1c9"/>
            <object_component_ref idref="oc-1b1"/>
            <object_component_ref idref="oc-55"/>
            <object_component_ref idref="oc-1da"/>
            <object_component_ref idref="oc-1e3"/>
            <object_component_ref idref="oc-1c1"/>
            <object_component_ref idref="oc-ab"/>
         </contents>
      </logical_group>
      <logical_group id="lg-218" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x35f3</size>
         <contents>
            <object_component_ref idref="oc-19f"/>
            <object_component_ref idref="oc-177"/>
            <object_component_ref idref="oc-14f"/>
            <object_component_ref idref="oc-167"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-d0"/>
            <object_component_ref idref="oc-5c"/>
            <object_component_ref idref="oc-5a"/>
            <object_component_ref idref="oc-52"/>
            <object_component_ref idref="oc-cd"/>
            <object_component_ref idref="oc-1e0"/>
            <object_component_ref idref="oc-1d5"/>
         </contents>
      </logical_group>
      <logical_group id="lg-222" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x198</size>
         <contents>
            <object_component_ref idref="oc-1b6"/>
            <object_component_ref idref="oc-1bb"/>
            <object_component_ref idref="oc-1aa"/>
            <object_component_ref idref="oc-1cc"/>
            <object_component_ref idref="oc-1af"/>
            <object_component_ref idref="oc-18b"/>
            <object_component_ref idref="oc-1cf"/>
            <object_component_ref idref="oc-1c8"/>
            <object_component_ref idref="oc-1b2"/>
            <object_component_ref idref="oc-57"/>
            <object_component_ref idref="oc-1d8"/>
            <object_component_ref idref="oc-a9"/>
         </contents>
      </logical_group>
      <logical_group id="lg-22c" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-23a" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2f58</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-23b" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0x1e7</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-f"/>
            <logical_group_ref idref="lg-e"/>
         </contents>
      </load_segment>
      <load_segment id="lg-23c" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20203e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x8000</length>
         <used_space>0x2f58</used_space>
         <unused_space>0x50a8</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0x2698</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x2758</start_address>
               <size>0x7c8</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x2f20</start_address>
               <size>0x38</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0x2f58</start_address>
               <size>0x50a8</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x4000</length>
         <used_space>0x3e4</used_space>
         <unused_space>0x3c1c</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-1ee"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-1f0"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x1d1</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <available_space>
               <start_address>0x202001d1</start_address>
               <size>0x3</size>
            </available_space>
            <allocated_space>
               <start_address>0x202001d4</start_address>
               <size>0x13</size>
               <logical_group_ref idref="lg-e"/>
            </allocated_space>
            <available_space>
               <start_address>0x202001e7</start_address>
               <size>0x3c19</size>
            </available_space>
            <allocated_space>
               <start_address>0x20203e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0xff</length>
         <used_space>0x0</used_space>
         <unused_space>0xff</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.data</name>
            <load_address>0x2f20</load_address>
            <load_size>0xf</load_size>
            <run_address>0x202001d4</run_address>
            <run_size>0x13</run_size>
            <compression>lzss</compression>
         </cprec>
         <cprec>
            <name>.bss</name>
            <load_address>0x2f3c</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0x1d1</run_size>
            <compression>zero_init</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_decompress_lzss</name>
      </handler>
      <handler>
         <index>0x1</index>
         <name>__TI_decompress_none</name>
      </handler>
      <handler>
         <index>0x2</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0x2f44</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0x2f54</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0x2f54</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0x2f30</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0x2f3c</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20204000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-43">
         <name>main</name>
         <value>0x197d</value>
         <object_component_ref idref="oc-93"/>
      </symbol>
      <symbol id="sm-44">
         <name>timerA_callback</name>
         <value>0x1a2b</value>
         <object_component_ref idref="oc-73"/>
      </symbol>
      <symbol id="sm-45">
         <name>timerB_callback</name>
         <value>0x1ee3</value>
         <object_component_ref idref="oc-85"/>
      </symbol>
      <symbol id="sm-46">
         <name>GROUP1_IRQHandler</name>
         <value>0x272f</value>
         <object_component_ref idref="oc-34"/>
      </symbol>
      <symbol id="sm-127">
         <name>SYSCFG_DL_init</name>
         <value>0x1785</value>
         <object_component_ref idref="oc-b1"/>
      </symbol>
      <symbol id="sm-128">
         <name>SYSCFG_DL_initPower</name>
         <value>0x1241</value>
         <object_component_ref idref="oc-e7"/>
      </symbol>
      <symbol id="sm-129">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0x7a1</value>
         <object_component_ref idref="oc-e8"/>
      </symbol>
      <symbol id="sm-12a">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0x21e5</value>
         <object_component_ref idref="oc-e9"/>
      </symbol>
      <symbol id="sm-12b">
         <name>SYSCFG_DL_PWM_6_init</name>
         <value>0x1315</value>
         <object_component_ref idref="oc-ea"/>
      </symbol>
      <symbol id="sm-12c">
         <name>SYSCFG_DL_PWM_7_init</name>
         <value>0x13a1</value>
         <object_component_ref idref="oc-eb"/>
      </symbol>
      <symbol id="sm-12d">
         <name>SYSCFG_DL_TIMER_8_init</name>
         <value>0x1cfd</value>
         <object_component_ref idref="oc-ec"/>
      </symbol>
      <symbol id="sm-12e">
         <name>SYSCFG_DL_TIMER_12_init</name>
         <value>0x1db9</value>
         <object_component_ref idref="oc-ed"/>
      </symbol>
      <symbol id="sm-12f">
         <name>SYSCFG_DL_UART_0_init</name>
         <value>0x1b65</value>
         <object_component_ref idref="oc-ee"/>
      </symbol>
      <symbol id="sm-130">
         <name>SYSCFG_DL_UART_1_init</name>
         <value>0x1a2d</value>
         <object_component_ref idref="oc-ef"/>
      </symbol>
      <symbol id="sm-131">
         <name>SYSCFG_DL_UART_3_init</name>
         <value>0x1bad</value>
         <object_component_ref idref="oc-f0"/>
      </symbol>
      <symbol id="sm-132">
         <name>SYSCFG_DL_SPI_IMU660RB_init</name>
         <value>0x1c7d</value>
         <object_component_ref idref="oc-f1"/>
      </symbol>
      <symbol id="sm-133">
         <name>SYSCFG_DL_TFT_SPI_init</name>
         <value>0x1cbd</value>
         <object_component_ref idref="oc-f2"/>
      </symbol>
      <symbol id="sm-134">
         <name>SYSCFG_DL_SYSTICK_init</name>
         <value>0x2709</value>
         <object_component_ref idref="oc-f3"/>
      </symbol>
      <symbol id="sm-135">
         <name>gPWM_6Backup</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-136">
         <name>gPWM_7Backup</name>
         <value>0x202000a0</value>
      </symbol>
      <symbol id="sm-137">
         <name>gUART_3Backup</name>
         <value>0x20200140</value>
      </symbol>
      <symbol id="sm-138">
         <name>gSPI_IMU660RBBackup</name>
         <value>0x20200170</value>
      </symbol>
      <symbol id="sm-139">
         <name>gTFT_SPIBackup</name>
         <value>0x20200198</value>
      </symbol>
      <symbol id="sm-144">
         <name>Default_Handler</name>
         <value>0x2747</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-145">
         <name>Reset_Handler</name>
         <value>0x274f</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-146">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-147">
         <name>NMI_Handler</name>
         <value>0x2747</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-148">
         <name>HardFault_Handler</name>
         <value>0x2747</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-149">
         <name>SVC_Handler</name>
         <value>0x2747</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-14a">
         <name>PendSV_Handler</name>
         <value>0x2747</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-14b">
         <name>SysTick_Handler</name>
         <value>0x2747</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-14c">
         <name>GROUP0_IRQHandler</name>
         <value>0x2747</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-14d">
         <name>ADC0_IRQHandler</name>
         <value>0x2747</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-14e">
         <name>ADC1_IRQHandler</name>
         <value>0x2747</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-14f">
         <name>CANFD0_IRQHandler</name>
         <value>0x2747</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-150">
         <name>DAC0_IRQHandler</name>
         <value>0x2747</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-151">
         <name>SPI0_IRQHandler</name>
         <value>0x2747</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-152">
         <name>SPI1_IRQHandler</name>
         <value>0x2747</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-153">
         <name>UART1_IRQHandler</name>
         <value>0x2747</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-154">
         <name>UART2_IRQHandler</name>
         <value>0x2747</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-155">
         <name>TIMG0_IRQHandler</name>
         <value>0x2747</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-156">
         <name>TIMG6_IRQHandler</name>
         <value>0x2747</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-157">
         <name>TIMA0_IRQHandler</name>
         <value>0x2747</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-158">
         <name>TIMA1_IRQHandler</name>
         <value>0x2747</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-159">
         <name>TIMG7_IRQHandler</name>
         <value>0x2747</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-15a">
         <name>I2C0_IRQHandler</name>
         <value>0x2747</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-15b">
         <name>I2C1_IRQHandler</name>
         <value>0x2747</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-15c">
         <name>AES_IRQHandler</name>
         <value>0x2747</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-15d">
         <name>RTC_IRQHandler</name>
         <value>0x2747</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-15e">
         <name>DMA_IRQHandler</name>
         <value>0x2747</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-173">
         <name>left_counter</name>
         <value>0x202001d8</value>
         <object_component_ref idref="oc-a2"/>
      </symbol>
      <symbol id="sm-174">
         <name>right_counter</name>
         <value>0x202001da</value>
         <object_component_ref idref="oc-a1"/>
      </symbol>
      <symbol id="sm-175">
         <name>encoder_exti_callback</name>
         <value>0x595</value>
         <object_component_ref idref="oc-6b"/>
      </symbol>
      <symbol id="sm-1c2">
         <name>openmv_init</name>
         <value>0x2585</value>
         <object_component_ref idref="oc-c1"/>
      </symbol>
      <symbol id="sm-1c3">
         <name>openmv_analysis</name>
         <value>0x1fbd</value>
         <object_component_ref idref="oc-79"/>
      </symbol>
      <symbol id="sm-1c4">
         <name>openmvData</name>
         <value>0x202001c8</value>
      </symbol>
      <symbol id="sm-1c5">
         <name>UART3_IRQHandler</name>
         <value>0xc79</value>
         <object_component_ref idref="oc-3e"/>
      </symbol>
      <symbol id="sm-1c6">
         <name>openmv_is_data_valid</name>
         <value>0x1a81</value>
         <object_component_ref idref="oc-10e"/>
      </symbol>
      <symbol id="sm-1c7">
         <name>openmv_display_data</name>
         <value>0x349</value>
         <object_component_ref idref="oc-c5"/>
      </symbol>
      <symbol id="sm-201">
         <name>tft180_write_8bit_data</name>
         <value>0x20cd</value>
         <object_component_ref idref="oc-fe"/>
      </symbol>
      <symbol id="sm-202">
         <name>tft180_write_16bit_data</name>
         <value>0x1c39</value>
         <object_component_ref idref="oc-106"/>
      </symbol>
      <symbol id="sm-203">
         <name>tft180_clear_color</name>
         <value>0x15b9</value>
         <object_component_ref idref="oc-c0"/>
      </symbol>
      <symbol id="sm-204">
         <name>tft180_init</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-b6"/>
      </symbol>
      <symbol id="sm-205">
         <name>tft180_show_char_color</name>
         <value>0xb49</value>
         <object_component_ref idref="oc-10b"/>
      </symbol>
      <symbol id="sm-206">
         <name>ascii_font_8x16</name>
         <value>0x2758</value>
         <object_component_ref idref="oc-188"/>
      </symbol>
      <symbol id="sm-207">
         <name>func_float_to_str</name>
         <value>0x98d</value>
         <object_component_ref idref="oc-1b8"/>
      </symbol>
      <symbol id="sm-208">
         <name>tft180_show_num_color</name>
         <value>0xd99</value>
         <object_component_ref idref="oc-18d"/>
      </symbol>
      <symbol id="sm-209">
         <name>tft180_show_string_color</name>
         <value>0x191b</value>
         <object_component_ref idref="oc-c2"/>
      </symbol>
      <symbol id="sm-22a">
         <name>timerA_init</name>
         <value>0x259b</value>
         <object_component_ref idref="oc-c3"/>
      </symbol>
      <symbol id="sm-22b">
         <name>timerB_init</name>
         <value>0x25b1</value>
         <object_component_ref idref="oc-c4"/>
      </symbol>
      <symbol id="sm-22c">
         <name>TIMG8_IRQHandler</name>
         <value>0x2149</value>
         <object_component_ref idref="oc-39"/>
      </symbol>
      <symbol id="sm-22d">
         <name>TIMG12_IRQHandler</name>
         <value>0x2361</value>
         <object_component_ref idref="oc-48"/>
      </symbol>
      <symbol id="sm-22e">
         <name>get_system_time_ms</name>
         <value>0x2719</value>
         <object_component_ref idref="oc-a5"/>
      </symbol>
      <symbol id="sm-23f">
         <name>UART0_IRQHandler</name>
         <value>0x2171</value>
         <object_component_ref idref="oc-43"/>
      </symbol>
      <symbol id="sm-240">
         <name>uart_data</name>
         <value>0x202001e6</value>
         <object_component_ref idref="oc-82"/>
      </symbol>
      <symbol id="sm-24c">
         <name>delay_ms</name>
         <value>0x256f</value>
         <object_component_ref idref="oc-bb"/>
      </symbol>
      <symbol id="sm-24d">
         <name>delay_us</name>
         <value>0x16a5</value>
         <object_component_ref idref="oc-102"/>
      </symbol>
      <symbol id="sm-253">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-254">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-255">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-256">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-257">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-258">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-259">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-25a">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-25b">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-264">
         <name>DL_Common_delayCycles</name>
         <value>0x2725</value>
         <object_component_ref idref="oc-13b"/>
      </symbol>
      <symbol id="sm-271">
         <name>DL_SPI_init</name>
         <value>0x1bf5</value>
         <object_component_ref idref="oc-17c"/>
      </symbol>
      <symbol id="sm-272">
         <name>DL_SPI_setClockConfig</name>
         <value>0x2669</value>
         <object_component_ref idref="oc-176"/>
      </symbol>
      <symbol id="sm-28e">
         <name>DL_Timer_setClockConfig</name>
         <value>0x2329</value>
         <object_component_ref idref="oc-14e"/>
      </symbol>
      <symbol id="sm-28f">
         <name>DL_Timer_initTimerMode</name>
         <value>0xfa9</value>
         <object_component_ref idref="oc-15f"/>
      </symbol>
      <symbol id="sm-290">
         <name>DL_Timer_setCaptureCompareValue</name>
         <value>0x26f9</value>
         <object_component_ref idref="oc-158"/>
      </symbol>
      <symbol id="sm-291">
         <name>DL_Timer_setCaptCompUpdateMethod</name>
         <value>0x230d</value>
         <object_component_ref idref="oc-157"/>
      </symbol>
      <symbol id="sm-292">
         <name>DL_Timer_setCaptureCompareOutCtl</name>
         <value>0x24e5</value>
         <object_component_ref idref="oc-156"/>
      </symbol>
      <symbol id="sm-293">
         <name>DL_Timer_initFourCCPWMMode</name>
         <value>0xea5</value>
         <object_component_ref idref="oc-154"/>
      </symbol>
      <symbol id="sm-2a0">
         <name>DL_UART_init</name>
         <value>0x1b1d</value>
         <object_component_ref idref="oc-16b"/>
      </symbol>
      <symbol id="sm-2a1">
         <name>DL_UART_setClockConfig</name>
         <value>0x26b1</value>
         <object_component_ref idref="oc-165"/>
      </symbol>
      <symbol id="sm-2b5">
         <name>_c_int00_noargs</name>
         <value>0x2199</value>
         <object_component_ref idref="oc-64"/>
      </symbol>
      <symbol id="sm-2b6">
         <name>__stack</name>
         <value>0x20203e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-2c2">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0x1e6d</value>
         <object_component_ref idref="oc-ce"/>
      </symbol>
      <symbol id="sm-2ca">
         <name>_system_pre_init</name>
         <value>0x2753</value>
         <object_component_ref idref="oc-8f"/>
      </symbol>
      <symbol id="sm-2d5">
         <name>__TI_zero_init_nomemset</name>
         <value>0x25c7</value>
         <object_component_ref idref="oc-5b"/>
      </symbol>
      <symbol id="sm-2de">
         <name>__TI_decompress_none</name>
         <value>0x26d5</value>
         <object_component_ref idref="oc-20"/>
      </symbol>
      <symbol id="sm-2e9">
         <name>__TI_decompress_lzss</name>
         <value>0x153d</value>
         <object_component_ref idref="oc-1c"/>
      </symbol>
      <symbol id="sm-2f8">
         <name>abort</name>
         <value>0x2741</value>
         <object_component_ref idref="oc-c7"/>
      </symbol>
      <symbol id="sm-30c">
         <name>HOSTexit</name>
         <value>0x274b</value>
         <object_component_ref idref="oc-122"/>
      </symbol>
      <symbol id="sm-30d">
         <name>C$$EXIT</name>
         <value>0x274a</value>
         <object_component_ref idref="oc-122"/>
      </symbol>
      <symbol id="sm-322">
         <name>__aeabi_fadd</name>
         <value>0x1173</value>
         <object_component_ref idref="oc-1b4"/>
      </symbol>
      <symbol id="sm-323">
         <name>__addsf3</name>
         <value>0x1173</value>
         <object_component_ref idref="oc-1b4"/>
      </symbol>
      <symbol id="sm-324">
         <name>__aeabi_fsub</name>
         <value>0x1169</value>
         <object_component_ref idref="oc-1b4"/>
      </symbol>
      <symbol id="sm-325">
         <name>__subsf3</name>
         <value>0x1169</value>
         <object_component_ref idref="oc-1b4"/>
      </symbol>
      <symbol id="sm-32b">
         <name>__muldsi3</name>
         <value>0x1ee5</value>
         <object_component_ref idref="oc-1b9"/>
      </symbol>
      <symbol id="sm-331">
         <name>__aeabi_fmul</name>
         <value>0x142d</value>
         <object_component_ref idref="oc-1a8"/>
      </symbol>
      <symbol id="sm-332">
         <name>__mulsf3</name>
         <value>0x142d</value>
         <object_component_ref idref="oc-1a8"/>
      </symbol>
      <symbol id="sm-338">
         <name>__aeabi_f2d</name>
         <value>0x1d3d</value>
         <object_component_ref idref="oc-1ca"/>
      </symbol>
      <symbol id="sm-339">
         <name>__extendsfdf2</name>
         <value>0x1d3d</value>
         <object_component_ref idref="oc-1ca"/>
      </symbol>
      <symbol id="sm-33f">
         <name>__aeabi_f2iz</name>
         <value>0x1f21</value>
         <object_component_ref idref="oc-1ac"/>
      </symbol>
      <symbol id="sm-340">
         <name>__fixsfsi</name>
         <value>0x1f21</value>
         <object_component_ref idref="oc-1ac"/>
      </symbol>
      <symbol id="sm-346">
         <name>__aeabi_i2f</name>
         <value>0x1df5</value>
         <object_component_ref idref="oc-189"/>
      </symbol>
      <symbol id="sm-347">
         <name>__floatsisf</name>
         <value>0x1df5</value>
         <object_component_ref idref="oc-189"/>
      </symbol>
      <symbol id="sm-34d">
         <name>__aeabi_dcmpeq</name>
         <value>0x1855</value>
         <object_component_ref idref="oc-1ce"/>
      </symbol>
      <symbol id="sm-34e">
         <name>__aeabi_dcmplt</name>
         <value>0x1869</value>
         <object_component_ref idref="oc-1ce"/>
      </symbol>
      <symbol id="sm-34f">
         <name>__aeabi_dcmple</name>
         <value>0x187d</value>
         <object_component_ref idref="oc-1ce"/>
      </symbol>
      <symbol id="sm-350">
         <name>__aeabi_dcmpge</name>
         <value>0x1891</value>
         <object_component_ref idref="oc-1ce"/>
      </symbol>
      <symbol id="sm-351">
         <name>__aeabi_dcmpgt</name>
         <value>0x18a5</value>
         <object_component_ref idref="oc-1ce"/>
      </symbol>
      <symbol id="sm-357">
         <name>__aeabi_fcmpeq</name>
         <value>0x18b9</value>
         <object_component_ref idref="oc-1c6"/>
      </symbol>
      <symbol id="sm-358">
         <name>__aeabi_fcmplt</name>
         <value>0x18cd</value>
         <object_component_ref idref="oc-1c6"/>
      </symbol>
      <symbol id="sm-359">
         <name>__aeabi_fcmple</name>
         <value>0x18e1</value>
         <object_component_ref idref="oc-1c6"/>
      </symbol>
      <symbol id="sm-35a">
         <name>__aeabi_fcmpge</name>
         <value>0x18f5</value>
         <object_component_ref idref="oc-1c6"/>
      </symbol>
      <symbol id="sm-35b">
         <name>__aeabi_fcmpgt</name>
         <value>0x1909</value>
         <object_component_ref idref="oc-1c6"/>
      </symbol>
      <symbol id="sm-361">
         <name>__aeabi_idiv</name>
         <value>0x19d5</value>
         <object_component_ref idref="oc-1b0"/>
      </symbol>
      <symbol id="sm-362">
         <name>__aeabi_idivmod</name>
         <value>0x19d5</value>
         <object_component_ref idref="oc-1b0"/>
      </symbol>
      <symbol id="sm-368">
         <name>__aeabi_memcpy</name>
         <value>0x2739</value>
         <object_component_ref idref="oc-54"/>
      </symbol>
      <symbol id="sm-369">
         <name>__aeabi_memcpy4</name>
         <value>0x2739</value>
         <object_component_ref idref="oc-54"/>
      </symbol>
      <symbol id="sm-36a">
         <name>__aeabi_memcpy8</name>
         <value>0x2739</value>
         <object_component_ref idref="oc-54"/>
      </symbol>
      <symbol id="sm-373">
         <name>__eqsf2</name>
         <value>0x1ea9</value>
         <object_component_ref idref="oc-1d6"/>
      </symbol>
      <symbol id="sm-374">
         <name>__lesf2</name>
         <value>0x1ea9</value>
         <object_component_ref idref="oc-1d6"/>
      </symbol>
      <symbol id="sm-375">
         <name>__ltsf2</name>
         <value>0x1ea9</value>
         <object_component_ref idref="oc-1d6"/>
      </symbol>
      <symbol id="sm-376">
         <name>__nesf2</name>
         <value>0x1ea9</value>
         <object_component_ref idref="oc-1d6"/>
      </symbol>
      <symbol id="sm-377">
         <name>__cmpsf2</name>
         <value>0x1ea9</value>
         <object_component_ref idref="oc-1d6"/>
      </symbol>
      <symbol id="sm-378">
         <name>__gtsf2</name>
         <value>0x1e31</value>
         <object_component_ref idref="oc-1db"/>
      </symbol>
      <symbol id="sm-379">
         <name>__gesf2</name>
         <value>0x1e31</value>
         <object_component_ref idref="oc-1db"/>
      </symbol>
      <symbol id="sm-387">
         <name>__ledf2</name>
         <value>0x17ed</value>
         <object_component_ref idref="oc-1de"/>
      </symbol>
      <symbol id="sm-388">
         <name>__gedf2</name>
         <value>0x1631</value>
         <object_component_ref idref="oc-1e4"/>
      </symbol>
      <symbol id="sm-389">
         <name>__cmpdf2</name>
         <value>0x17ed</value>
         <object_component_ref idref="oc-1de"/>
      </symbol>
      <symbol id="sm-38a">
         <name>__eqdf2</name>
         <value>0x17ed</value>
         <object_component_ref idref="oc-1de"/>
      </symbol>
      <symbol id="sm-38b">
         <name>__ltdf2</name>
         <value>0x17ed</value>
         <object_component_ref idref="oc-1de"/>
      </symbol>
      <symbol id="sm-38c">
         <name>__nedf2</name>
         <value>0x17ed</value>
         <object_component_ref idref="oc-1de"/>
      </symbol>
      <symbol id="sm-38d">
         <name>__gtdf2</name>
         <value>0x1631</value>
         <object_component_ref idref="oc-1e4"/>
      </symbol>
      <symbol id="sm-397">
         <name>__aeabi_idiv0</name>
         <value>0x18b7</value>
         <object_component_ref idref="oc-1bf"/>
      </symbol>
      <symbol id="sm-3a1">
         <name>TI_memcpy_small</name>
         <value>0x26c3</value>
         <object_component_ref idref="oc-a7"/>
      </symbol>
      <symbol id="sm-3a2">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-3a5">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-3a6">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
