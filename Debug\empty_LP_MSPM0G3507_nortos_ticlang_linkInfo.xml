<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v4.0.3.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <command_line>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\bin\tiarmlnk -ID:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib -o empty_LP_MSPM0G3507_nortos_ticlang.out -mempty_LP_MSPM0G3507_nortos_ticlang.map -iC:/ti/mspm0_sdk_2_05_01_00/source -iC:/Users/<USER>/workspace_ccstheia/empty_LP_MSPM0G3507_nortos_ticlang -iC:/Users/<USER>/workspace_ccstheia/empty_LP_MSPM0G3507_nortos_ticlang/Debug/syscfg -iD:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib --diag_wrap=off --display_error_number --warn_sections --xml_link_info=empty_LP_MSPM0G3507_nortos_ticlang_linkInfo.xml --rom_model ./empty.o ./ti_msp_dl_config.o ./startup_mspm0g350x_ticlang.o ./drivers/encoder.o ./drivers/flash.o ./drivers/imu660rb.o ./drivers/key.o ./drivers/lsm6dsr_reg.o ./drivers/motor.o ./drivers/openmv.o ./drivers/quaternion.o ./drivers/tft180.o ./drivers/timer.o ./soft/debug.o ./soft/delay.o ./soft/menu.o ./soft/pid.o ./soft/protocol.o ./soft/task.o ./soft/vofa.o -l./device_linker.cmd -ldevice.cmd.genlibs -llibc.a --start-group -llibc++.a -llibc++abi.a -llibc.a -llibsys.a -llibsysbm.a -llibclang_rt.builtins.a -llibclang_rt.profile.a --end-group --cg_opt_level=0</command_line>
   <link_time>0x688b17e4</link_time>
   <link_errors>0x0</link_errors>
   <output_file>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\empty_LP_MSPM0G3507_nortos_ticlang.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0x20f1</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>empty.o</file>
         <name>empty.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-4">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>encoder.o</file>
         <name>encoder.o</name>
      </input_file>
      <input_file id="fl-5">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>flash.o</file>
         <name>flash.o</name>
      </input_file>
      <input_file id="fl-6">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>imu660rb.o</file>
         <name>imu660rb.o</name>
      </input_file>
      <input_file id="fl-7">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>key.o</file>
         <name>key.o</name>
      </input_file>
      <input_file id="fl-8">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>lsm6dsr_reg.o</file>
         <name>lsm6dsr_reg.o</name>
      </input_file>
      <input_file id="fl-9">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>motor.o</file>
         <name>motor.o</name>
      </input_file>
      <input_file id="fl-a">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>openmv.o</file>
         <name>openmv.o</name>
      </input_file>
      <input_file id="fl-b">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>quaternion.o</file>
         <name>quaternion.o</name>
      </input_file>
      <input_file id="fl-c">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>tft180.o</file>
         <name>tft180.o</name>
      </input_file>
      <input_file id="fl-d">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>timer.o</file>
         <name>timer.o</name>
      </input_file>
      <input_file id="fl-e">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\soft\</path>
         <kind>object</kind>
         <file>debug.o</file>
         <name>debug.o</name>
      </input_file>
      <input_file id="fl-f">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\soft\</path>
         <kind>object</kind>
         <file>delay.o</file>
         <name>delay.o</name>
      </input_file>
      <input_file id="fl-10">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\soft\</path>
         <kind>object</kind>
         <file>menu.o</file>
         <name>menu.o</name>
      </input_file>
      <input_file id="fl-11">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\soft\</path>
         <kind>object</kind>
         <file>pid.o</file>
         <name>pid.o</name>
      </input_file>
      <input_file id="fl-12">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\soft\</path>
         <kind>object</kind>
         <file>protocol.o</file>
         <name>protocol.o</name>
      </input_file>
      <input_file id="fl-13">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\soft\</path>
         <kind>object</kind>
         <file>task.o</file>
         <name>task.o</name>
      </input_file>
      <input_file id="fl-14">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\soft\</path>
         <kind>object</kind>
         <file>vofa.o</file>
         <name>vofa.o</name>
      </input_file>
      <input_file id="fl-21">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-22">
         <path>C:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-23">
         <path>C:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_spi.o</name>
      </input_file>
      <input_file id="fl-24">
         <path>C:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_timer.o</name>
      </input_file>
      <input_file id="fl-25">
         <path>C:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_uart.o</name>
      </input_file>
      <input_file id="fl-3c">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>e_asin.c.obj</name>
      </input_file>
      <input_file id="fl-3d">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>e_atan2.c.obj</name>
      </input_file>
      <input_file id="fl-3e">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>e_sqrt.c.obj</name>
      </input_file>
      <input_file id="fl-3f">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>s_atan.c.obj</name>
      </input_file>
      <input_file id="fl-40">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>aeabi_portable.c.obj</name>
      </input_file>
      <input_file id="fl-41">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>assert.c.obj</name>
      </input_file>
      <input_file id="fl-42">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>defs.c.obj</name>
      </input_file>
      <input_file id="fl-43">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memory.c.obj</name>
      </input_file>
      <input_file id="fl-44">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>strlen.c.obj</name>
      </input_file>
      <input_file id="fl-45">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-46">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-47">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-48">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-49">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-4a">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-4b">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-4c">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-4d">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>sprintf.c.obj</name>
      </input_file>
      <input_file id="fl-4e">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fflush.c.obj</name>
      </input_file>
      <input_file id="fl-4f">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fputs.c.obj</name>
      </input_file>
      <input_file id="fl-50">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_io_perm.c.obj</name>
      </input_file>
      <input_file id="fl-51">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>setvbuf.c.obj</name>
      </input_file>
      <input_file id="fl-52">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-53">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-54">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-55">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_printfi.c.obj</name>
      </input_file>
      <input_file id="fl-56">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fopen.c.obj</name>
      </input_file>
      <input_file id="fl-57">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fseek.c.obj</name>
      </input_file>
      <input_file id="fl-58">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>s_frexp.c.obj</name>
      </input_file>
      <input_file id="fl-59">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>s_scalbn.c.obj</name>
      </input_file>
      <input_file id="fl-5a">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>wcslen.c.obj</name>
      </input_file>
      <input_file id="fl-5b">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_ltoa.c.obj</name>
      </input_file>
      <input_file id="fl-5c">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>atoi.c.obj</name>
      </input_file>
      <input_file id="fl-5d">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memccpy.c.obj</name>
      </input_file>
      <input_file id="fl-5e">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fclose.c.obj</name>
      </input_file>
      <input_file id="fl-5f">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>aeabi_ctype.S.obj</name>
      </input_file>
      <input_file id="fl-ff">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostexit.c.obj</name>
      </input_file>
      <input_file id="fl-100">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>write.c.obj</name>
      </input_file>
      <input_file id="fl-101">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>host_device.c.obj</name>
      </input_file>
      <input_file id="fl-102">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>remove.c.obj</name>
      </input_file>
      <input_file id="fl-103">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>open.c.obj</name>
      </input_file>
      <input_file id="fl-104">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>lseek.c.obj</name>
      </input_file>
      <input_file id="fl-105">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>close.c.obj</name>
      </input_file>
      <input_file id="fl-106">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>unlink.c.obj</name>
      </input_file>
      <input_file id="fl-107">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostclose.c.obj</name>
      </input_file>
      <input_file id="fl-108">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostlseek.c.obj</name>
      </input_file>
      <input_file id="fl-109">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostopen.c.obj</name>
      </input_file>
      <input_file id="fl-10a">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostread.c.obj</name>
      </input_file>
      <input_file id="fl-10b">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostrename.c.obj</name>
      </input_file>
      <input_file id="fl-10c">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostunlink.c.obj</name>
      </input_file>
      <input_file id="fl-10d">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostwrite.c.obj</name>
      </input_file>
      <input_file id="fl-10e">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>trgmsg.c.obj</name>
      </input_file>
      <input_file id="fl-10f">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>addsf3.S.obj</name>
      </input_file>
      <input_file id="fl-110">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>adddf3.S.obj</name>
      </input_file>
      <input_file id="fl-111">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldf3.S.obj</name>
      </input_file>
      <input_file id="fl-112">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldsi3.S.obj</name>
      </input_file>
      <input_file id="fl-113">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>mulsf3.S.obj</name>
      </input_file>
      <input_file id="fl-114">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>divsf3.S.obj</name>
      </input_file>
      <input_file id="fl-115">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>divdf3.S.obj</name>
      </input_file>
      <input_file id="fl-116">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>extendsfdf2.S.obj</name>
      </input_file>
      <input_file id="fl-117">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixdfsi.S.obj</name>
      </input_file>
      <input_file id="fl-118">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixsfsi.S.obj</name>
      </input_file>
      <input_file id="fl-119">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatsidf.S.obj</name>
      </input_file>
      <input_file id="fl-11a">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatsisf.S.obj</name>
      </input_file>
      <input_file id="fl-11b">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatunsisf.S.obj</name>
      </input_file>
      <input_file id="fl-11c">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldi3.S.obj</name>
      </input_file>
      <input_file id="fl-11d">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>truncdfsf2.S.obj</name>
      </input_file>
      <input_file id="fl-11e">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_dcmp.S.obj</name>
      </input_file>
      <input_file id="fl-11f">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_fcmp.S.obj</name>
      </input_file>
      <input_file id="fl-120">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_idivmod.S.obj</name>
      </input_file>
      <input_file id="fl-121">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-122">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-123">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uidivmod.S.obj</name>
      </input_file>
      <input_file id="fl-124">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uldivmod.S.obj</name>
      </input_file>
      <input_file id="fl-125">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>comparesf2.S.obj</name>
      </input_file>
      <input_file id="fl-126">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>udivmoddi4.S.obj</name>
      </input_file>
      <input_file id="fl-127">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>ashldi3.S.obj</name>
      </input_file>
      <input_file id="fl-128">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>comparedf2.c.obj</name>
      </input_file>
      <input_file id="fl-129">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_div0.c.obj</name>
      </input_file>
      <input_file id="fl-12a">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>getdevice.c.obj</name>
      </input_file>
      <input_file id="fl-12b">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-12c">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
      <input_file id="fl-12d">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>strcmp-armv6m.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-b6">
         <name>.text.tft180_init</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0x288</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-c5">
         <name>.text.openmv_display_data</name>
         <load_address>0x348</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x348</run_address>
         <size>0x27c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-6b">
         <name>.text.encoder_exti_callback</name>
         <load_address>0x5c4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5c4</run_address>
         <size>0x20c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-e8">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0x7d0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7d0</run_address>
         <size>0x1ec</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1a0">
         <name>.text.func_float_to_str</name>
         <load_address>0x9bc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x9bc</run_address>
         <size>0x1bc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-10b">
         <name>.text.tft180_show_char_color</name>
         <load_address>0xb78</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb78</run_address>
         <size>0x130</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.text.UART3_IRQHandler</name>
         <load_address>0xca8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xca8</run_address>
         <size>0x120</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-113">
         <name>.text.tft180_show_num_color</name>
         <load_address>0xdc8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xdc8</run_address>
         <size>0x10c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-15a">
         <name>.text.DL_Timer_initFourCCPWMMode</name>
         <load_address>0xed4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xed4</run_address>
         <size>0x104</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-165">
         <name>.text.DL_Timer_initTimerMode</name>
         <load_address>0xfd8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xfd8</run_address>
         <size>0xe8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-19c">
         <name>.text</name>
         <load_address>0x10c0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x10c0</run_address>
         <size>0xd8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-10f"/>
      </object_component>
      <object_component id="oc-e7">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0x1198</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1198</run_address>
         <size>0xd4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-ea">
         <name>.text.SYSCFG_DL_PWM_6_init</name>
         <load_address>0x126c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x126c</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-eb">
         <name>.text.SYSCFG_DL_PWM_7_init</name>
         <load_address>0x12f8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x12f8</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-190">
         <name>.text.__mulsf3</name>
         <load_address>0x1384</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1384</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-113"/>
      </object_component>
      <object_component id="oc-153">
         <name>.text.__NVIC_SetPriority</name>
         <load_address>0x1410</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1410</run_address>
         <size>0x84</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.text:decompress:lzss</name>
         <load_address>0x1494</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1494</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-c0">
         <name>.text.tft180_clear_color</name>
         <load_address>0x1510</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1510</run_address>
         <size>0x78</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-1e1">
         <name>.text.__gedf2</name>
         <load_address>0x1588</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1588</run_address>
         <size>0x74</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-128"/>
      </object_component>
      <object_component id="oc-102">
         <name>.text.delay_us</name>
         <load_address>0x15fc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x15fc</run_address>
         <size>0x74</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-105">
         <name>.text.tft180_set_region</name>
         <load_address>0x1670</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1670</run_address>
         <size>0x6c</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-b1">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0x16dc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x16dc</run_address>
         <size>0x68</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1db">
         <name>.text.__ledf2</name>
         <load_address>0x1744</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1744</run_address>
         <size>0x68</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-128"/>
      </object_component>
      <object_component id="oc-1cb">
         <name>.text.__aeabi_dcmp</name>
         <load_address>0x17ac</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x17ac</run_address>
         <size>0x62</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11e"/>
      </object_component>
      <object_component id="oc-1bc">
         <name>.text.__aeabi_idiv0</name>
         <load_address>0x180e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x180e</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-129"/>
      </object_component>
      <object_component id="oc-1c3">
         <name>.text.__aeabi_fcmp</name>
         <load_address>0x1810</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1810</run_address>
         <size>0x62</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11f"/>
      </object_component>
      <object_component id="oc-c2">
         <name>.text.tft180_show_string_color</name>
         <load_address>0x1872</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1872</run_address>
         <size>0x62</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-93">
         <name>.text.main</name>
         <load_address>0x18d4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x18d4</run_address>
         <size>0x58</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-198">
         <name>.text.__aeabi_idivmod</name>
         <load_address>0x192c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x192c</run_address>
         <size>0x56</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-120"/>
      </object_component>
      <object_component id="oc-73">
         <name>.text.timerA_callback</name>
         <load_address>0x1982</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1982</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-ef">
         <name>.text.SYSCFG_DL_UART_1_init</name>
         <load_address>0x1984</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1984</run_address>
         <size>0x54</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-10e">
         <name>.text.openmv_is_data_valid</name>
         <load_address>0x19d8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x19d8</run_address>
         <size>0x50</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-173">
         <name>.text.DL_UART_setBaudRateDivisor</name>
         <load_address>0x1a28</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a28</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-171">
         <name>.text.DL_UART_init</name>
         <load_address>0x1a74</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a74</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-ee">
         <name>.text.SYSCFG_DL_UART_0_init</name>
         <load_address>0x1abc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1abc</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-f0">
         <name>.text.SYSCFG_DL_UART_3_init</name>
         <load_address>0x1b04</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b04</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-182">
         <name>.text.DL_SPI_init</name>
         <load_address>0x1b4c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b4c</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-106">
         <name>.text.tft180_write_16bit_data</name>
         <load_address>0x1b90</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b90</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-f1">
         <name>.text.SYSCFG_DL_SPI_IMU660RB_init</name>
         <load_address>0x1bd4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1bd4</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-f2">
         <name>.text.SYSCFG_DL_TFT_SPI_init</name>
         <load_address>0x1c14</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c14</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-ec">
         <name>.text.SYSCFG_DL_TIMER_8_init</name>
         <load_address>0x1c54</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c54</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1c7">
         <name>.text.__extendsfdf2</name>
         <load_address>0x1c94</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c94</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-116"/>
      </object_component>
      <object_component id="oc-15b">
         <name>.text.DL_Timer_setCounterControl</name>
         <load_address>0x1cd4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1cd4</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-ed">
         <name>.text.SYSCFG_DL_TIMER_12_init</name>
         <load_address>0x1d10</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d10</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-10f">
         <name>.text.__floatsisf</name>
         <load_address>0x1d4c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d4c</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11a"/>
      </object_component>
      <object_component id="oc-1d8">
         <name>.text.__gtsf2</name>
         <load_address>0x1d88</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d88</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-125"/>
      </object_component>
      <object_component id="oc-ce">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0x1dc4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1dc4</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-47"/>
      </object_component>
      <object_component id="oc-1d3">
         <name>.text.__eqsf2</name>
         <load_address>0x1e00</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e00</run_address>
         <size>0x3a</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-125"/>
      </object_component>
      <object_component id="oc-85">
         <name>.text.timerB_callback</name>
         <load_address>0x1e3a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e3a</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-1b6">
         <name>.text.__muldsi3</name>
         <load_address>0x1e3c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e3c</run_address>
         <size>0x3a</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-112"/>
      </object_component>
      <object_component id="oc-194">
         <name>.text.__fixsfsi</name>
         <load_address>0x1e78</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e78</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-118"/>
      </object_component>
      <object_component id="oc-148">
         <name>.text.DL_GPIO_initDigitalInputFeatures</name>
         <load_address>0x1eb0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1eb0</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-184">
         <name>.text.DL_SPI_setFIFOThreshold</name>
         <load_address>0x1ee4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ee4</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-79">
         <name>.text.openmv_analysis</name>
         <load_address>0x1f14</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f14</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-fd">
         <name>.text.tft180_write_index</name>
         <load_address>0x1f44</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f44</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-109">
         <name>.text.__NVIC_ClearPendingIRQ</name>
         <load_address>0x1f74</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f74</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-10c">
         <name>.text.__NVIC_ClearPendingIRQ</name>
         <load_address>0x1fa0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1fa0</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-10a">
         <name>.text.__NVIC_EnableIRQ</name>
         <load_address>0x1fcc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1fcc</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-10d">
         <name>.text.__NVIC_EnableIRQ</name>
         <load_address>0x1ff8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ff8</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-fe">
         <name>.text.tft180_write_8bit_data</name>
         <load_address>0x2024</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2024</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-1af">
         <name>.text.DL_Common_updateReg</name>
         <load_address>0x2050</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2050</run_address>
         <size>0x28</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-18a">
         <name>.text.DL_SYSTICK_init</name>
         <load_address>0x2078</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2078</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-39">
         <name>.text.TIMG8_IRQHandler</name>
         <load_address>0x20a0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20a0</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-43">
         <name>.text.UART0_IRQHandler</name>
         <load_address>0x20c8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20c8</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-64">
         <name>.text:_c_int00_noargs</name>
         <load_address>0x20f0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20f0</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-183">
         <name>.text.DL_SPI_setBitRateSerialClockDivider</name>
         <load_address>0x2118</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2118</run_address>
         <size>0x24</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-e9">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0x213c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x213c</run_address>
         <size>0x22</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-147">
         <name>.text.DL_GPIO_initPeripheralInputFunction</name>
         <load_address>0x2160</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2160</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-172">
         <name>.text.DL_UART_setOversampling</name>
         <load_address>0x2180</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2180</run_address>
         <size>0x1e</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-9f">
         <name>.text.DL_GPIO_clearInterruptStatus</name>
         <load_address>0x21a0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x21a0</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-14c">
         <name>.text.DL_GPIO_clearInterruptStatus</name>
         <load_address>0x21bc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x21bc</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-14d">
         <name>.text.DL_GPIO_enableInterrupt</name>
         <load_address>0x21d8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x21d8</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-145">
         <name>.text.DL_GPIO_initPeripheralOutputFunction</name>
         <load_address>0x21f4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x21f4</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-14a">
         <name>.text.DL_GPIO_setLowerPinsPolarity</name>
         <load_address>0x2210</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2210</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-151">
         <name>.text.DL_SYSCTL_setSYSOSCFreq</name>
         <load_address>0x222c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x222c</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-166">
         <name>.text.DL_Timer_enableInterrupt</name>
         <load_address>0x2248</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2248</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-15d">
         <name>.text.DL_Timer_setCaptCompUpdateMethod</name>
         <load_address>0x2264</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2264</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-154">
         <name>.text.DL_Timer_setClockConfig</name>
         <load_address>0x2280</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2280</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-174">
         <name>.text.DL_UART_enableInterrupt</name>
         <load_address>0x229c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x229c</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-48">
         <name>.text.TIMG12_IRQHandler</name>
         <load_address>0x22b8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x22b8</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-146">
         <name>.text.DL_GPIO_enableOutput</name>
         <load_address>0x22d4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x22d4</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-13d">
         <name>.text.DL_GPIO_enablePower</name>
         <load_address>0x22ec</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x22ec</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-9e">
         <name>.text.DL_GPIO_getEnabledInterruptStatus</name>
         <load_address>0x2304</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2304</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-149">
         <name>.text.DL_GPIO_initDigitalOutput</name>
         <load_address>0x231c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x231c</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-139">
         <name>.text.DL_GPIO_reset</name>
         <load_address>0x2334</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2334</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-fc">
         <name>.text.DL_GPIO_setPins</name>
         <load_address>0x234c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x234c</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-14f">
         <name>.text.DL_GPIO_setPins</name>
         <load_address>0x2364</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2364</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-14b">
         <name>.text.DL_GPIO_setUpperPinsPolarity</name>
         <load_address>0x237c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x237c</run_address>
         <size>0x18</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-185">
         <name>.text.DL_SPI_enable</name>
         <load_address>0x2394</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2394</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-140">
         <name>.text.DL_SPI_enablePower</name>
         <load_address>0x23ac</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x23ac</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-18d">
         <name>.text.DL_SPI_isBusy</name>
         <load_address>0x23c4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x23c4</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-13c">
         <name>.text.DL_SPI_reset</name>
         <load_address>0x23dc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x23dc</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-150">
         <name>.text.DL_SYSCTL_setBORThreshold</name>
         <load_address>0x23f4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x23f4</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-13e">
         <name>.text.DL_Timer_enablePower</name>
         <load_address>0x240c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x240c</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-13a">
         <name>.text.DL_Timer_reset</name>
         <load_address>0x2424</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2424</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-15c">
         <name>.text.DL_Timer_setCaptureCompareOutCtl</name>
         <load_address>0x243c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x243c</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-13f">
         <name>.text.DL_UART_enablePower</name>
         <load_address>0x2454</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2454</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-13b">
         <name>.text.DL_UART_reset</name>
         <load_address>0x246c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x246c</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-a0">
         <name>.text.DL_GPIO_readPins</name>
         <load_address>0x2484</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2484</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-18c">
         <name>.text.DL_SPI_transmitData8</name>
         <load_address>0x249a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x249a</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-175">
         <name>.text.DL_UART_enable</name>
         <load_address>0x24b0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x24b0</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-bb">
         <name>.text.delay_ms</name>
         <load_address>0x24c6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x24c6</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-c1">
         <name>.text.openmv_init</name>
         <load_address>0x24dc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x24dc</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-c3">
         <name>.text.timerA_init</name>
         <load_address>0x24f2</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x24f2</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-c4">
         <name>.text.timerB_init</name>
         <load_address>0x2508</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2508</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-5b">
         <name>.text:decompress:ZI:__TI_zero_init_nomemset</name>
         <load_address>0x251e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x251e</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4a"/>
      </object_component>
      <object_component id="oc-fb">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x2534</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2534</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-14e">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x2548</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2548</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-152">
         <name>.text.DL_SYSCTL_enableMFCLK</name>
         <load_address>0x255c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x255c</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-15f">
         <name>.text.DL_Timer_enableClock</name>
         <load_address>0x2570</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2570</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-160">
         <name>.text.DL_Timer_setCCPDirection</name>
         <load_address>0x2584</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2584</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-81">
         <name>.text.DL_UART_receiveData</name>
         <load_address>0x2598</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2598</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-78">
         <name>.text.DL_UART_receiveData</name>
         <load_address>0x25ac</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x25ac</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-17c">
         <name>.text.DL_SPI_setClockConfig</name>
         <load_address>0x25c0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x25c0</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-72">
         <name>.text.DL_Timer_getPendingInterrupt</name>
         <load_address>0x25d2</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x25d2</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-80">
         <name>.text.DL_UART_getPendingInterrupt</name>
         <load_address>0x25e4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x25e4</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-77">
         <name>.text.DL_UART_getPendingInterrupt</name>
         <load_address>0x25f6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x25f6</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-16b">
         <name>.text.DL_UART_setClockConfig</name>
         <load_address>0x2608</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2608</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-a7">
         <name>.text:TI_memcpy_small</name>
         <load_address>0x261a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x261a</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12b"/>
      </object_component>
      <object_component id="oc-20">
         <name>.text:decompress:none</name>
         <load_address>0x262c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x262c</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4b"/>
      </object_component>
      <object_component id="oc-18b">
         <name>.text.DL_SYSTICK_enable</name>
         <load_address>0x2640</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2640</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-15e">
         <name>.text.DL_Timer_setCaptureCompareValue</name>
         <load_address>0x2650</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2650</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-f3">
         <name>.text.SYSCFG_DL_SYSTICK_init</name>
         <load_address>0x2660</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2660</run_address>
         <size>0x10</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-a5">
         <name>.text.get_system_time_ms</name>
         <load_address>0x2670</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2670</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-141">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0x267c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x267c</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-34">
         <name>.text.GROUP1_IRQHandler</name>
         <load_address>0x2686</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2686</run_address>
         <size>0x8</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-54">
         <name>.text.__aeabi_memcpy</name>
         <load_address>0x2690</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2690</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-121"/>
      </object_component>
      <object_component id="oc-c7">
         <name>.text:abort</name>
         <load_address>0x2698</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2698</run_address>
         <size>0x6</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-52"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x269e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x269e</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-128">
         <name>.text.HOSTexit</name>
         <load_address>0x26a2</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x26a2</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-ff"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0x26a6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x26a6</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.text._system_pre_init</name>
         <load_address>0x26aa</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x26aa</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-48"/>
      </object_component>
      <object_component id="oc-227">
         <name>.cinit..data.load</name>
         <load_address>0x2e70</load_address>
         <readonly>true</readonly>
         <run_address>0x2e70</run_address>
         <size>0xf</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-225">
         <name>__TI_handler_table</name>
         <load_address>0x2e80</load_address>
         <readonly>true</readonly>
         <run_address>0x2e80</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-228">
         <name>.cinit..bss.load</name>
         <load_address>0x2e8c</load_address>
         <readonly>true</readonly>
         <run_address>0x2e8c</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-226">
         <name>__TI_cinit_table</name>
         <load_address>0x2e94</load_address>
         <readonly>true</readonly>
         <run_address>0x2e94</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-18e">
         <name>.rodata.ascii_font_8x16</name>
         <load_address>0x26b0</load_address>
         <readonly>true</readonly>
         <run_address>0x26b0</run_address>
         <size>0x5f0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-16a">
         <name>.rodata.gTIMER_12TimerConfig</name>
         <load_address>0x2ca0</load_address>
         <readonly>true</readonly>
         <run_address>0x2ca0</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-168">
         <name>.rodata.gTIMER_8TimerConfig</name>
         <load_address>0x2cb4</load_address>
         <readonly>true</readonly>
         <run_address>0x2cb4</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-c6">
         <name>.rodata.str1.5792233746214848027.1</name>
         <load_address>0x2cc8</load_address>
         <readonly>true</readonly>
         <run_address>0x2cc8</run_address>
         <size>0x14</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-114">
         <name>.rodata.str1.14055760531511630791.1</name>
         <load_address>0x2cdc</load_address>
         <readonly>true</readonly>
         <run_address>0x2cdc</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-11c">
         <name>.rodata.str1.10222307361326560281.1</name>
         <load_address>0x2cef</load_address>
         <readonly>true</readonly>
         <run_address>0x2cef</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-127">
         <name>.rodata.str1.10322375466862398049.1</name>
         <load_address>0x2d01</load_address>
         <readonly>true</readonly>
         <run_address>0x2d01</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-11b">
         <name>.rodata.str1.10499335994202400488.1</name>
         <load_address>0x2d13</load_address>
         <readonly>true</readonly>
         <run_address>0x2d13</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-118">
         <name>.rodata.str1.11972700756869316087.1</name>
         <load_address>0x2d25</load_address>
         <readonly>true</readonly>
         <run_address>0x2d25</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-11d">
         <name>.rodata.str1.12960560650680968270.1</name>
         <load_address>0x2d37</load_address>
         <readonly>true</readonly>
         <run_address>0x2d37</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-126">
         <name>.rodata.str1.12983843792890534433.1</name>
         <load_address>0x2d49</load_address>
         <readonly>true</readonly>
         <run_address>0x2d49</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-124">
         <name>.rodata.str1.15289475315984735280.1</name>
         <load_address>0x2d5b</load_address>
         <readonly>true</readonly>
         <run_address>0x2d5b</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-123">
         <name>.rodata.str1.16410698957387474858.1</name>
         <load_address>0x2d6d</load_address>
         <readonly>true</readonly>
         <run_address>0x2d6d</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-117">
         <name>.rodata.str1.16964012482489148156.1</name>
         <load_address>0x2d7f</load_address>
         <readonly>true</readonly>
         <run_address>0x2d7f</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-125">
         <name>.rodata.str1.4018680016288177859.1</name>
         <load_address>0x2d91</load_address>
         <readonly>true</readonly>
         <run_address>0x2d91</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-115">
         <name>.rodata.str1.5388014517103437970.1</name>
         <load_address>0x2da3</load_address>
         <readonly>true</readonly>
         <run_address>0x2da3</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-122">
         <name>.rodata.str1.5573925365973559781.1</name>
         <load_address>0x2db5</load_address>
         <readonly>true</readonly>
         <run_address>0x2db5</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-116">
         <name>.rodata.str1.5732439976852354875.1</name>
         <load_address>0x2dc7</load_address>
         <readonly>true</readonly>
         <run_address>0x2dc7</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-119">
         <name>.rodata.str1.8871796710599046883.1</name>
         <load_address>0x2dd9</load_address>
         <readonly>true</readonly>
         <run_address>0x2dd9</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-11a">
         <name>.rodata.str1.9558640640855864504.1</name>
         <load_address>0x2deb</load_address>
         <readonly>true</readonly>
         <run_address>0x2deb</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-187">
         <name>.rodata.gSPI_IMU660RB_config</name>
         <load_address>0x2dfe</load_address>
         <readonly>true</readonly>
         <run_address>0x2dfe</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-189">
         <name>.rodata.gTFT_SPI_config</name>
         <load_address>0x2e08</load_address>
         <readonly>true</readonly>
         <run_address>0x2e08</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-177">
         <name>.rodata.gUART_0Config</name>
         <load_address>0x2e12</load_address>
         <readonly>true</readonly>
         <run_address>0x2e12</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-179">
         <name>.rodata.gUART_1Config</name>
         <load_address>0x2e1c</load_address>
         <readonly>true</readonly>
         <run_address>0x2e1c</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-17b">
         <name>.rodata.gUART_3Config</name>
         <load_address>0x2e26</load_address>
         <readonly>true</readonly>
         <run_address>0x2e26</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-11e">
         <name>.rodata.str1.16395811435273266920.1</name>
         <load_address>0x2e30</load_address>
         <readonly>true</readonly>
         <run_address>0x2e30</run_address>
         <size>0xa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-121">
         <name>.rodata.str1.9416414711272993270.1</name>
         <load_address>0x2e3a</load_address>
         <readonly>true</readonly>
         <run_address>0x2e3a</run_address>
         <size>0xa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-162">
         <name>.rodata.gPWM_6Config</name>
         <load_address>0x2e44</load_address>
         <readonly>true</readonly>
         <run_address>0x2e44</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-164">
         <name>.rodata.gPWM_7Config</name>
         <load_address>0x2e4c</load_address>
         <readonly>true</readonly>
         <run_address>0x2e4c</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-161">
         <name>.rodata.gPWM_6ClockConfig</name>
         <load_address>0x2e54</load_address>
         <readonly>true</readonly>
         <run_address>0x2e54</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-163">
         <name>.rodata.gPWM_7ClockConfig</name>
         <load_address>0x2e57</load_address>
         <readonly>true</readonly>
         <run_address>0x2e57</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-169">
         <name>.rodata.gTIMER_12ClockConfig</name>
         <load_address>0x2e5a</load_address>
         <readonly>true</readonly>
         <run_address>0x2e5a</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-167">
         <name>.rodata.gTIMER_8ClockConfig</name>
         <load_address>0x2e5d</load_address>
         <readonly>true</readonly>
         <run_address>0x2e5d</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-186">
         <name>.rodata.gSPI_IMU660RB_clockConfig</name>
         <load_address>0x2e60</load_address>
         <readonly>true</readonly>
         <run_address>0x2e60</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-188">
         <name>.rodata.gTFT_SPI_clockConfig</name>
         <load_address>0x2e62</load_address>
         <readonly>true</readonly>
         <run_address>0x2e62</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-176">
         <name>.rodata.gUART_0ClockConfig</name>
         <load_address>0x2e64</load_address>
         <readonly>true</readonly>
         <run_address>0x2e64</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-178">
         <name>.rodata.gUART_1ClockConfig</name>
         <load_address>0x2e66</load_address>
         <readonly>true</readonly>
         <run_address>0x2e66</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-17a">
         <name>.rodata.gUART_3ClockConfig</name>
         <load_address>0x2e68</load_address>
         <readonly>true</readonly>
         <run_address>0x2e68</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1ef">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-a2">
         <name>.data.left_counter</name>
         <load_address>0x202001d8</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001d8</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-a1">
         <name>.data.right_counter</name>
         <load_address>0x202001da</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001da</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-7b">
         <name>.data.state</name>
         <load_address>0x202001e3</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001e3</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-7c">
         <name>.data.n</name>
         <load_address>0x202001e2</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001e2</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-11f">
         <name>.data.tft180_bgcolor</name>
         <load_address>0x202001dc</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001dc</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-120">
         <name>.data.tft180_pencolor</name>
         <load_address>0x202001e0</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001e0</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-107">
         <name>.data.tft180_x_max</name>
         <load_address>0x202001e4</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001e4</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-108">
         <name>.data.tft180_y_max</name>
         <load_address>0x202001e5</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001e5</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-ff">
         <name>.data.tft180_bgcolor</name>
         <load_address>0x202001de</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001de</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-74">
         <name>.data.system_time_ms</name>
         <load_address>0x202001d4</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001d4</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-82">
         <name>.data.uart_data</name>
         <load_address>0x202001e6</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202001e6</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-7d">
         <name>.bss.rx_buffer</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202001c0</run_address>
         <size>0x8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-7a">
         <name>.bss.data</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202001d0</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-f4">
         <name>.common:gPWM_6Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0xa0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-f5">
         <name>.common:gPWM_7Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202000a0</run_address>
         <size>0xa0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-f6">
         <name>.common:gUART_3Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200140</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-f7">
         <name>.common:gSPI_IMU660RBBackup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200170</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-f8">
         <name>.common:gTFT_SPIBackup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200198</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-a6">
         <name>.common:openmvData</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202001c8</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20203e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-22a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20203e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-70">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xfa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-f9">
         <name>.debug_abbrev</name>
         <load_address>0xfa</load_address>
         <run_address>0xfa</run_address>
         <size>0x210</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_abbrev</name>
         <load_address>0x30a</load_address>
         <run_address>0x30a</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-a3">
         <name>.debug_abbrev</name>
         <load_address>0x377</load_address>
         <run_address>0x377</run_address>
         <size>0x185</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-7e">
         <name>.debug_abbrev</name>
         <load_address>0x4fc</load_address>
         <run_address>0x4fc</run_address>
         <size>0x205</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-100">
         <name>.debug_abbrev</name>
         <load_address>0x701</load_address>
         <run_address>0x701</run_address>
         <size>0x19d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-75">
         <name>.debug_abbrev</name>
         <load_address>0x89e</load_address>
         <run_address>0x89e</run_address>
         <size>0x18e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-83">
         <name>.debug_abbrev</name>
         <load_address>0xa2c</load_address>
         <run_address>0xa2c</run_address>
         <size>0x1c4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-103">
         <name>.debug_abbrev</name>
         <load_address>0xbf0</load_address>
         <run_address>0xbf0</run_address>
         <size>0xac</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-1ac">
         <name>.debug_abbrev</name>
         <load_address>0xc9c</load_address>
         <run_address>0xc9c</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-1b4">
         <name>.debug_abbrev</name>
         <load_address>0xcfe</load_address>
         <run_address>0xcfe</run_address>
         <size>0x277</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-1b0">
         <name>.debug_abbrev</name>
         <load_address>0xf75</load_address>
         <run_address>0xf75</run_address>
         <size>0x286</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-1b2">
         <name>.debug_abbrev</name>
         <load_address>0x11fb</load_address>
         <run_address>0x11fb</run_address>
         <size>0x29b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.debug_abbrev</name>
         <load_address>0x1496</load_address>
         <run_address>0x1496</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-132">
         <name>.debug_abbrev</name>
         <load_address>0x1545</load_address>
         <run_address>0x1545</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-47"/>
      </object_component>
      <object_component id="oc-af">
         <name>.debug_abbrev</name>
         <load_address>0x16b5</load_address>
         <run_address>0x16b5</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-48"/>
      </object_component>
      <object_component id="oc-88">
         <name>.debug_abbrev</name>
         <load_address>0x16ee</load_address>
         <run_address>0x16ee</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4a"/>
      </object_component>
      <object_component id="oc-58">
         <name>.debug_abbrev</name>
         <load_address>0x17b0</load_address>
         <run_address>0x17b0</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4b"/>
      </object_component>
      <object_component id="oc-50">
         <name>.debug_abbrev</name>
         <load_address>0x1820</load_address>
         <run_address>0x1820</run_address>
         <size>0x8d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-12c">
         <name>.debug_abbrev</name>
         <load_address>0x18ad</load_address>
         <run_address>0x18ad</run_address>
         <size>0x98</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-52"/>
      </object_component>
      <object_component id="oc-1a1">
         <name>.debug_abbrev</name>
         <load_address>0x1945</load_address>
         <run_address>0x1945</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ff"/>
      </object_component>
      <object_component id="oc-1c2">
         <name>.debug_abbrev</name>
         <load_address>0x1971</load_address>
         <run_address>0x1971</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10f"/>
      </object_component>
      <object_component id="oc-1cf">
         <name>.debug_abbrev</name>
         <load_address>0x1998</load_address>
         <run_address>0x1998</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-112"/>
      </object_component>
      <object_component id="oc-1ba">
         <name>.debug_abbrev</name>
         <load_address>0x19bf</load_address>
         <run_address>0x19bf</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-113"/>
      </object_component>
      <object_component id="oc-1da">
         <name>.debug_abbrev</name>
         <load_address>0x19e6</load_address>
         <run_address>0x19e6</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-116"/>
      </object_component>
      <object_component id="oc-1bb">
         <name>.debug_abbrev</name>
         <load_address>0x1a0d</load_address>
         <run_address>0x1a0d</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-118"/>
      </object_component>
      <object_component id="oc-18f">
         <name>.debug_abbrev</name>
         <load_address>0x1a34</load_address>
         <run_address>0x1a34</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11a"/>
      </object_component>
      <object_component id="oc-1e2">
         <name>.debug_abbrev</name>
         <load_address>0x1a5b</load_address>
         <run_address>0x1a5b</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11e"/>
      </object_component>
      <object_component id="oc-1d9">
         <name>.debug_abbrev</name>
         <load_address>0x1a82</load_address>
         <run_address>0x1a82</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11f"/>
      </object_component>
      <object_component id="oc-1c1">
         <name>.debug_abbrev</name>
         <load_address>0x1aa9</load_address>
         <run_address>0x1aa9</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-120"/>
      </object_component>
      <object_component id="oc-87">
         <name>.debug_abbrev</name>
         <load_address>0x1ad0</load_address>
         <run_address>0x1ad0</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-121"/>
      </object_component>
      <object_component id="oc-1e3">
         <name>.debug_abbrev</name>
         <load_address>0x1af7</load_address>
         <run_address>0x1af7</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-125"/>
      </object_component>
      <object_component id="oc-1e4">
         <name>.debug_abbrev</name>
         <load_address>0x1b1c</load_address>
         <run_address>0x1b1c</run_address>
         <size>0xc8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-128"/>
      </object_component>
      <object_component id="oc-1d0">
         <name>.debug_abbrev</name>
         <load_address>0x1be4</load_address>
         <run_address>0x1be4</run_address>
         <size>0x59</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-129"/>
      </object_component>
      <object_component id="oc-e0">
         <name>.debug_abbrev</name>
         <load_address>0x1c3d</load_address>
         <run_address>0x1c3d</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12b"/>
      </object_component>
      <object_component id="oc-22c">
         <name>.debug_abbrev</name>
         <load_address>0x1c62</load_address>
         <run_address>0x1c62</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x5ad</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b5">
         <name>.debug_info</name>
         <load_address>0x5ad</load_address>
         <run_address>0x5ad</run_address>
         <size>0x3acb</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x4078</load_address>
         <run_address>0x4078</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-6d">
         <name>.debug_info</name>
         <load_address>0x40f8</load_address>
         <run_address>0x40f8</run_address>
         <size>0xb04</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_info</name>
         <load_address>0x4bfc</load_address>
         <run_address>0x4bfc</run_address>
         <size>0xdc9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-ba">
         <name>.debug_info</name>
         <load_address>0x59c5</load_address>
         <run_address>0x59c5</run_address>
         <size>0x14df</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_info</name>
         <load_address>0x6ea4</load_address>
         <run_address>0x6ea4</run_address>
         <size>0xa3c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-44">
         <name>.debug_info</name>
         <load_address>0x78e0</load_address>
         <run_address>0x78e0</run_address>
         <size>0xa81</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-be">
         <name>.debug_info</name>
         <load_address>0x8361</load_address>
         <run_address>0x8361</run_address>
         <size>0x121</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-142">
         <name>.debug_info</name>
         <load_address>0x8482</load_address>
         <run_address>0x8482</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-181">
         <name>.debug_info</name>
         <load_address>0x84f7</load_address>
         <run_address>0x84f7</run_address>
         <size>0x1142</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-155">
         <name>.debug_info</name>
         <load_address>0x9639</load_address>
         <run_address>0x9639</run_address>
         <size>0x3172</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-16e">
         <name>.debug_info</name>
         <load_address>0xc7ab</load_address>
         <run_address>0xc7ab</run_address>
         <size>0x12a6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0xda51</load_address>
         <run_address>0xda51</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-cf">
         <name>.debug_info</name>
         <load_address>0xde74</load_address>
         <run_address>0xde74</run_address>
         <size>0x744</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-47"/>
      </object_component>
      <object_component id="oc-90">
         <name>.debug_info</name>
         <load_address>0xe5b8</load_address>
         <run_address>0xe5b8</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-48"/>
      </object_component>
      <object_component id="oc-5e">
         <name>.debug_info</name>
         <load_address>0xe5fe</load_address>
         <run_address>0xe5fe</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4a"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_info</name>
         <load_address>0xe790</load_address>
         <run_address>0xe790</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4b"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_info</name>
         <load_address>0xe856</load_address>
         <run_address>0xe856</run_address>
         <size>0x17c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-c8">
         <name>.debug_info</name>
         <load_address>0xe9d2</load_address>
         <run_address>0xe9d2</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-52"/>
      </object_component>
      <object_component id="oc-12a">
         <name>.debug_info</name>
         <load_address>0xeaca</load_address>
         <run_address>0xeaca</run_address>
         <size>0x3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ff"/>
      </object_component>
      <object_component id="oc-19d">
         <name>.debug_info</name>
         <load_address>0xeb05</load_address>
         <run_address>0xeb05</run_address>
         <size>0x1a7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10f"/>
      </object_component>
      <object_component id="oc-1b9">
         <name>.debug_info</name>
         <load_address>0xecac</load_address>
         <run_address>0xecac</run_address>
         <size>0x18f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-112"/>
      </object_component>
      <object_component id="oc-192">
         <name>.debug_info</name>
         <load_address>0xee3b</load_address>
         <run_address>0xee3b</run_address>
         <size>0x18d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-113"/>
      </object_component>
      <object_component id="oc-1c8">
         <name>.debug_info</name>
         <load_address>0xefc8</load_address>
         <run_address>0xefc8</run_address>
         <size>0x197</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-116"/>
      </object_component>
      <object_component id="oc-197">
         <name>.debug_info</name>
         <load_address>0xf15f</load_address>
         <run_address>0xf15f</run_address>
         <size>0x18f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-118"/>
      </object_component>
      <object_component id="oc-111">
         <name>.debug_info</name>
         <load_address>0xf2ee</load_address>
         <run_address>0xf2ee</run_address>
         <size>0x193</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11a"/>
      </object_component>
      <object_component id="oc-1cc">
         <name>.debug_info</name>
         <load_address>0xf481</load_address>
         <run_address>0xf481</run_address>
         <size>0x217</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11e"/>
      </object_component>
      <object_component id="oc-1c4">
         <name>.debug_info</name>
         <load_address>0xf698</load_address>
         <run_address>0xf698</run_address>
         <size>0x217</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11f"/>
      </object_component>
      <object_component id="oc-19a">
         <name>.debug_info</name>
         <load_address>0xf8af</load_address>
         <run_address>0xf8af</run_address>
         <size>0x1b9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-120"/>
      </object_component>
      <object_component id="oc-55">
         <name>.debug_info</name>
         <load_address>0xfa68</load_address>
         <run_address>0xfa68</run_address>
         <size>0x199</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-121"/>
      </object_component>
      <object_component id="oc-1d4">
         <name>.debug_info</name>
         <load_address>0xfc01</load_address>
         <run_address>0xfc01</run_address>
         <size>0x1c1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-125"/>
      </object_component>
      <object_component id="oc-1df">
         <name>.debug_info</name>
         <load_address>0xfdc2</load_address>
         <run_address>0xfdc2</run_address>
         <size>0x2f9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-128"/>
      </object_component>
      <object_component id="oc-1c0">
         <name>.debug_info</name>
         <load_address>0x100bb</load_address>
         <run_address>0x100bb</run_address>
         <size>0x85</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-129"/>
      </object_component>
      <object_component id="oc-aa">
         <name>.debug_info</name>
         <load_address>0x10140</load_address>
         <run_address>0x10140</run_address>
         <size>0x2fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12b"/>
      </object_component>
      <object_component id="oc-22b">
         <name>.debug_info</name>
         <load_address>0x1043a</load_address>
         <run_address>0x1043a</run_address>
         <size>0xbc</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b4">
         <name>.debug_ranges</name>
         <load_address>0x50</load_address>
         <run_address>0x50</run_address>
         <size>0x1b0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_ranges</name>
         <load_address>0x200</load_address>
         <run_address>0x200</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-6f">
         <name>.debug_ranges</name>
         <load_address>0x218</load_address>
         <run_address>0x218</run_address>
         <size>0x40</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_ranges</name>
         <load_address>0x258</load_address>
         <run_address>0x258</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-b9">
         <name>.debug_ranges</name>
         <load_address>0x2d8</load_address>
         <run_address>0x2d8</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_ranges</name>
         <load_address>0x358</load_address>
         <run_address>0x358</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-46">
         <name>.debug_ranges</name>
         <load_address>0x3a0</load_address>
         <run_address>0x3a0</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-bd">
         <name>.debug_ranges</name>
         <load_address>0x410</load_address>
         <run_address>0x410</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-180">
         <name>.debug_ranges</name>
         <load_address>0x428</load_address>
         <run_address>0x428</run_address>
         <size>0x390</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-156">
         <name>.debug_ranges</name>
         <load_address>0x7b8</load_address>
         <run_address>0x7b8</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-16c">
         <name>.debug_ranges</name>
         <load_address>0x990</load_address>
         <run_address>0x990</run_address>
         <size>0x1a8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_ranges</name>
         <load_address>0xb38</load_address>
         <run_address>0xb38</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-d0">
         <name>.debug_ranges</name>
         <load_address>0xb80</load_address>
         <run_address>0xb80</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-47"/>
      </object_component>
      <object_component id="oc-5c">
         <name>.debug_ranges</name>
         <load_address>0xbc8</load_address>
         <run_address>0xbc8</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4a"/>
      </object_component>
      <object_component id="oc-53">
         <name>.debug_ranges</name>
         <load_address>0xbe0</load_address>
         <run_address>0xbe0</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-cb">
         <name>.debug_ranges</name>
         <load_address>0xc30</load_address>
         <run_address>0xc30</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-52"/>
      </object_component>
      <object_component id="oc-1d7">
         <name>.debug_ranges</name>
         <load_address>0xc48</load_address>
         <run_address>0xc48</run_address>
         <size>0x38</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-125"/>
      </object_component>
      <object_component id="oc-1de">
         <name>.debug_ranges</name>
         <load_address>0xc80</load_address>
         <run_address>0xc80</run_address>
         <size>0x38</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-128"/>
      </object_component>
      <object_component id="oc-1bd">
         <name>.debug_ranges</name>
         <load_address>0xcb8</load_address>
         <run_address>0xcb8</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-129"/>
      </object_component>
      <object_component id="oc-a8">
         <name>.debug_ranges</name>
         <load_address>0xcd0</load_address>
         <run_address>0xcd0</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12b"/>
      </object_component>
      <object_component id="oc-71">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x368</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-fa">
         <name>.debug_str</name>
         <load_address>0x368</load_address>
         <run_address>0x368</run_address>
         <size>0x3086</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_str</name>
         <load_address>0x33ee</load_address>
         <run_address>0x33ee</run_address>
         <size>0x173</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-a4">
         <name>.debug_str</name>
         <load_address>0x3561</load_address>
         <run_address>0x3561</run_address>
         <size>0x7de</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-7f">
         <name>.debug_str</name>
         <load_address>0x3d3f</load_address>
         <run_address>0x3d3f</run_address>
         <size>0xa30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-101">
         <name>.debug_str</name>
         <load_address>0x476f</load_address>
         <run_address>0x476f</run_address>
         <size>0x770</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-76">
         <name>.debug_str</name>
         <load_address>0x4edf</load_address>
         <run_address>0x4edf</run_address>
         <size>0x89e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-84">
         <name>.debug_str</name>
         <load_address>0x577d</load_address>
         <run_address>0x577d</run_address>
         <size>0x8af</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-104">
         <name>.debug_str</name>
         <load_address>0x602c</load_address>
         <run_address>0x602c</run_address>
         <size>0x140</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-1ad">
         <name>.debug_str</name>
         <load_address>0x616c</load_address>
         <run_address>0x616c</run_address>
         <size>0x178</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-1b5">
         <name>.debug_str</name>
         <load_address>0x62e4</load_address>
         <run_address>0x62e4</run_address>
         <size>0xc46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-1b1">
         <name>.debug_str</name>
         <load_address>0x6f2a</load_address>
         <run_address>0x6f2a</run_address>
         <size>0x1dd7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-1b3">
         <name>.debug_str</name>
         <load_address>0x8d01</load_address>
         <run_address>0x8d01</run_address>
         <size>0xcee</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-4c">
         <name>.debug_str</name>
         <load_address>0x99ef</load_address>
         <run_address>0x99ef</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-133">
         <name>.debug_str</name>
         <load_address>0x9c14</load_address>
         <run_address>0x9c14</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-47"/>
      </object_component>
      <object_component id="oc-b0">
         <name>.debug_str</name>
         <load_address>0x9f43</load_address>
         <run_address>0x9f43</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-48"/>
      </object_component>
      <object_component id="oc-89">
         <name>.debug_str</name>
         <load_address>0xa038</load_address>
         <run_address>0xa038</run_address>
         <size>0x19b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4a"/>
      </object_component>
      <object_component id="oc-59">
         <name>.debug_str</name>
         <load_address>0xa1d3</load_address>
         <run_address>0xa1d3</run_address>
         <size>0x168</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4b"/>
      </object_component>
      <object_component id="oc-51">
         <name>.debug_str</name>
         <load_address>0xa33b</load_address>
         <run_address>0xa33b</run_address>
         <size>0x1d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-12d">
         <name>.debug_str</name>
         <load_address>0xa510</load_address>
         <run_address>0xa510</run_address>
         <size>0x148</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-52"/>
      </object_component>
      <object_component id="oc-1a2">
         <name>.debug_str</name>
         <load_address>0xa658</load_address>
         <run_address>0xa658</run_address>
         <size>0xe9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ff"/>
      </object_component>
      <object_component id="oc-1e5">
         <name>.debug_str</name>
         <load_address>0xa741</load_address>
         <run_address>0xa741</run_address>
         <size>0x276</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-128"/>
      </object_component>
      <object_component id="oc-1d1">
         <name>.debug_str</name>
         <load_address>0xa9b7</load_address>
         <run_address>0xa9b7</run_address>
         <size>0x193</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-129"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xf4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b2">
         <name>.debug_frame</name>
         <load_address>0xf4</load_address>
         <run_address>0xf4</run_address>
         <size>0x4e8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_frame</name>
         <load_address>0x5dc</load_address>
         <run_address>0x5dc</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-6e">
         <name>.debug_frame</name>
         <load_address>0x60c</load_address>
         <run_address>0x60c</run_address>
         <size>0xa8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.debug_frame</name>
         <load_address>0x6b4</load_address>
         <run_address>0x6b4</run_address>
         <size>0x16c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-b7">
         <name>.debug_frame</name>
         <load_address>0x820</load_address>
         <run_address>0x820</run_address>
         <size>0x198</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_frame</name>
         <load_address>0x9b8</load_address>
         <run_address>0x9b8</run_address>
         <size>0xc4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-45">
         <name>.debug_frame</name>
         <load_address>0xa7c</load_address>
         <run_address>0xa7c</run_address>
         <size>0x140</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-bc">
         <name>.debug_frame</name>
         <load_address>0xbbc</load_address>
         <run_address>0xbbc</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-143">
         <name>.debug_frame</name>
         <load_address>0xbfc</load_address>
         <run_address>0xbfc</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-17d">
         <name>.debug_frame</name>
         <load_address>0xc1c</load_address>
         <run_address>0xc1c</run_address>
         <size>0x234</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-157">
         <name>.debug_frame</name>
         <load_address>0xe50</load_address>
         <run_address>0xe50</run_address>
         <size>0x408</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-16f">
         <name>.debug_frame</name>
         <load_address>0x1258</load_address>
         <run_address>0x1258</run_address>
         <size>0x1b8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-63">
         <name>.debug_frame</name>
         <load_address>0x1410</load_address>
         <run_address>0x1410</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-d2">
         <name>.debug_frame</name>
         <load_address>0x14a0</load_address>
         <run_address>0x14a0</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-47"/>
      </object_component>
      <object_component id="oc-91">
         <name>.debug_frame</name>
         <load_address>0x15a0</load_address>
         <run_address>0x15a0</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-48"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.debug_frame</name>
         <load_address>0x15c0</load_address>
         <run_address>0x15c0</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4a"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_frame</name>
         <load_address>0x15f8</load_address>
         <run_address>0x15f8</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4b"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_frame</name>
         <load_address>0x1620</load_address>
         <run_address>0x1620</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-c9">
         <name>.debug_frame</name>
         <load_address>0x1650</load_address>
         <run_address>0x1650</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-52"/>
      </object_component>
      <object_component id="oc-129">
         <name>.debug_frame</name>
         <load_address>0x1680</load_address>
         <run_address>0x1680</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-ff"/>
      </object_component>
      <object_component id="oc-1e0">
         <name>.debug_frame</name>
         <load_address>0x16a0</load_address>
         <run_address>0x16a0</run_address>
         <size>0x6c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-128"/>
      </object_component>
      <object_component id="oc-1bf">
         <name>.debug_frame</name>
         <load_address>0x170c</load_address>
         <run_address>0x170c</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-129"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x304</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b3">
         <name>.debug_line</name>
         <load_address>0x304</load_address>
         <run_address>0x304</run_address>
         <size>0xd76</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_line</name>
         <load_address>0x107a</load_address>
         <run_address>0x107a</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-6c">
         <name>.debug_line</name>
         <load_address>0x1132</load_address>
         <run_address>0x1132</run_address>
         <size>0x48b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_line</name>
         <load_address>0x15bd</load_address>
         <run_address>0x15bd</run_address>
         <size>0x6f3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-b8">
         <name>.debug_line</name>
         <load_address>0x1cb0</load_address>
         <run_address>0x1cb0</run_address>
         <size>0xad6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-c"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_line</name>
         <load_address>0x2786</load_address>
         <run_address>0x2786</run_address>
         <size>0x338</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_line</name>
         <load_address>0x2abe</load_address>
         <run_address>0x2abe</run_address>
         <size>0x493</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-bf">
         <name>.debug_line</name>
         <load_address>0x2f51</load_address>
         <run_address>0x2f51</run_address>
         <size>0x1f1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-144">
         <name>.debug_line</name>
         <load_address>0x3142</load_address>
         <run_address>0x3142</run_address>
         <size>0x179</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-17f">
         <name>.debug_line</name>
         <load_address>0x32bb</load_address>
         <run_address>0x32bb</run_address>
         <size>0xc1b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-159">
         <name>.debug_line</name>
         <load_address>0x3ed6</load_address>
         <run_address>0x3ed6</run_address>
         <size>0x176f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-16d">
         <name>.debug_line</name>
         <load_address>0x5645</load_address>
         <run_address>0x5645</run_address>
         <size>0xa18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-4d">
         <name>.debug_line</name>
         <load_address>0x605d</load_address>
         <run_address>0x605d</run_address>
         <size>0x1dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-d3">
         <name>.debug_line</name>
         <load_address>0x6239</load_address>
         <run_address>0x6239</run_address>
         <size>0x51a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-47"/>
      </object_component>
      <object_component id="oc-92">
         <name>.debug_line</name>
         <load_address>0x6753</load_address>
         <run_address>0x6753</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-48"/>
      </object_component>
      <object_component id="oc-60">
         <name>.debug_line</name>
         <load_address>0x6791</load_address>
         <run_address>0x6791</run_address>
         <size>0xfe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4a"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_line</name>
         <load_address>0x688f</load_address>
         <run_address>0x688f</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4b"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_line</name>
         <load_address>0x694f</load_address>
         <run_address>0x694f</run_address>
         <size>0x1c8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-ca">
         <name>.debug_line</name>
         <load_address>0x6b17</load_address>
         <run_address>0x6b17</run_address>
         <size>0x67</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-52"/>
      </object_component>
      <object_component id="oc-12b">
         <name>.debug_line</name>
         <load_address>0x6b7e</load_address>
         <run_address>0x6b7e</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ff"/>
      </object_component>
      <object_component id="oc-19f">
         <name>.debug_line</name>
         <load_address>0x6bbf</load_address>
         <run_address>0x6bbf</run_address>
         <size>0x107</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10f"/>
      </object_component>
      <object_component id="oc-1b8">
         <name>.debug_line</name>
         <load_address>0x6cc6</load_address>
         <run_address>0x6cc6</run_address>
         <size>0xb9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-112"/>
      </object_component>
      <object_component id="oc-191">
         <name>.debug_line</name>
         <load_address>0x6d7f</load_address>
         <run_address>0x6d7f</run_address>
         <size>0xe0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-113"/>
      </object_component>
      <object_component id="oc-1ca">
         <name>.debug_line</name>
         <load_address>0x6e5f</load_address>
         <run_address>0x6e5f</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-116"/>
      </object_component>
      <object_component id="oc-196">
         <name>.debug_line</name>
         <load_address>0x6f1f</load_address>
         <run_address>0x6f1f</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-118"/>
      </object_component>
      <object_component id="oc-112">
         <name>.debug_line</name>
         <load_address>0x6fd7</load_address>
         <run_address>0x6fd7</run_address>
         <size>0xbc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11a"/>
      </object_component>
      <object_component id="oc-1ce">
         <name>.debug_line</name>
         <load_address>0x7093</load_address>
         <run_address>0x7093</run_address>
         <size>0xc7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11e"/>
      </object_component>
      <object_component id="oc-1c6">
         <name>.debug_line</name>
         <load_address>0x715a</load_address>
         <run_address>0x715a</run_address>
         <size>0xc7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11f"/>
      </object_component>
      <object_component id="oc-19b">
         <name>.debug_line</name>
         <load_address>0x7221</load_address>
         <run_address>0x7221</run_address>
         <size>0xcc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-120"/>
      </object_component>
      <object_component id="oc-57">
         <name>.debug_line</name>
         <load_address>0x72ed</load_address>
         <run_address>0x72ed</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-121"/>
      </object_component>
      <object_component id="oc-1d6">
         <name>.debug_line</name>
         <load_address>0x7391</load_address>
         <run_address>0x7391</run_address>
         <size>0x104</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-125"/>
      </object_component>
      <object_component id="oc-1dc">
         <name>.debug_line</name>
         <load_address>0x7495</load_address>
         <run_address>0x7495</run_address>
         <size>0x2ef</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-128"/>
      </object_component>
      <object_component id="oc-1be">
         <name>.debug_line</name>
         <load_address>0x7784</load_address>
         <run_address>0x7784</run_address>
         <size>0xb5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-129"/>
      </object_component>
      <object_component id="oc-ab">
         <name>.debug_line</name>
         <load_address>0x7839</load_address>
         <run_address>0x7839</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12b"/>
      </object_component>
      <object_component id="oc-1ae">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-17e">
         <name>.debug_loc</name>
         <load_address>0x13</load_address>
         <run_address>0x13</run_address>
         <size>0x816</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-158">
         <name>.debug_loc</name>
         <load_address>0x829</load_address>
         <run_address>0x829</run_address>
         <size>0x1a27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-170">
         <name>.debug_loc</name>
         <load_address>0x2250</load_address>
         <run_address>0x2250</run_address>
         <size>0x7bc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.debug_loc</name>
         <load_address>0x2a0c</load_address>
         <run_address>0x2a0c</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-d1">
         <name>.debug_loc</name>
         <load_address>0x2ae4</load_address>
         <run_address>0x2ae4</run_address>
         <size>0x424</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-47"/>
      </object_component>
      <object_component id="oc-5d">
         <name>.debug_loc</name>
         <load_address>0x2f08</load_address>
         <run_address>0x2f08</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4a"/>
      </object_component>
      <object_component id="oc-5a">
         <name>.debug_loc</name>
         <load_address>0x3074</load_address>
         <run_address>0x3074</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4b"/>
      </object_component>
      <object_component id="oc-52">
         <name>.debug_loc</name>
         <load_address>0x30e3</load_address>
         <run_address>0x30e3</run_address>
         <size>0x167</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.debug_loc</name>
         <load_address>0x324a</load_address>
         <run_address>0x324a</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-52"/>
      </object_component>
      <object_component id="oc-1dd">
         <name>.debug_loc</name>
         <load_address>0x3270</load_address>
         <run_address>0x3270</run_address>
         <size>0x363</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-128"/>
      </object_component>
      <object_component id="oc-1d2">
         <name>.debug_loc</name>
         <load_address>0x35d3</load_address>
         <run_address>0x35d3</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-129"/>
      </object_component>
      <object_component id="oc-19e">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10f"/>
      </object_component>
      <object_component id="oc-1b7">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-112"/>
      </object_component>
      <object_component id="oc-193">
         <name>.debug_aranges</name>
         <load_address>0x40</load_address>
         <run_address>0x40</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-113"/>
      </object_component>
      <object_component id="oc-1c9">
         <name>.debug_aranges</name>
         <load_address>0x60</load_address>
         <run_address>0x60</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-116"/>
      </object_component>
      <object_component id="oc-195">
         <name>.debug_aranges</name>
         <load_address>0x80</load_address>
         <run_address>0x80</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-118"/>
      </object_component>
      <object_component id="oc-110">
         <name>.debug_aranges</name>
         <load_address>0xa0</load_address>
         <run_address>0xa0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11a"/>
      </object_component>
      <object_component id="oc-1cd">
         <name>.debug_aranges</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11e"/>
      </object_component>
      <object_component id="oc-1c5">
         <name>.debug_aranges</name>
         <load_address>0xe0</load_address>
         <run_address>0xe0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11f"/>
      </object_component>
      <object_component id="oc-199">
         <name>.debug_aranges</name>
         <load_address>0x100</load_address>
         <run_address>0x100</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-120"/>
      </object_component>
      <object_component id="oc-56">
         <name>.debug_aranges</name>
         <load_address>0x120</load_address>
         <run_address>0x120</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-121"/>
      </object_component>
      <object_component id="oc-1d5">
         <name>.debug_aranges</name>
         <load_address>0x140</load_address>
         <run_address>0x140</run_address>
         <size>0x30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-125"/>
      </object_component>
      <object_component id="oc-a9">
         <name>.debug_aranges</name>
         <load_address>0x170</load_address>
         <run_address>0x170</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12b"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x25f0</size>
         <contents>
            <object_component_ref idref="oc-b6"/>
            <object_component_ref idref="oc-c5"/>
            <object_component_ref idref="oc-6b"/>
            <object_component_ref idref="oc-e8"/>
            <object_component_ref idref="oc-1a0"/>
            <object_component_ref idref="oc-10b"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-113"/>
            <object_component_ref idref="oc-15a"/>
            <object_component_ref idref="oc-165"/>
            <object_component_ref idref="oc-19c"/>
            <object_component_ref idref="oc-e7"/>
            <object_component_ref idref="oc-ea"/>
            <object_component_ref idref="oc-eb"/>
            <object_component_ref idref="oc-190"/>
            <object_component_ref idref="oc-153"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-c0"/>
            <object_component_ref idref="oc-1e1"/>
            <object_component_ref idref="oc-102"/>
            <object_component_ref idref="oc-105"/>
            <object_component_ref idref="oc-b1"/>
            <object_component_ref idref="oc-1db"/>
            <object_component_ref idref="oc-1cb"/>
            <object_component_ref idref="oc-1bc"/>
            <object_component_ref idref="oc-1c3"/>
            <object_component_ref idref="oc-c2"/>
            <object_component_ref idref="oc-93"/>
            <object_component_ref idref="oc-198"/>
            <object_component_ref idref="oc-73"/>
            <object_component_ref idref="oc-ef"/>
            <object_component_ref idref="oc-10e"/>
            <object_component_ref idref="oc-173"/>
            <object_component_ref idref="oc-171"/>
            <object_component_ref idref="oc-ee"/>
            <object_component_ref idref="oc-f0"/>
            <object_component_ref idref="oc-182"/>
            <object_component_ref idref="oc-106"/>
            <object_component_ref idref="oc-f1"/>
            <object_component_ref idref="oc-f2"/>
            <object_component_ref idref="oc-ec"/>
            <object_component_ref idref="oc-1c7"/>
            <object_component_ref idref="oc-15b"/>
            <object_component_ref idref="oc-ed"/>
            <object_component_ref idref="oc-10f"/>
            <object_component_ref idref="oc-1d8"/>
            <object_component_ref idref="oc-ce"/>
            <object_component_ref idref="oc-1d3"/>
            <object_component_ref idref="oc-85"/>
            <object_component_ref idref="oc-1b6"/>
            <object_component_ref idref="oc-194"/>
            <object_component_ref idref="oc-148"/>
            <object_component_ref idref="oc-184"/>
            <object_component_ref idref="oc-79"/>
            <object_component_ref idref="oc-fd"/>
            <object_component_ref idref="oc-109"/>
            <object_component_ref idref="oc-10c"/>
            <object_component_ref idref="oc-10a"/>
            <object_component_ref idref="oc-10d"/>
            <object_component_ref idref="oc-fe"/>
            <object_component_ref idref="oc-1af"/>
            <object_component_ref idref="oc-18a"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-183"/>
            <object_component_ref idref="oc-e9"/>
            <object_component_ref idref="oc-147"/>
            <object_component_ref idref="oc-172"/>
            <object_component_ref idref="oc-9f"/>
            <object_component_ref idref="oc-14c"/>
            <object_component_ref idref="oc-14d"/>
            <object_component_ref idref="oc-145"/>
            <object_component_ref idref="oc-14a"/>
            <object_component_ref idref="oc-151"/>
            <object_component_ref idref="oc-166"/>
            <object_component_ref idref="oc-15d"/>
            <object_component_ref idref="oc-154"/>
            <object_component_ref idref="oc-174"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-146"/>
            <object_component_ref idref="oc-13d"/>
            <object_component_ref idref="oc-9e"/>
            <object_component_ref idref="oc-149"/>
            <object_component_ref idref="oc-139"/>
            <object_component_ref idref="oc-fc"/>
            <object_component_ref idref="oc-14f"/>
            <object_component_ref idref="oc-14b"/>
            <object_component_ref idref="oc-185"/>
            <object_component_ref idref="oc-140"/>
            <object_component_ref idref="oc-18d"/>
            <object_component_ref idref="oc-13c"/>
            <object_component_ref idref="oc-150"/>
            <object_component_ref idref="oc-13e"/>
            <object_component_ref idref="oc-13a"/>
            <object_component_ref idref="oc-15c"/>
            <object_component_ref idref="oc-13f"/>
            <object_component_ref idref="oc-13b"/>
            <object_component_ref idref="oc-a0"/>
            <object_component_ref idref="oc-18c"/>
            <object_component_ref idref="oc-175"/>
            <object_component_ref idref="oc-bb"/>
            <object_component_ref idref="oc-c1"/>
            <object_component_ref idref="oc-c3"/>
            <object_component_ref idref="oc-c4"/>
            <object_component_ref idref="oc-5b"/>
            <object_component_ref idref="oc-fb"/>
            <object_component_ref idref="oc-14e"/>
            <object_component_ref idref="oc-152"/>
            <object_component_ref idref="oc-15f"/>
            <object_component_ref idref="oc-160"/>
            <object_component_ref idref="oc-81"/>
            <object_component_ref idref="oc-78"/>
            <object_component_ref idref="oc-17c"/>
            <object_component_ref idref="oc-72"/>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-77"/>
            <object_component_ref idref="oc-16b"/>
            <object_component_ref idref="oc-a7"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-18b"/>
            <object_component_ref idref="oc-15e"/>
            <object_component_ref idref="oc-f3"/>
            <object_component_ref idref="oc-a5"/>
            <object_component_ref idref="oc-141"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-c7"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-128"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-8f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x2e70</load_address>
         <run_address>0x2e70</run_address>
         <size>0x38</size>
         <contents>
            <object_component_ref idref="oc-227"/>
            <object_component_ref idref="oc-225"/>
            <object_component_ref idref="oc-228"/>
            <object_component_ref idref="oc-226"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0x26b0</load_address>
         <run_address>0x26b0</run_address>
         <size>0x7c0</size>
         <contents>
            <object_component_ref idref="oc-18e"/>
            <object_component_ref idref="oc-16a"/>
            <object_component_ref idref="oc-168"/>
            <object_component_ref idref="oc-c6"/>
            <object_component_ref idref="oc-114"/>
            <object_component_ref idref="oc-11c"/>
            <object_component_ref idref="oc-127"/>
            <object_component_ref idref="oc-11b"/>
            <object_component_ref idref="oc-118"/>
            <object_component_ref idref="oc-11d"/>
            <object_component_ref idref="oc-126"/>
            <object_component_ref idref="oc-124"/>
            <object_component_ref idref="oc-123"/>
            <object_component_ref idref="oc-117"/>
            <object_component_ref idref="oc-125"/>
            <object_component_ref idref="oc-115"/>
            <object_component_ref idref="oc-122"/>
            <object_component_ref idref="oc-116"/>
            <object_component_ref idref="oc-119"/>
            <object_component_ref idref="oc-11a"/>
            <object_component_ref idref="oc-187"/>
            <object_component_ref idref="oc-189"/>
            <object_component_ref idref="oc-177"/>
            <object_component_ref idref="oc-179"/>
            <object_component_ref idref="oc-17b"/>
            <object_component_ref idref="oc-11e"/>
            <object_component_ref idref="oc-121"/>
            <object_component_ref idref="oc-162"/>
            <object_component_ref idref="oc-164"/>
            <object_component_ref idref="oc-161"/>
            <object_component_ref idref="oc-163"/>
            <object_component_ref idref="oc-169"/>
            <object_component_ref idref="oc-167"/>
            <object_component_ref idref="oc-186"/>
            <object_component_ref idref="oc-188"/>
            <object_component_ref idref="oc-176"/>
            <object_component_ref idref="oc-178"/>
            <object_component_ref idref="oc-17a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-1ef"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x202001d4</run_address>
         <size>0x13</size>
         <contents>
            <object_component_ref idref="oc-a2"/>
            <object_component_ref idref="oc-a1"/>
            <object_component_ref idref="oc-7b"/>
            <object_component_ref idref="oc-7c"/>
            <object_component_ref idref="oc-11f"/>
            <object_component_ref idref="oc-120"/>
            <object_component_ref idref="oc-107"/>
            <object_component_ref idref="oc-108"/>
            <object_component_ref idref="oc-ff"/>
            <object_component_ref idref="oc-74"/>
            <object_component_ref idref="oc-82"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x20200000</run_address>
         <size>0x1d1</size>
         <contents>
            <object_component_ref idref="oc-7d"/>
            <object_component_ref idref="oc-7a"/>
            <object_component_ref idref="oc-f4"/>
            <object_component_ref idref="oc-f5"/>
            <object_component_ref idref="oc-f6"/>
            <object_component_ref idref="oc-f7"/>
            <object_component_ref idref="oc-f8"/>
            <object_component_ref idref="oc-a6"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20203e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-22a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1e6" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1e7" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1e8" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1e9" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1ea" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1eb" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1ed" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-209" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1c71</size>
         <contents>
            <object_component_ref idref="oc-70"/>
            <object_component_ref idref="oc-f9"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-a3"/>
            <object_component_ref idref="oc-7e"/>
            <object_component_ref idref="oc-100"/>
            <object_component_ref idref="oc-75"/>
            <object_component_ref idref="oc-83"/>
            <object_component_ref idref="oc-103"/>
            <object_component_ref idref="oc-1ac"/>
            <object_component_ref idref="oc-1b4"/>
            <object_component_ref idref="oc-1b0"/>
            <object_component_ref idref="oc-1b2"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-132"/>
            <object_component_ref idref="oc-af"/>
            <object_component_ref idref="oc-88"/>
            <object_component_ref idref="oc-58"/>
            <object_component_ref idref="oc-50"/>
            <object_component_ref idref="oc-12c"/>
            <object_component_ref idref="oc-1a1"/>
            <object_component_ref idref="oc-1c2"/>
            <object_component_ref idref="oc-1cf"/>
            <object_component_ref idref="oc-1ba"/>
            <object_component_ref idref="oc-1da"/>
            <object_component_ref idref="oc-1bb"/>
            <object_component_ref idref="oc-18f"/>
            <object_component_ref idref="oc-1e2"/>
            <object_component_ref idref="oc-1d9"/>
            <object_component_ref idref="oc-1c1"/>
            <object_component_ref idref="oc-87"/>
            <object_component_ref idref="oc-1e3"/>
            <object_component_ref idref="oc-1e4"/>
            <object_component_ref idref="oc-1d0"/>
            <object_component_ref idref="oc-e0"/>
            <object_component_ref idref="oc-22c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-20b" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x104f6</size>
         <contents>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-b5"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-6d"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-ba"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-be"/>
            <object_component_ref idref="oc-142"/>
            <object_component_ref idref="oc-181"/>
            <object_component_ref idref="oc-155"/>
            <object_component_ref idref="oc-16e"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-cf"/>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-5e"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-c8"/>
            <object_component_ref idref="oc-12a"/>
            <object_component_ref idref="oc-19d"/>
            <object_component_ref idref="oc-1b9"/>
            <object_component_ref idref="oc-192"/>
            <object_component_ref idref="oc-1c8"/>
            <object_component_ref idref="oc-197"/>
            <object_component_ref idref="oc-111"/>
            <object_component_ref idref="oc-1cc"/>
            <object_component_ref idref="oc-1c4"/>
            <object_component_ref idref="oc-19a"/>
            <object_component_ref idref="oc-55"/>
            <object_component_ref idref="oc-1d4"/>
            <object_component_ref idref="oc-1df"/>
            <object_component_ref idref="oc-1c0"/>
            <object_component_ref idref="oc-aa"/>
            <object_component_ref idref="oc-22b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-20d" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xcf8</size>
         <contents>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-b4"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-6f"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-b9"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-bd"/>
            <object_component_ref idref="oc-180"/>
            <object_component_ref idref="oc-156"/>
            <object_component_ref idref="oc-16c"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-d0"/>
            <object_component_ref idref="oc-5c"/>
            <object_component_ref idref="oc-53"/>
            <object_component_ref idref="oc-cb"/>
            <object_component_ref idref="oc-1d7"/>
            <object_component_ref idref="oc-1de"/>
            <object_component_ref idref="oc-1bd"/>
            <object_component_ref idref="oc-a8"/>
         </contents>
      </logical_group>
      <logical_group id="lg-20f" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xab4a</size>
         <contents>
            <object_component_ref idref="oc-71"/>
            <object_component_ref idref="oc-fa"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-a4"/>
            <object_component_ref idref="oc-7f"/>
            <object_component_ref idref="oc-101"/>
            <object_component_ref idref="oc-76"/>
            <object_component_ref idref="oc-84"/>
            <object_component_ref idref="oc-104"/>
            <object_component_ref idref="oc-1ad"/>
            <object_component_ref idref="oc-1b5"/>
            <object_component_ref idref="oc-1b1"/>
            <object_component_ref idref="oc-1b3"/>
            <object_component_ref idref="oc-4c"/>
            <object_component_ref idref="oc-133"/>
            <object_component_ref idref="oc-b0"/>
            <object_component_ref idref="oc-89"/>
            <object_component_ref idref="oc-59"/>
            <object_component_ref idref="oc-51"/>
            <object_component_ref idref="oc-12d"/>
            <object_component_ref idref="oc-1a2"/>
            <object_component_ref idref="oc-1e5"/>
            <object_component_ref idref="oc-1d1"/>
         </contents>
      </logical_group>
      <logical_group id="lg-211" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x173c</size>
         <contents>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-b2"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-6e"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-b7"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-bc"/>
            <object_component_ref idref="oc-143"/>
            <object_component_ref idref="oc-17d"/>
            <object_component_ref idref="oc-157"/>
            <object_component_ref idref="oc-16f"/>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-d2"/>
            <object_component_ref idref="oc-91"/>
            <object_component_ref idref="oc-5f"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-c9"/>
            <object_component_ref idref="oc-129"/>
            <object_component_ref idref="oc-1e0"/>
            <object_component_ref idref="oc-1bf"/>
         </contents>
      </logical_group>
      <logical_group id="lg-213" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x78d9</size>
         <contents>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-b3"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-6c"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-b8"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-bf"/>
            <object_component_ref idref="oc-144"/>
            <object_component_ref idref="oc-17f"/>
            <object_component_ref idref="oc-159"/>
            <object_component_ref idref="oc-16d"/>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-d3"/>
            <object_component_ref idref="oc-92"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-ca"/>
            <object_component_ref idref="oc-12b"/>
            <object_component_ref idref="oc-19f"/>
            <object_component_ref idref="oc-1b8"/>
            <object_component_ref idref="oc-191"/>
            <object_component_ref idref="oc-1ca"/>
            <object_component_ref idref="oc-196"/>
            <object_component_ref idref="oc-112"/>
            <object_component_ref idref="oc-1ce"/>
            <object_component_ref idref="oc-1c6"/>
            <object_component_ref idref="oc-19b"/>
            <object_component_ref idref="oc-57"/>
            <object_component_ref idref="oc-1d6"/>
            <object_component_ref idref="oc-1dc"/>
            <object_component_ref idref="oc-1be"/>
            <object_component_ref idref="oc-ab"/>
         </contents>
      </logical_group>
      <logical_group id="lg-215" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x35f3</size>
         <contents>
            <object_component_ref idref="oc-1ae"/>
            <object_component_ref idref="oc-17e"/>
            <object_component_ref idref="oc-158"/>
            <object_component_ref idref="oc-170"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-d1"/>
            <object_component_ref idref="oc-5d"/>
            <object_component_ref idref="oc-5a"/>
            <object_component_ref idref="oc-52"/>
            <object_component_ref idref="oc-cd"/>
            <object_component_ref idref="oc-1dd"/>
            <object_component_ref idref="oc-1d2"/>
         </contents>
      </logical_group>
      <logical_group id="lg-21f" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x198</size>
         <contents>
            <object_component_ref idref="oc-19e"/>
            <object_component_ref idref="oc-1b7"/>
            <object_component_ref idref="oc-193"/>
            <object_component_ref idref="oc-1c9"/>
            <object_component_ref idref="oc-195"/>
            <object_component_ref idref="oc-110"/>
            <object_component_ref idref="oc-1cd"/>
            <object_component_ref idref="oc-1c5"/>
            <object_component_ref idref="oc-199"/>
            <object_component_ref idref="oc-56"/>
            <object_component_ref idref="oc-1d5"/>
            <object_component_ref idref="oc-a9"/>
         </contents>
      </logical_group>
      <logical_group id="lg-229" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-237" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2ea8</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-238" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0x1e7</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-f"/>
            <logical_group_ref idref="lg-e"/>
         </contents>
      </load_segment>
      <load_segment id="lg-239" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20203e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x8000</length>
         <used_space>0x2ea8</used_space>
         <unused_space>0x5158</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0x25f0</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x26b0</start_address>
               <size>0x7c0</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x2e70</start_address>
               <size>0x38</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0x2ea8</start_address>
               <size>0x5158</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x4000</length>
         <used_space>0x3e4</used_space>
         <unused_space>0x3c1c</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-1eb"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-1ed"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x1d1</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <available_space>
               <start_address>0x202001d1</start_address>
               <size>0x3</size>
            </available_space>
            <allocated_space>
               <start_address>0x202001d4</start_address>
               <size>0x13</size>
               <logical_group_ref idref="lg-e"/>
            </allocated_space>
            <available_space>
               <start_address>0x202001e7</start_address>
               <size>0x3c19</size>
            </available_space>
            <allocated_space>
               <start_address>0x20203e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0xff</length>
         <used_space>0x0</used_space>
         <unused_space>0xff</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.data</name>
            <load_address>0x2e70</load_address>
            <load_size>0xf</load_size>
            <run_address>0x202001d4</run_address>
            <run_size>0x13</run_size>
            <compression>lzss</compression>
         </cprec>
         <cprec>
            <name>.bss</name>
            <load_address>0x2e8c</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0x1d1</run_size>
            <compression>zero_init</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_decompress_lzss</name>
      </handler>
      <handler>
         <index>0x1</index>
         <name>__TI_decompress_none</name>
      </handler>
      <handler>
         <index>0x2</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0x2e94</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0x2ea4</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0x2ea4</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0x2e80</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0x2e8c</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20204000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-43">
         <name>main</name>
         <value>0x18d5</value>
         <object_component_ref idref="oc-93"/>
      </symbol>
      <symbol id="sm-44">
         <name>timerA_callback</name>
         <value>0x1983</value>
         <object_component_ref idref="oc-73"/>
      </symbol>
      <symbol id="sm-45">
         <name>timerB_callback</name>
         <value>0x1e3b</value>
         <object_component_ref idref="oc-85"/>
      </symbol>
      <symbol id="sm-46">
         <name>GROUP1_IRQHandler</name>
         <value>0x2687</value>
         <object_component_ref idref="oc-34"/>
      </symbol>
      <symbol id="sm-127">
         <name>SYSCFG_DL_init</name>
         <value>0x16dd</value>
         <object_component_ref idref="oc-b1"/>
      </symbol>
      <symbol id="sm-128">
         <name>SYSCFG_DL_initPower</name>
         <value>0x1199</value>
         <object_component_ref idref="oc-e7"/>
      </symbol>
      <symbol id="sm-129">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0x7d1</value>
         <object_component_ref idref="oc-e8"/>
      </symbol>
      <symbol id="sm-12a">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0x213d</value>
         <object_component_ref idref="oc-e9"/>
      </symbol>
      <symbol id="sm-12b">
         <name>SYSCFG_DL_PWM_6_init</name>
         <value>0x126d</value>
         <object_component_ref idref="oc-ea"/>
      </symbol>
      <symbol id="sm-12c">
         <name>SYSCFG_DL_PWM_7_init</name>
         <value>0x12f9</value>
         <object_component_ref idref="oc-eb"/>
      </symbol>
      <symbol id="sm-12d">
         <name>SYSCFG_DL_TIMER_8_init</name>
         <value>0x1c55</value>
         <object_component_ref idref="oc-ec"/>
      </symbol>
      <symbol id="sm-12e">
         <name>SYSCFG_DL_TIMER_12_init</name>
         <value>0x1d11</value>
         <object_component_ref idref="oc-ed"/>
      </symbol>
      <symbol id="sm-12f">
         <name>SYSCFG_DL_UART_0_init</name>
         <value>0x1abd</value>
         <object_component_ref idref="oc-ee"/>
      </symbol>
      <symbol id="sm-130">
         <name>SYSCFG_DL_UART_1_init</name>
         <value>0x1985</value>
         <object_component_ref idref="oc-ef"/>
      </symbol>
      <symbol id="sm-131">
         <name>SYSCFG_DL_UART_3_init</name>
         <value>0x1b05</value>
         <object_component_ref idref="oc-f0"/>
      </symbol>
      <symbol id="sm-132">
         <name>SYSCFG_DL_SPI_IMU660RB_init</name>
         <value>0x1bd5</value>
         <object_component_ref idref="oc-f1"/>
      </symbol>
      <symbol id="sm-133">
         <name>SYSCFG_DL_TFT_SPI_init</name>
         <value>0x1c15</value>
         <object_component_ref idref="oc-f2"/>
      </symbol>
      <symbol id="sm-134">
         <name>SYSCFG_DL_SYSTICK_init</name>
         <value>0x2661</value>
         <object_component_ref idref="oc-f3"/>
      </symbol>
      <symbol id="sm-135">
         <name>gPWM_6Backup</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-136">
         <name>gPWM_7Backup</name>
         <value>0x202000a0</value>
      </symbol>
      <symbol id="sm-137">
         <name>gUART_3Backup</name>
         <value>0x20200140</value>
      </symbol>
      <symbol id="sm-138">
         <name>gSPI_IMU660RBBackup</name>
         <value>0x20200170</value>
      </symbol>
      <symbol id="sm-139">
         <name>gTFT_SPIBackup</name>
         <value>0x20200198</value>
      </symbol>
      <symbol id="sm-144">
         <name>Default_Handler</name>
         <value>0x269f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-145">
         <name>Reset_Handler</name>
         <value>0x26a7</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-146">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-147">
         <name>NMI_Handler</name>
         <value>0x269f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-148">
         <name>HardFault_Handler</name>
         <value>0x269f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-149">
         <name>SVC_Handler</name>
         <value>0x269f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-14a">
         <name>PendSV_Handler</name>
         <value>0x269f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-14b">
         <name>SysTick_Handler</name>
         <value>0x269f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-14c">
         <name>GROUP0_IRQHandler</name>
         <value>0x269f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-14d">
         <name>ADC0_IRQHandler</name>
         <value>0x269f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-14e">
         <name>ADC1_IRQHandler</name>
         <value>0x269f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-14f">
         <name>CANFD0_IRQHandler</name>
         <value>0x269f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-150">
         <name>DAC0_IRQHandler</name>
         <value>0x269f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-151">
         <name>SPI0_IRQHandler</name>
         <value>0x269f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-152">
         <name>SPI1_IRQHandler</name>
         <value>0x269f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-153">
         <name>UART1_IRQHandler</name>
         <value>0x269f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-154">
         <name>UART2_IRQHandler</name>
         <value>0x269f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-155">
         <name>TIMG0_IRQHandler</name>
         <value>0x269f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-156">
         <name>TIMG6_IRQHandler</name>
         <value>0x269f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-157">
         <name>TIMA0_IRQHandler</name>
         <value>0x269f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-158">
         <name>TIMA1_IRQHandler</name>
         <value>0x269f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-159">
         <name>TIMG7_IRQHandler</name>
         <value>0x269f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-15a">
         <name>I2C0_IRQHandler</name>
         <value>0x269f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-15b">
         <name>I2C1_IRQHandler</name>
         <value>0x269f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-15c">
         <name>AES_IRQHandler</name>
         <value>0x269f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-15d">
         <name>RTC_IRQHandler</name>
         <value>0x269f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-15e">
         <name>DMA_IRQHandler</name>
         <value>0x269f</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-173">
         <name>left_counter</name>
         <value>0x202001d8</value>
         <object_component_ref idref="oc-a2"/>
      </symbol>
      <symbol id="sm-174">
         <name>right_counter</name>
         <value>0x202001da</value>
         <object_component_ref idref="oc-a1"/>
      </symbol>
      <symbol id="sm-175">
         <name>encoder_exti_callback</name>
         <value>0x5c5</value>
         <object_component_ref idref="oc-6b"/>
      </symbol>
      <symbol id="sm-1bc">
         <name>openmv_init</name>
         <value>0x24dd</value>
         <object_component_ref idref="oc-c1"/>
      </symbol>
      <symbol id="sm-1bd">
         <name>openmv_analysis</name>
         <value>0x1f15</value>
         <object_component_ref idref="oc-79"/>
      </symbol>
      <symbol id="sm-1be">
         <name>openmvData</name>
         <value>0x202001c8</value>
      </symbol>
      <symbol id="sm-1bf">
         <name>UART3_IRQHandler</name>
         <value>0xca9</value>
         <object_component_ref idref="oc-3e"/>
      </symbol>
      <symbol id="sm-1c0">
         <name>openmv_is_data_valid</name>
         <value>0x19d9</value>
         <object_component_ref idref="oc-10e"/>
      </symbol>
      <symbol id="sm-1c1">
         <name>openmv_display_data</name>
         <value>0x349</value>
         <object_component_ref idref="oc-c5"/>
      </symbol>
      <symbol id="sm-1fb">
         <name>tft180_write_8bit_data</name>
         <value>0x2025</value>
         <object_component_ref idref="oc-fe"/>
      </symbol>
      <symbol id="sm-1fc">
         <name>tft180_write_16bit_data</name>
         <value>0x1b91</value>
         <object_component_ref idref="oc-106"/>
      </symbol>
      <symbol id="sm-1fd">
         <name>tft180_clear_color</name>
         <value>0x1511</value>
         <object_component_ref idref="oc-c0"/>
      </symbol>
      <symbol id="sm-1fe">
         <name>tft180_init</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-b6"/>
      </symbol>
      <symbol id="sm-1ff">
         <name>tft180_show_char_color</name>
         <value>0xb79</value>
         <object_component_ref idref="oc-10b"/>
      </symbol>
      <symbol id="sm-200">
         <name>ascii_font_8x16</name>
         <value>0x26b0</value>
         <object_component_ref idref="oc-18e"/>
      </symbol>
      <symbol id="sm-201">
         <name>func_float_to_str</name>
         <value>0x9bd</value>
         <object_component_ref idref="oc-1a0"/>
      </symbol>
      <symbol id="sm-202">
         <name>tft180_show_num_color</name>
         <value>0xdc9</value>
         <object_component_ref idref="oc-113"/>
      </symbol>
      <symbol id="sm-203">
         <name>tft180_show_string_color</name>
         <value>0x1873</value>
         <object_component_ref idref="oc-c2"/>
      </symbol>
      <symbol id="sm-224">
         <name>timerA_init</name>
         <value>0x24f3</value>
         <object_component_ref idref="oc-c3"/>
      </symbol>
      <symbol id="sm-225">
         <name>timerB_init</name>
         <value>0x2509</value>
         <object_component_ref idref="oc-c4"/>
      </symbol>
      <symbol id="sm-226">
         <name>TIMG8_IRQHandler</name>
         <value>0x20a1</value>
         <object_component_ref idref="oc-39"/>
      </symbol>
      <symbol id="sm-227">
         <name>TIMG12_IRQHandler</name>
         <value>0x22b9</value>
         <object_component_ref idref="oc-48"/>
      </symbol>
      <symbol id="sm-228">
         <name>get_system_time_ms</name>
         <value>0x2671</value>
         <object_component_ref idref="oc-a5"/>
      </symbol>
      <symbol id="sm-239">
         <name>UART0_IRQHandler</name>
         <value>0x20c9</value>
         <object_component_ref idref="oc-43"/>
      </symbol>
      <symbol id="sm-23a">
         <name>uart_data</name>
         <value>0x202001e6</value>
         <object_component_ref idref="oc-82"/>
      </symbol>
      <symbol id="sm-246">
         <name>delay_ms</name>
         <value>0x24c7</value>
         <object_component_ref idref="oc-bb"/>
      </symbol>
      <symbol id="sm-247">
         <name>delay_us</name>
         <value>0x15fd</value>
         <object_component_ref idref="oc-102"/>
      </symbol>
      <symbol id="sm-24d">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-24e">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-24f">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-250">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-251">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-252">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-253">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-254">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-255">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-25e">
         <name>DL_Common_delayCycles</name>
         <value>0x267d</value>
         <object_component_ref idref="oc-141"/>
      </symbol>
      <symbol id="sm-26b">
         <name>DL_SPI_init</name>
         <value>0x1b4d</value>
         <object_component_ref idref="oc-182"/>
      </symbol>
      <symbol id="sm-26c">
         <name>DL_SPI_setClockConfig</name>
         <value>0x25c1</value>
         <object_component_ref idref="oc-17c"/>
      </symbol>
      <symbol id="sm-288">
         <name>DL_Timer_setClockConfig</name>
         <value>0x2281</value>
         <object_component_ref idref="oc-154"/>
      </symbol>
      <symbol id="sm-289">
         <name>DL_Timer_initTimerMode</name>
         <value>0xfd9</value>
         <object_component_ref idref="oc-165"/>
      </symbol>
      <symbol id="sm-28a">
         <name>DL_Timer_setCaptureCompareValue</name>
         <value>0x2651</value>
         <object_component_ref idref="oc-15e"/>
      </symbol>
      <symbol id="sm-28b">
         <name>DL_Timer_setCaptCompUpdateMethod</name>
         <value>0x2265</value>
         <object_component_ref idref="oc-15d"/>
      </symbol>
      <symbol id="sm-28c">
         <name>DL_Timer_setCaptureCompareOutCtl</name>
         <value>0x243d</value>
         <object_component_ref idref="oc-15c"/>
      </symbol>
      <symbol id="sm-28d">
         <name>DL_Timer_initFourCCPWMMode</name>
         <value>0xed5</value>
         <object_component_ref idref="oc-15a"/>
      </symbol>
      <symbol id="sm-29a">
         <name>DL_UART_init</name>
         <value>0x1a75</value>
         <object_component_ref idref="oc-171"/>
      </symbol>
      <symbol id="sm-29b">
         <name>DL_UART_setClockConfig</name>
         <value>0x2609</value>
         <object_component_ref idref="oc-16b"/>
      </symbol>
      <symbol id="sm-2af">
         <name>_c_int00_noargs</name>
         <value>0x20f1</value>
         <object_component_ref idref="oc-64"/>
      </symbol>
      <symbol id="sm-2b0">
         <name>__stack</name>
         <value>0x20203e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-2bc">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0x1dc5</value>
         <object_component_ref idref="oc-ce"/>
      </symbol>
      <symbol id="sm-2c4">
         <name>_system_pre_init</name>
         <value>0x26ab</value>
         <object_component_ref idref="oc-8f"/>
      </symbol>
      <symbol id="sm-2cf">
         <name>__TI_zero_init_nomemset</name>
         <value>0x251f</value>
         <object_component_ref idref="oc-5b"/>
      </symbol>
      <symbol id="sm-2d8">
         <name>__TI_decompress_none</name>
         <value>0x262d</value>
         <object_component_ref idref="oc-20"/>
      </symbol>
      <symbol id="sm-2e3">
         <name>__TI_decompress_lzss</name>
         <value>0x1495</value>
         <object_component_ref idref="oc-1c"/>
      </symbol>
      <symbol id="sm-2f2">
         <name>abort</name>
         <value>0x2699</value>
         <object_component_ref idref="oc-c7"/>
      </symbol>
      <symbol id="sm-306">
         <name>HOSTexit</name>
         <value>0x26a3</value>
         <object_component_ref idref="oc-128"/>
      </symbol>
      <symbol id="sm-307">
         <name>C$$EXIT</name>
         <value>0x26a2</value>
         <object_component_ref idref="oc-128"/>
      </symbol>
      <symbol id="sm-31c">
         <name>__aeabi_fadd</name>
         <value>0x10cb</value>
         <object_component_ref idref="oc-19c"/>
      </symbol>
      <symbol id="sm-31d">
         <name>__addsf3</name>
         <value>0x10cb</value>
         <object_component_ref idref="oc-19c"/>
      </symbol>
      <symbol id="sm-31e">
         <name>__aeabi_fsub</name>
         <value>0x10c1</value>
         <object_component_ref idref="oc-19c"/>
      </symbol>
      <symbol id="sm-31f">
         <name>__subsf3</name>
         <value>0x10c1</value>
         <object_component_ref idref="oc-19c"/>
      </symbol>
      <symbol id="sm-325">
         <name>__muldsi3</name>
         <value>0x1e3d</value>
         <object_component_ref idref="oc-1b6"/>
      </symbol>
      <symbol id="sm-32b">
         <name>__aeabi_fmul</name>
         <value>0x1385</value>
         <object_component_ref idref="oc-190"/>
      </symbol>
      <symbol id="sm-32c">
         <name>__mulsf3</name>
         <value>0x1385</value>
         <object_component_ref idref="oc-190"/>
      </symbol>
      <symbol id="sm-332">
         <name>__aeabi_f2d</name>
         <value>0x1c95</value>
         <object_component_ref idref="oc-1c7"/>
      </symbol>
      <symbol id="sm-333">
         <name>__extendsfdf2</name>
         <value>0x1c95</value>
         <object_component_ref idref="oc-1c7"/>
      </symbol>
      <symbol id="sm-339">
         <name>__aeabi_f2iz</name>
         <value>0x1e79</value>
         <object_component_ref idref="oc-194"/>
      </symbol>
      <symbol id="sm-33a">
         <name>__fixsfsi</name>
         <value>0x1e79</value>
         <object_component_ref idref="oc-194"/>
      </symbol>
      <symbol id="sm-340">
         <name>__aeabi_i2f</name>
         <value>0x1d4d</value>
         <object_component_ref idref="oc-10f"/>
      </symbol>
      <symbol id="sm-341">
         <name>__floatsisf</name>
         <value>0x1d4d</value>
         <object_component_ref idref="oc-10f"/>
      </symbol>
      <symbol id="sm-347">
         <name>__aeabi_dcmpeq</name>
         <value>0x17ad</value>
         <object_component_ref idref="oc-1cb"/>
      </symbol>
      <symbol id="sm-348">
         <name>__aeabi_dcmplt</name>
         <value>0x17c1</value>
         <object_component_ref idref="oc-1cb"/>
      </symbol>
      <symbol id="sm-349">
         <name>__aeabi_dcmple</name>
         <value>0x17d5</value>
         <object_component_ref idref="oc-1cb"/>
      </symbol>
      <symbol id="sm-34a">
         <name>__aeabi_dcmpge</name>
         <value>0x17e9</value>
         <object_component_ref idref="oc-1cb"/>
      </symbol>
      <symbol id="sm-34b">
         <name>__aeabi_dcmpgt</name>
         <value>0x17fd</value>
         <object_component_ref idref="oc-1cb"/>
      </symbol>
      <symbol id="sm-351">
         <name>__aeabi_fcmpeq</name>
         <value>0x1811</value>
         <object_component_ref idref="oc-1c3"/>
      </symbol>
      <symbol id="sm-352">
         <name>__aeabi_fcmplt</name>
         <value>0x1825</value>
         <object_component_ref idref="oc-1c3"/>
      </symbol>
      <symbol id="sm-353">
         <name>__aeabi_fcmple</name>
         <value>0x1839</value>
         <object_component_ref idref="oc-1c3"/>
      </symbol>
      <symbol id="sm-354">
         <name>__aeabi_fcmpge</name>
         <value>0x184d</value>
         <object_component_ref idref="oc-1c3"/>
      </symbol>
      <symbol id="sm-355">
         <name>__aeabi_fcmpgt</name>
         <value>0x1861</value>
         <object_component_ref idref="oc-1c3"/>
      </symbol>
      <symbol id="sm-35b">
         <name>__aeabi_idiv</name>
         <value>0x192d</value>
         <object_component_ref idref="oc-198"/>
      </symbol>
      <symbol id="sm-35c">
         <name>__aeabi_idivmod</name>
         <value>0x192d</value>
         <object_component_ref idref="oc-198"/>
      </symbol>
      <symbol id="sm-362">
         <name>__aeabi_memcpy</name>
         <value>0x2691</value>
         <object_component_ref idref="oc-54"/>
      </symbol>
      <symbol id="sm-363">
         <name>__aeabi_memcpy4</name>
         <value>0x2691</value>
         <object_component_ref idref="oc-54"/>
      </symbol>
      <symbol id="sm-364">
         <name>__aeabi_memcpy8</name>
         <value>0x2691</value>
         <object_component_ref idref="oc-54"/>
      </symbol>
      <symbol id="sm-36d">
         <name>__eqsf2</name>
         <value>0x1e01</value>
         <object_component_ref idref="oc-1d3"/>
      </symbol>
      <symbol id="sm-36e">
         <name>__lesf2</name>
         <value>0x1e01</value>
         <object_component_ref idref="oc-1d3"/>
      </symbol>
      <symbol id="sm-36f">
         <name>__ltsf2</name>
         <value>0x1e01</value>
         <object_component_ref idref="oc-1d3"/>
      </symbol>
      <symbol id="sm-370">
         <name>__nesf2</name>
         <value>0x1e01</value>
         <object_component_ref idref="oc-1d3"/>
      </symbol>
      <symbol id="sm-371">
         <name>__cmpsf2</name>
         <value>0x1e01</value>
         <object_component_ref idref="oc-1d3"/>
      </symbol>
      <symbol id="sm-372">
         <name>__gtsf2</name>
         <value>0x1d89</value>
         <object_component_ref idref="oc-1d8"/>
      </symbol>
      <symbol id="sm-373">
         <name>__gesf2</name>
         <value>0x1d89</value>
         <object_component_ref idref="oc-1d8"/>
      </symbol>
      <symbol id="sm-381">
         <name>__ledf2</name>
         <value>0x1745</value>
         <object_component_ref idref="oc-1db"/>
      </symbol>
      <symbol id="sm-382">
         <name>__gedf2</name>
         <value>0x1589</value>
         <object_component_ref idref="oc-1e1"/>
      </symbol>
      <symbol id="sm-383">
         <name>__cmpdf2</name>
         <value>0x1745</value>
         <object_component_ref idref="oc-1db"/>
      </symbol>
      <symbol id="sm-384">
         <name>__eqdf2</name>
         <value>0x1745</value>
         <object_component_ref idref="oc-1db"/>
      </symbol>
      <symbol id="sm-385">
         <name>__ltdf2</name>
         <value>0x1745</value>
         <object_component_ref idref="oc-1db"/>
      </symbol>
      <symbol id="sm-386">
         <name>__nedf2</name>
         <value>0x1745</value>
         <object_component_ref idref="oc-1db"/>
      </symbol>
      <symbol id="sm-387">
         <name>__gtdf2</name>
         <value>0x1589</value>
         <object_component_ref idref="oc-1e1"/>
      </symbol>
      <symbol id="sm-391">
         <name>__aeabi_idiv0</name>
         <value>0x180f</value>
         <object_component_ref idref="oc-1bc"/>
      </symbol>
      <symbol id="sm-39b">
         <name>TI_memcpy_small</name>
         <value>0x261b</value>
         <object_component_ref idref="oc-a7"/>
      </symbol>
      <symbol id="sm-39c">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-39f">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-3a0">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
