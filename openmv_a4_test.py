# OpenMV A4纸检测测试程序
# 用于验证与MSPM0G3507的A4纸中心偏移量通信

import time
from machine import UART

# 初始化UART1，波特率9600
uart = UART(1, baudrate=9600)

def send_a4_rect_data(detection_flag, offset_x, offset_y):
    """
    发送A4纸检测数据到MSPM0G3507
    数据格式：[0x2B, 0x11, data0, data1, data2, data3, 0x5A, 0x59]
    data0: 检测标志 (0=未检测到A4纸, 1=检测到A4纸)
    data1: X轴偏移量 (有符号，-128到127)
    data2: Y轴偏移量 (有符号，-128到127)
    data3: 保留字节 (设为0)
    """
    # 限制数据范围
    if offset_x > 127:
        offset_x = 127
    elif offset_x < -128:
        offset_x = -128
        
    if offset_y > 127:
        offset_y = 127
    elif offset_y < -128:
        offset_y = -128
    
    # 构造数据
    data0 = detection_flag & 0xFF
    data1 = offset_x & 0xFF
    data2 = offset_y & 0xFF
    data3 = 0  # 保留字节
    
    # 发送数据帧
    msg = bytearray([0x2B, 0x11, data0, data1, data2, data3, 0x5A, 0x59])
    uart.write(msg)
    
    print(f"Sent: flag={detection_flag}, x_offset={offset_x}, y_offset={offset_y}")

# 测试序列 - 模拟A4纸在不同位置的情况
test_cases = [
    (0, 0, 0),        # 未检测到A4纸
    (1, 0, 0),        # 检测到A4纸，居中
    (1, 40, 0),       # 检测到A4纸，右偏40
    (1, -35, 0),      # 检测到A4纸，左偏35
    (1, 0, 25),       # 检测到A4纸，下偏25
    (1, 0, -20),      # 检测到A4纸，上偏20
    (1, 30, 20),      # 检测到A4纸，右下偏移
    (1, -25, -15),    # 检测到A4纸，左上偏移
    (1, 45, -30),     # 检测到A4纸，右上偏移
    (1, -40, 35),     # 检测到A4纸，左下偏移
    (1, 60, 0),       # 检测到A4纸，右偏60
    (1, -50, 0),      # 检测到A4纸，左偏50
    (0, 0, 0),        # 未检测到A4纸
]

print("OpenMV A4 Paper Detection Test Program Started")
print("Sending test data to MSPM0G3507...")
print("Data format: detection_flag, x_offset, y_offset")
print("X_offset: negative=left, positive=right")
print("Y_offset: negative=up, positive=down")
print("A4 paper aspect ratio: 1.2 < ratio < 1.6")
print("-" * 50)

test_index = 0
while True:
    # 循环发送测试数据
    detection_flag, offset_x, offset_y = test_cases[test_index]
    send_a4_rect_data(detection_flag, offset_x, offset_y)
    
    # 显示当前测试状态
    if detection_flag == 1:
        if offset_x == 0 and offset_y == 0:
            status = "A4 paper centered"
        elif offset_x > 0 and offset_y == 0:
            status = f"A4 paper right {offset_x}px"
        elif offset_x < 0 and offset_y == 0:
            status = f"A4 paper left {abs(offset_x)}px"
        elif offset_x == 0 and offset_y > 0:
            status = f"A4 paper down {offset_y}px"
        elif offset_x == 0 and offset_y < 0:
            status = f"A4 paper up {abs(offset_y)}px"
        else:
            status = f"A4 paper at ({offset_x}, {offset_y})"
    else:
        status = "No A4 paper detected"
    
    print(f"Test {test_index + 1}: {status}")
    
    # 切换到下一个测试用例
    test_index = (test_index + 1) % len(test_cases)
    
    # 等待2秒
    time.sleep(2)
