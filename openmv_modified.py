import sensor, image, time
from machine import UART

# --- 传感器初始化 ---
sensor.reset()
sensor.set_pixformat(sensor.GRAYSCALE) # 设置为灰度图像
sensor.set_framesize(sensor.QQVGA)     # 160x120 分辨率
sensor.skip_frames(time = 2000)
clock = time.clock()

# ---------------------------
# 初始化 UART1，用于向 MSPM0G3507 发送数据
# ---------------------------
uart = UART(1, baudrate=9600)  # 9600波特率，与MSPM0配置匹配

# --- 亮度自动控制部分 ---
initial_brightness = 500
L_set          = 80
PWM_MIN, PWM_MAX = 100, 4000
Kp, Ki, Kd    = 0.0, 1.0, 0.0
alpha          = 0.9
deadband_dark  = 3
deadband_light = 1
delta_pwm_max  = 500
filtered_L    = L_set
prev_error    = 0.0
last_error    = 0.0
pwm           = float(initial_brightness)

def update_pwm(current_img):
    global filtered_L, prev_error, last_error, pwm
    raw_L = current_img.get_statistics().l_mean()
    filtered_L = alpha * raw_L + (1 - alpha) * filtered_L
    L_now      = int(filtered_L)
    if L_now < L_set - 10: deadband = deadband_dark
    else: deadband = deadband_light
    error = L_set - L_now
    if abs(error) <= deadband:
        prev_error, last_error = last_error, error
        return int(pwm)
    d_error   = error - last_error
    dd_error  = error - 2*last_error + prev_error
    delta_u   = (Kp * d_error) + (Ki * error) + (Kd * dd_error)
    delta_u = max(min(delta_u, delta_pwm_max), -delta_pwm_max)
    pwm = pwm + delta_u
    pwm = max(min(pwm, PWM_MAX), PWM_MIN)
    prev_error, last_error = last_error, error
    return int(pwm)

# 定义二值化的阈值
binary_threshold = (100, 255)

# ======================= 平滑处理参数 =======================
smoothing_alpha = 0.8
smoothed_cx = None
smoothed_cy = None

def send_data_to_mspm0(detection_flag, offset_x, offset_y):
    """
    发送数据到MSPM0G3507
    数据格式：[0x2B, 0x11, data0, data1, data2, data3, 0x5A, 0x59]
    data0: 检测标志 (0=未检测到, 1=检测到)
    data1: X轴偏移量 (有符号，-128到127)
    data2: Y轴偏移量低字节 (有符号，-128到127)
    data3: 保留字节 (暂时设为0)
    """
    # 限制偏移量范围
    if offset_x > 127:
        offset_x = 127
    elif offset_x < -128:
        offset_x = -128

    if offset_y > 127:
        offset_y = 127
    elif offset_y < -128:
        offset_y = -128

    # 转换为无符号字节
    data0 = detection_flag & 0xFF
    data1 = offset_x & 0xFF  # X轴偏移量
    data2 = offset_y & 0xFF  # Y轴偏移量
    data3 = 0                # 保留字节

    # 构造消息
    msg = bytearray([0x2B, 0x11, data0, data1, data2, data3, 0x5A, 0x59])
    uart.write(msg)

    # 调试输出
    print(f"Sent: flag={detection_flag}, x_offset={offset_x}, y_offset={offset_y}")

# --- 主循环 ---
while(True):
    clock.tick()

    # 1. 拍摄与处理图像
    img_original = sensor.snapshot()
    # (亮度控制) update_pwm(img_original) ...
    img_for_processing = img_original.copy().binary([binary_threshold])
    rects = img_for_processing.find_rects(threshold = 15000)

    # 定义一个变量，用于存放本轮循环找到的最佳矩形
    found_rect = None

    if rects:
        # 寻找面积最大的矩形
        largest_rect = max(rects, key=lambda r: r.w() * r.h())
        # 进行有效性检查
        if largest_rect and largest_rect.w() > 0 and largest_rect.h() > 0 and (largest_rect.w() * largest_rect.h()) > 400:
            found_rect = largest_rect

    # --- 核心修改：平滑与绘制逻辑 ---
    if found_rect:
        # 如果当前帧找到了有效矩形

        # 1. 计算当前帧的 "原始" 中心点
        raw_cx = found_rect.x() + found_rect.w() // 2
        raw_cy = found_rect.y() + found_rect.h() // 2

        # 2. 应用指数移动平均滤波
        if smoothed_cx is None:
            # 如果是第一次检测到，直接用原始值作为平滑初始值
            smoothed_cx = raw_cx
            smoothed_cy = raw_cy
        else:
            # 如果不是第一次，应用平滑公式
            smoothed_cx = int((smoothing_alpha * raw_cx) + (1 - smoothing_alpha) * smoothed_cx)
            smoothed_cy = int((smoothing_alpha * raw_cy) + (1 - smoothing_alpha) * smoothed_cy)

        # 3. 绘制检测到的原始矩形边框（红色）
        corners = found_rect.corners()
        for i in range(4):
            p1 = corners[i]
            p2 = corners[(i + 1) % 4]
            img_original.draw_line(p1[0], p1[1], p2[0], p2[1], color=(255, 0, 0), thickness=2)

    # 4. 无论当前帧是否找到矩形，都尝试绘制平滑后的中心点
    #    这样做可以避免在丢帧时中心标记闪烁
    if smoothed_cx is not None:
        # 只要平滑坐标有效（即至少成功检测到过一次）

        # 决定标记的颜色：如果本帧找到了就用蓝色，如果没找到（丢帧）就用青色表示"最后位置"
        cross_color = (0, 0, 255) if found_rect else (0, 255, 255)

        # 绘制平滑后的中心十字（蓝色或青色）
        img_original.draw_cross(smoothed_cx, smoothed_cy, size=10, color=cross_color, thickness=2)

        # 在平滑后的坐标旁显示文字
        text = f"({smoothed_cx}, {smoothed_cy})"
        img_original.draw_string(smoothed_cx + 10, smoothed_cy - 10, text, color=(255, 255, 0), scale=1.5)

        # 计算与图像中心的偏移量
        img_center_x = sensor.width() // 2   # 80
        img_center_y = sensor.height() // 2  # 60
        offset_x = smoothed_cx - img_center_x
        offset_y = smoothed_cy - img_center_y

        # 发送数据到MSPM0G3507
        send_data_to_mspm0(1, offset_x, offset_y)

        # 显示偏移量信息
        img_original.draw_string(10, 10, f"Offset: ({offset_x}, {offset_y})", color=(255, 255, 0), scale=1.5)
    else:
        # 没有检测到矩形，发送无效数据
        send_data_to_mspm0(0, 0, 0)

    # 5. 打印帧率和信息
    print(f"FPS: {clock.fps():.2f} | PWM: {int(pwm)}")

    # 延时
    time.sleep_ms(50)  # 50ms发送一次，20Hz
