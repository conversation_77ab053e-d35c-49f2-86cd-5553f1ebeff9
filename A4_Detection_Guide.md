# OpenMV A4纸检测与MSPM0G3507通信指南

## 📋 功能概述

这个系统实现了OpenMV检测A4纸矩形并将矩形中心与图像中心的偏移量发送给MSPM0G3507的功能。

### 主要特性：
- 保持原有的A4纸检测逻辑
- 长宽比筛选 (1.2 < ratio < 1.6)
- 最小面积筛选 (>400像素)
- 实时偏移量计算
- UART通信传输数据
- TFT屏幕显示检测结果

## 🔧 硬件连接

```
OpenMV ←→ MSPM0G3507
P4 (TX) ←→ PA9 (UART1_RX)
P5 (RX) ←→ PA8 (UART1_TX)
GND     ←→ GND
VCC     ←→ 3.3V
```

## 📡 通信协议

### 数据帧格式
```
[0x2B, 0x11, data0, data1, data2, data3, 0x5A, 0x59]
```

| 字节 | 内容 | 说明 |
|------|------|------|
| 0-1 | 0x2B, 0x11 | 帧头 |
| 2 | data0 | 检测标志 (0=未检测到A4纸, 1=检测到A4纸) |
| 3 | data1 | X轴偏移量 (有符号，-128到127) |
| 4 | data2 | Y轴偏移量 (有符号，-128到127) |
| 5 | data3 | 保留字节 (设为0) |
| 6-7 | 0x5A, 0x59 | 帧尾 |

### 偏移量说明
- **X轴偏移量**: 
  - 负值 = A4纸在图像中心左侧
  - 正值 = A4纸在图像中心右侧
  - 0 = A4纸在X轴中心
- **Y轴偏移量**: 
  - 负值 = A4纸在图像中心上方
  - 正值 = A4纸在图像中心下方
  - 0 = A4纸在Y轴中心

## 💻 OpenMV代码说明

### 保持的原有功能：

1. **A4纸检测逻辑**：
   - 灰度图像处理
   - 二值化处理
   - 矩形检测和筛选
   - 长宽比验证 (1.2-1.6)
   - 最小面积验证 (>400)

2. **亮度控制**：
   - PID亮度控制算法
   - 自动曝光调整
   - 所有原始参数保持不变

3. **图像显示**：
   - 红色矩形框
   - 蓝色中心十字
   - 帧率显示

### 新增功能：

1. **UART通信**：
   - 9600波特率
   - 20Hz发送频率
   - 协议格式化

2. **偏移量计算**：
   - 矩形中心坐标计算
   - 与图像中心比较
   - 偏移量范围限制

3. **图像中心标记**：
   - 绿色十字标记图像中心
   - 便于观察偏移效果

## 🖥️ MSPM0G3507显示内容

### TFT屏幕显示：
```
OpenMV Rect Data:
Rect: FOUND
X Offset: +25 
Y Offset: -15 
X-Dir: RIGHT
Y-Dir: UP
```

### 显示状态：
- **检测状态**: FOUND / NOT FOUND
- **偏移量**: 数值显示（已修复重叠问题）
- **方向指示**: LEFT/RIGHT/CENTER, UP/DOWN/CENTER

## 🚀 使用步骤

### 1. 准备工作
- 确保硬件连接正确
- 检查波特率设置(9600)
- 准备A4纸作为检测目标

### 2. 代码部署
- 将 `openmv_a4_detection_with_uart.py` 烧录到OpenMV
- 确保MSPM0G3507代码已编译烧录

### 3. 测试验证
- 先使用 `openmv_a4_test.py` 测试通信
- 观察TFT屏幕显示是否正常
- 确认数据更新频率

### 4. 实际应用
- 将A4纸放在摄像头前
- 观察检测效果和偏移量显示
- 根据需要调整参数

## ⚙️ 参数调整指南

### A4纸检测参数：

1. **二值化阈值** (`binary_threshold = (100, 255)`):
   - 根据光照条件调整
   - 值越小检测越敏感
   - 建议范围: (80, 255) ~ (120, 255)

2. **矩形检测阈值** (`threshold = 15000`):
   - 值越大检测越严格
   - 建议范围: 10000-20000

3. **长宽比范围** (`1.2 < ratio < 1.6`):
   - A4纸标准比例约1.414
   - 可根据实际情况微调

4. **最小面积** (`area > 400`):
   - 过滤小噪声
   - 根据检测距离调整

### 亮度控制参数：

1. **目标亮度** (`L_set = 80`):
   - 根据环境光调整
   - 建议范围: 60-100

2. **PID参数** (`Kp=0.0, Ki=1.0, Kd=0.0`):
   - 根据响应速度调整
   - 当前设置为纯积分控制

## 🔍 调试方法

### 1. 通信调试
- 使用测试程序验证基础通信
- 检查串口连接和波特率
- 观察调试输出信息

### 2. 检测调试
- 调整光照条件
- 修改二值化阈值
- 观察A4纸检测效果
- 检查长宽比筛选

### 3. 显示调试
- 检查TFT屏幕显示
- 验证数据更新频率
- 确认偏移量计算正确

## 📝 注意事项

1. **光照条件**: 确保良好的光照环境
2. **A4纸对比度**: A4纸与背景要有足够对比度
3. **检测距离**: 保持适当的检测距离
4. **纸张平整**: 确保A4纸平整，避免折叠
5. **环境稳定**: 避免剧烈震动影响检测

## 🛠️ 故障排除

### 问题1: 屏幕显示"NO DATA"
- 检查UART连接
- 确认波特率匹配
- 验证OpenMV是否正常运行

### 问题2: 检测不到A4纸
- 调整二值化阈值
- 改善光照条件
- 检查A4纸对比度
- 验证长宽比设置

### 问题3: 检测到错误矩形
- 提高面积阈值
- 调整长宽比范围
- 改善环境条件

### 问题4: 偏移量不准确
- 校准摄像头位置
- 检查计算公式
- 验证坐标系定义

## 📊 性能指标

- **检测精度**: 长宽比1.2-1.6的矩形
- **最小检测面积**: 400像素
- **通信频率**: 20Hz
- **偏移量范围**: ±127像素
- **图像分辨率**: 160x120 (QQVGA)

## 🎯 应用场景

1. **文档扫描**: A4纸自动定位
2. **纸张分拣**: 自动识别和定位
3. **机器人导航**: 基于A4纸的视觉导航
4. **自动对齐**: 设备自动对齐A4纸
