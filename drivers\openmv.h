//
// Created by f<PERSON><PERSON><PERSON> on 2024/7/25.
//

#ifndef OPENMV_H
#define OPENMV_H

#include "common_inc.h"

// OpenMV数据结构体
typedef struct {
    uint8_t detection_flag;    // 检测标志：0=未检测到，1=检测到目标
    int8_t  offset_x;          // X轴偏移量（有符号）
    uint16_t distance_mm;      // 距离（毫米）
    uint8_t data_valid;        // 数据有效标志
    uint32_t last_update_time; // 最后更新时间（用于超时检测）
}openmv_data_t;

// 命令结构体（用于发送命令给OpenMV）
typedef struct {
    uint8_t num1;
    uint8_t num2;
    uint8_t num3;
    uint8_t num4;
}openmv_command_t;

extern openmv_data_t openmvData;
extern openmv_command_t openmvCommand;

// 函数声明
void openmv_init(void);
void send_openmv_command(void);
void openmv_display_data(void);
uint8_t openmv_is_data_valid(void);
void openmv_send_test_command(void);
openmv_data_t* openmv_get_data(void);

// 数据超时时间（毫秒）
#define OPENMV_DATA_TIMEOUT_MS  500

#endif //OPENMV_H
