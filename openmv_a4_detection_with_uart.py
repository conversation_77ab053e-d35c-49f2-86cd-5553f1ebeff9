import sensor, image, time
from machine import UART

# --- 传感器初始化 ---
sensor.reset()
sensor.set_pixformat(sensor.GRAYSCALE) # 设置为灰度图像
sensor.set_framesize(sensor.QQVGA)     # 160x120 分辨率
sensor.skip_frames(time = 2000)

# ---------------------------
# 初始化 UART1，用于向 MSPM0G3507 发送数据
# ---------------------------
uart = UART(1, baudrate=9600)  # 9600波特率，与MSPM0配置匹配

# --- 亮度自动控制部分 (您的原始代码) ---
# 初始亮度设定
initial_brightness = 500
sensor.set_brightness(initial_brightness)
# 亮度PID控制相关参数
L_set          = 80
PWM_MIN, PWM_MAX = 100, 4000
Kp, Ki, Kd    = 0.0, 1.0, 0.0
alpha          = 0.9
deadband_dark  = 3
deadband_light = 1
delta_pwm_max  = 500
# 初始化状态
filtered_L    = L_set
prev_error    = 0.0
last_error    = 0.0
pwm           = float(initial_brightness)
clock = time.clock()

def update_pwm(current_img):
    global filtered_L, prev_error, last_error, pwm
    raw_L = current_img.get_statistics()[0]
    filtered_L = alpha * raw_L + (1 - alpha) * filtered_L
    L_now      = int(filtered_L)
    if L_now < L_set - 10:
        deadband = deadband_dark
    else:
        deadband = deadband_light
    error = L_set - L_now
    if abs(error) <= deadband:
        prev_error, last_error = last_error, error
        return int(pwm)
    d_error   = error - last_error
    dd_error  = error - 2*last_error + prev_error
    delta_u   = (Kp * d_error) + (Ki * error) + (Kd * dd_error)
    delta_u = max(min(delta_u, delta_pwm_max), -delta_pwm_max)
    pwm = pwm + delta_u
    pwm = max(min(pwm, PWM_MAX), PWM_MIN)
    prev_error, last_error = last_error, error
    return int(pwm)

# 定义二值化的阈值
binary_threshold = (100, 255)

def send_rect_offset_to_mspm0(detection_flag, offset_x, offset_y):
    """
    发送矩形偏移量数据到MSPM0G3507
    数据格式：[0x2B, 0x11, data0, data1, data2, data3, 0x5A, 0x59]
    data0: 检测标志 (0=未检测到A4纸, 1=检测到A4纸)
    data1: X轴偏移量 (有符号，-128到127)
    data2: Y轴偏移量 (有符号，-128到127)
    data3: 保留字节 (设为0)
    """
    # 限制偏移量范围
    if offset_x > 127:
        offset_x = 127
    elif offset_x < -128:
        offset_x = -128
        
    if offset_y > 127:
        offset_y = 127
    elif offset_y < -128:
        offset_y = -128
    
    # 转换为无符号字节
    data0 = detection_flag & 0xFF
    data1 = offset_x & 0xFF  # X轴偏移量
    data2 = offset_y & 0xFF  # Y轴偏移量
    data3 = 0                # 保留字节
    
    # 构造消息
    msg = bytearray([0x2B, 0x11, data0, data1, data2, data3, 0x5A, 0x59])
    uart.write(msg)
    
    # 调试输出
    print(f"Sent: flag={detection_flag}, x_offset={offset_x}, y_offset={offset_y}")

# --- 主循环 ---
while(True):
    clock.tick()

    # 1. 拍摄图像
    img = sensor.snapshot()

    # 2. 根据当前图像亮度计算并设置下一次曝光
    #new_pwm = update_pwm(img)
    #sensor.set_brightness(new_pwm)

    # 3. 对当前图像进行二值化处理
    # !!! 注意：这是一个"破坏性"操作，img 图像从此变成了只有黑(0)白(255)两种颜色的图像
    img.binary([binary_threshold])

    # --- 核心逻辑：寻找并筛选A4纸矩形 ---
    rects = img.find_rects(threshold = 15000)

    # 计算图像中心点
    img_center_x = sensor.width() // 2   # 80
    img_center_y = sensor.height() // 2  # 60
    
    # 绘制图像中心点（绿色十字）
    img.draw_cross(img_center_x, img_center_y, size=8, color=(0, 255, 0), thickness=2)

    # 初始化检测结果
    found_a4_rect = False
    rect_center_x = 0
    rect_center_y = 0

    if rects:
        largest_rect = None
        max_area = 0
        for r in rects:
            area = r.w() * r.h()
            # 如果矩形的宽或高为0，则跳过此次循环，因为它无法计算长宽比
            if r.w() == 0 or r.h() == 0:
                continue

            aspect_ratio = r.w() / r.h()
            if aspect_ratio < 1:
                aspect_ratio = 1 / aspect_ratio

            if area > 400 and (1.2 < aspect_ratio < 1.6):
                if area > max_area:
                    max_area = area
                    largest_rect = r

        if largest_rect:
            # a. 在二值化的图像上画红框
            #    注意：因为图像已经是二值化的，这个"红"框会显示为黑色
            img.draw_rectangle(largest_rect.rect(), color = (255, 0, 0), thickness = 2)

            # b. 【新增】在矩形中心画一个蓝色的十字
            #    计算中心点坐标
            cx = largest_rect.x() + largest_rect.w() // 2
            cy = largest_rect.y() + largest_rect.h() // 2
            #    画十字。同样，这个"蓝"十字也会显示为黑色
            img.draw_cross(cx, cy, size = 10, color = (0, 0, 255), thickness = 2)
            
            # c. 【新增】记录检测结果
            found_a4_rect = True
            rect_center_x = cx
            rect_center_y = cy

    # --- 【新增】计算偏移量并发送数据 ---
    if found_a4_rect:
        # 计算矩形中心与图像中心的偏移量
        offset_x = rect_center_x - img_center_x
        offset_y = rect_center_y - img_center_y
        
        # 发送检测到A4纸的数据
        send_rect_offset_to_mspm0(1, offset_x, offset_y)
        
        # 在图像上显示偏移量信息（可选）
        offset_text = f"Offset:({offset_x},{offset_y})"
        img.draw_string(10, 10, offset_text, color=(255, 255, 255), scale=1)
    else:
        # 未检测到A4纸，发送无效数据
        send_rect_offset_to_mspm0(0, 0, 0)

    # 打印帧率
    print("FPS: %.2f" % clock.fps())
    
    # 延时控制发送频率
    time.sleep_ms(50)  # 50ms发送一次，20Hz
