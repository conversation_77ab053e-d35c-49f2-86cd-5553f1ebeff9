//
// Created by f<PERSON><PERSON><PERSON> on 2024/7/24.
//

#include "timer.h"

void timerA_init()
{
    NVIC_ClearPendingIRQ(TIMG8_INT_IRQn);
    NVIC_EnableIRQ(TIMG8_INT_IRQn);
}

void timerB_init()
{
    NVIC_ClearPendingIRQ(TIMG12_INT_IRQn);
    NVIC_EnableIRQ(TIMG12_INT_IRQn);
}

void TIMG8_IRQHandler(void)
{
    switch( DL_TimerG_getPendingInterrupt(TIMG8) )
    {
        case DL_TIMER_IIDX_ZERO://如果是0溢出中断
            timerA_callback();
            break;
        default://其他的定时器中断
            break;
    }
}

void TIMG12_IRQHandler(void)
{
    switch( DL_TimerG_getPendingInterrupt(TIMG12) )
    {
        case DL_TIMER_IIDX_ZERO://如果是0溢出中断
            timerB_callback();
            break;
        default://其他的定时器中断
            break;
    }
}
