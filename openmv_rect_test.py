# OpenMV矩形检测测试程序
# 用于验证与MSPM0G3507的矩形中心偏移量通信

import time
from machine import UART

# 初始化UART1，波特率9600
uart = UART(1, baudrate=9600)

def send_rect_data(detection_flag, offset_x, offset_y):
    """
    发送矩形检测数据到MSPM0G3507
    数据格式：[0x2B, 0x11, data0, data1, data2, data3, 0x5A, 0x59]
    data0: 检测标志 (0=未检测到, 1=检测到矩形)
    data1: X轴偏移量 (有符号，-128到127)
    data2: Y轴偏移量 (有符号，-128到127)
    data3: 保留字节 (设为0)
    """
    # 限制数据范围
    if offset_x > 127:
        offset_x = 127
    elif offset_x < -128:
        offset_x = -128
        
    if offset_y > 127:
        offset_y = 127
    elif offset_y < -128:
        offset_y = -128
    
    # 构造数据
    data0 = detection_flag & 0xFF
    data1 = offset_x & 0xFF
    data2 = offset_y & 0xFF
    data3 = 0  # 保留字节
    
    # 发送数据帧
    msg = bytearray([0x2B, 0x11, data0, data1, data2, data3, 0x5A, 0x59])
    uart.write(msg)
    
    print(f"Sent: flag={detection_flag}, x_offset={offset_x}, y_offset={offset_y}")

# 测试序列 - 模拟矩形在不同位置的情况
test_cases = [
    (0, 0, 0),        # 未检测到矩形
    (1, 0, 0),        # 检测到矩形，居中
    (1, 30, 0),       # 检测到矩形，右偏30
    (1, -25, 0),      # 检测到矩形，左偏25
    (1, 0, 20),       # 检测到矩形，下偏20
    (1, 0, -15),      # 检测到矩形，上偏15
    (1, 40, 30),      # 检测到矩形，右下偏移
    (1, -35, -20),    # 检测到矩形，左上偏移
    (1, 50, -25),     # 检测到矩形，右上偏移
    (1, -40, 35),     # 检测到矩形，左下偏移
    (0, 0, 0),        # 未检测到矩形
]

print("OpenMV Rectangle Detection Test Program Started")
print("Sending test data to MSPM0G3507...")
print("Data format: detection_flag, x_offset, y_offset")
print("X_offset: negative=left, positive=right")
print("Y_offset: negative=up, positive=down")
print("-" * 50)

test_index = 0
while True:
    # 循环发送测试数据
    detection_flag, offset_x, offset_y = test_cases[test_index]
    send_rect_data(detection_flag, offset_x, offset_y)
    
    # 切换到下一个测试用例
    test_index = (test_index + 1) % len(test_cases)
    
    # 等待2秒
    time.sleep(2)
